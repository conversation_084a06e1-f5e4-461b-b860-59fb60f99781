# AcePokemonCleaner 配置文件
# 精灵定时清理插件配置

# AceBrand 许可证设置
# 请在下方填写您的 AceBrand 许可证密钥，没有许可证插件将无法启动
# 插件将连接到 AceBrand 许可证服务器进行在线验证
# 示例: license-key: "857ff611-618f-40db-8fe2-291b388f0cf0"
# 技术支持 QQ: 337871509
license-key: ""

# 清理间隔（秒）
# 设置为 0 或负数将禁用自动清理
clean-interval: 300

# 启用清理的世界列表
# 只有在这个列表中的世界才会执行精灵清理
enabled-worlds:
  - world
  - world_nether
  - world_the_end

# 排除清理的精灵列表
# 这些精灵永远不会被清理
# 支持特殊标识符和精灵种类名称匹配
excluded-pokemon:
  - "shiny"      # 排除所有闪光精灵
  - "legendary"  # 排除所有传说精灵
  - "mythical"   # 排除所有幻之精灵
  # - "pikachu"  # 排除皮卡丘（种类名称匹配）
  # - "charizard" # 排除喷火龙（种类名称匹配）

# 玩家附近多少格内的精灵不被清理
# 这可以防止清理玩家正在观察或互动的精灵
max-distance: 100.0

# 精灵存在多少秒后才能被清理
# 这可以防止清理刚刚生成的精灵
min-age: 60

# 是否启用详细日志
# 启用后会在控制台输出更多清理信息
enable-logging: false

# 调试模式（显示更多调试信息）
debug-mode: false

# 是否只清理野生精灵
# 启用后不会清理玩家拥有的精灵
clean-only-wild: true

# 每次清理最多清理多少只精灵
# 这可以防止一次性清理过多精灵导致服务器卡顿
max-clean-per-run: 50

# 是否启用清理公告
# 启用后会向所有在线玩家发送清理结果
enable-announcement: true

# 清理公告消息模板
# {pokemonCount} - 清理的精灵数量
# {vanillaCount} - 清理的原版生物数量
# {itemCount} - 清理的掉落物数量
# {duration} - 清理用时（毫秒）
# 支持 Minecraft 颜色代码（如 §6 表示金色）
announcement-message: "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，§e{itemCount} §f个掉落物，用时 §e{duration}秒"

# 最少清理多少只精灵才发送公告
# 设置为 0 表示即使没有清理任何精灵也发送公告
min-announce-count: 1

# 是否启用倒计时公告
# 启用后会在指定时间点提醒玩家距离下次清理的时间
enable-countdown: true

# 倒计时公告消息模板
# {time} - 剩余时间（秒）
# 支持 Minecraft 颜色代码（如 §6 表示金色）
countdown-message: "§6[精灵清理] §f距离下次清理还有 §e{time} §f秒"

# 在剩余多少秒时发送倒计时公告
# 例如：[300, 180, 60, 30, 10] 表示在剩余 5分钟、3分钟、1分钟、30秒、10秒时发送公告
countdown-times:
  - 300  # 5分钟
  - 180  # 3分钟
  - 60   # 1分钟
  - 30   # 30秒
  - 10   # 10秒

# 是否启用 BossBar 倒计时显示
# 启用后会在屏幕上方显示彩色的倒计时进度条
bossbar-flag: false

# BossBar 倒计时配置
# 格式：秒数;消息;样式;颜色
# 样式：SOLID, SEGMENTED_6, SEGMENTED_10, SEGMENTED_12, SEGMENTED_20
# 颜色：PINK, BLUE, RED, GREEN, YELLOW, PURPLE, WHITE
# 可用变量：
#   {count} - 清理的实体总数量
#   {pokemonCount} - 清理的精灵数量
#   {vanillaCount} - 清理的原版生物数量
#   {itemCount} - 清理的掉落物数量
bossbar-message-for-count:
  - "300;§b§l【实体清理】距离下次清理还有 5 分钟;SOLID;BLUE"
  - "180;§6§l【实体清理】距离下次清理还有 3 分钟;SOLID;YELLOW"
  - "120;§6§l【实体清理】距离下次清理还有 2 分钟;SOLID;YELLOW"
  - "60;§e§l【实体清理】距离下次清理还有 1 分钟;SOLID;YELLOW"
  - "45;§c§l【实体清理】距离下次清理还有 45 秒;SEGMENTED_6;RED"
  - "30;§c§l【实体清理】距离下次清理还有 30 秒;SEGMENTED_6;RED"
  - "25;§c§l【实体清理】距离下次清理还有 25 秒;SEGMENTED_10;RED"
  - "20;§c§l【实体清理】距离下次清理还有 20 秒;SEGMENTED_10;RED"
  - "15;§c§l【实体清理】距离下次清理还有 15 秒;SEGMENTED_10;RED"
  - "10;§4§l【实体清理】距离下次清理还有 10 秒;SEGMENTED_12;RED"
  - "9;§4§l【实体清理】距离下次清理还有 9 秒;SEGMENTED_12;RED"
  - "8;§4§l【实体清理】距离下次清理还有 8 秒;SEGMENTED_12;RED"
  - "7;§4§l【实体清理】距离下次清理还有 7 秒;SEGMENTED_12;RED"
  - "6;§4§l【实体清理】距离下次清理还有 6 秒;SEGMENTED_12;RED"
  - "5;§4§l【实体清理】距离下次清理还有 5 秒;SEGMENTED_20;RED"
  - "4;§4§l【实体清理】距离下次清理还有 4 秒;SEGMENTED_20;RED"
  - "3;§4§l【实体清理】距离下次清理还有 3 秒;SEGMENTED_20;RED"
  - "2;§4§l【实体清理】距离下次清理还有 2 秒;SEGMENTED_20;RED"
  - "1;§4§l【实体清理】距离下次清理还有 1 秒;SEGMENTED_20;RED"
  - "0;§a§l【清理完成】精灵: {pokemonCount} 只 | 原版生物: {vanillaCount} 只 | 掉落物: {itemCount} 个 | 总计: {count} 个;SOLID;GREEN"

# 原版生物清理设置
# 是否启用原版生物清理功能
enable-vanilla-mob-cleaning: false

# 排除清理的原版生物列表
# 这些原版生物永远不会被清理
# 支持精确匹配和部分匹配
excluded-vanilla-mobs:
  - "villager"     # 排除村民
  - "iron_golem"   # 排除铁傀儡
  - "cat"          # 排除猫
  - "wolf"         # 排除狼
  - "horse"        # 排除马
  - "donkey"       # 排除驴
  - "mule"         # 排除骡子
  - "llama"        # 排除羊驼
  - "parrot"       # 排除鹦鹉
  - "axolotl"      # 排除美西螈
  - "bee"          # 排除蜜蜂
  # - "zombie"     # 排除僵尸（示例）
  # - "skeleton"   # 排除骷髅（示例）

# 每次清理最多清理多少只原版生物
# 这可以防止一次性清理过多原版生物导致服务器卡顿
vanilla-mob-max-clean-per-run: 30

# 原版生物存在多少秒后才能被清理
# 这可以防止清理刚刚生成的原版生物
vanilla-mob-min-age: 120

# 是否保护被命名牌命名的原版生物
# 启用后，任何使用命名牌命名的原版生物都不会被清理
# 这包括被驯服的动物、特殊命名的生物等
protect-named-vanilla-mobs: true

# 掉落物清理设置
# 是否启用掉落物清理功能
enable-item-cleaning: false

# 排除清理的掉落物列表
# 这些掉落物永远不会被清理
# 支持物品ID和显示名称匹配
excluded-items:
  - "diamond"          # 排除钻石
  - "emerald"          # 排除绿宝石
  - "netherite_ingot"  # 排除下界合金锭
  - "elytra"           # 排除鞘翅
  - "totem_of_undying" # 排除不死图腾
  - "enchanted_book"   # 排除附魔书
  - "nether_star"      # 排除下界之星
  - "dragon_egg"       # 排除龙蛋
  - "beacon"           # 排除信标
  - "shulker_box"      # 排除潜影盒
  # Cobblemon相关物品
  - "poke_ball"        # 排除精灵球
  - "great_ball"       # 排除超级球
  - "ultra_ball"       # 排除高级球
  - "master_ball"      # 排除大师球
  - "rare_candy"       # 排除神奇糖果
  - "exp_candy"        # 排除经验糖果
  # - "cobblestone"    # 排除圆石（示例）

# 每次清理最多清理多少个掉落物
# 这可以防止一次性清理过多掉落物导致服务器卡顿
item-max-clean-per-run: 100

# 掉落物存在多少秒后才能被清理
# 这可以防止清理玩家刚刚丢弃的物品
item-min-age: 300

# 掉落物清理的最大距离
# 玩家附近多少格内的掉落物不被清理
item-max-distance: 50.0

# 是否清理有自定义名称的掉落物
# 启用后会清理玩家重命名的物品
clean-named-items: false

# 是否清理附魔物品
# 启用后会清理带有附魔的物品（不推荐）
clean-enchanted-items: false

# 掉落物价值保护
# 根据物品稀有度自动保护贵重物品
item-rarity-protection:
  # 是否启用稀有度保护
  enabled: true

  # 保护的稀有度等级
  # 稀有度等级说明：
  # COMMON (普通) - 大部分基础物品：石头、木头、铁锭、食物等
  # UNCOMMON (不常见) - 稍有价值的物品：金锭、红石、青金石等
  # RARE (稀有) - 贵重物品：钻石、绿宝石、下界合金锭、鞘翅等
  # EPIC (史诗) - 特殊物品：附魔书、信标、潜影盒、末影珍珠等
  # LEGENDARY (传说) - 超稀有物品：龙蛋、下界之星、不死图腾等
  #
  # Cobblemon物品示例：
  # COMMON: 普通精灵球、树果
  # UNCOMMON: 超级球、进化石
  # RARE: 高级球、神奇糖果
  # EPIC: 大师球、特殊道具
  # LEGENDARY: 传说精灵相关物品
  protected-rarities:
    - "RARE"      # 保护稀有物品（钻石、绿宝石等）
    - "EPIC"      # 保护史诗物品（附魔书、信标等）
    - "LEGENDARY" # 保护传说物品（龙蛋、下界之星等）
    # - "UNCOMMON" # 可选：保护不常见物品（金锭、红石等）
    # - "COMMON"   # 不推荐：保护普通物品（会保护大部分物品）

# 高级设置
advanced:
  # 是否清理被玩家拥有的精灵（不推荐）
  clean-owned: false

  # 是否清理正在战斗的精灵（不推荐）
  clean-battling: false

  # 是否清理繁忙状态的精灵（如进化中，不推荐）
  clean-busy: false

  # 是否清理被骑乘的精灵（不推荐）
  clean-riding: false

  # 是否清理不可捕获的精灵（不推荐）
  clean-uncatchable: false
