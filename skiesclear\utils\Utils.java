/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.gson.JsonDeserializationContext
 *  com.google.gson.JsonDeserializer
 *  com.google.gson.JsonElement
 *  com.google.gson.JsonNull
 *  com.google.gson.JsonParseException
 *  com.google.gson.JsonPrimitive
 *  com.google.gson.JsonSerializationContext
 *  com.google.gson.JsonSerializer
 *  com.mojang.datafixers.util.Pair
 *  com.mojang.serialization.Codec
 *  com.mojang.serialization.DynamicOps
 *  com.mojang.serialization.JsonOps
 *  kotlin.Metadata
 *  kotlin.jvm.internal.Intrinsics
 *  net.kyori.adventure.platform.fabric.FabricServerAudiences
 *  net.kyori.adventure.text.minimessage.MiniMessage
 *  net.minecraft.class_2378
 *  net.minecraft.class_2561
 *  net.minecraft.class_2960
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear.utils;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.mojang.datafixers.util.Pair;
import com.mojang.serialization.Codec;
import com.mojang.serialization.DynamicOps;
import com.mojang.serialization.JsonOps;
import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.config.ConfigManager;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import net.kyori.adventure.platform.fabric.FabricServerAudiences;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.minecraft.class_2378;
import net.minecraft.class_2561;
import net.minecraft.class_2960;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0003\"#$B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ!\u0010\r\u001a\u00020\f2\b\u0010\t\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\f2\b\u0010\t\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0017\u0010\u0011\u001a\u00020\f2\b\u0010\t\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\u0011\u0010\u0010J\u0015\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0017\u001a\u00020\u00168\u0006\u00a2\u0006\f\n\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0019\u0010\u001aR\"\u0010\u001c\u001a\u00020\u001b8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u001c\u0010\u001d\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!\u00a8\u0006%"}, d2={"Lcom/pokeskies/skiesclear/utils/Utils;", "", "<init>", "()V", "", "text", "Lnet/minecraft/class_2561;", "deserializeText", "(Ljava/lang/String;)Lnet/minecraft/class_2561;", "message", "", "bypassCheck", "", "printDebug", "(Ljava/lang/String;Z)V", "printError", "(Ljava/lang/String;)V", "printInfo", "", "time", "getFormattedTime", "(J)Ljava/lang/String;", "Lnet/kyori/adventure/text/minimessage/MiniMessage;", "miniMessage", "Lnet/kyori/adventure/text/minimessage/MiniMessage;", "getMiniMessage", "()Lnet/kyori/adventure/text/minimessage/MiniMessage;", "Ljava/util/regex/Pattern;", "wildcardPattern", "Ljava/util/regex/Pattern;", "getWildcardPattern", "()Ljava/util/regex/Pattern;", "setWildcardPattern", "(Ljava/util/regex/Pattern;)V", "RegistrySerializer", "CodecSerializer", "ResourceLocationSerializer", "SkiesClear"})
public final class Utils {
    @NotNull
    public static final Utils INSTANCE = new Utils();
    @NotNull
    private static final MiniMessage miniMessage;
    @NotNull
    private static Pattern wildcardPattern;

    private Utils() {
    }

    @NotNull
    public final MiniMessage getMiniMessage() {
        return miniMessage;
    }

    @NotNull
    public final Pattern getWildcardPattern() {
        return wildcardPattern;
    }

    public final void setWildcardPattern(@NotNull Pattern pattern) {
        Intrinsics.checkNotNullParameter((Object)pattern, (String)"<set-?>");
        wildcardPattern = pattern;
    }

    @NotNull
    public final class_2561 deserializeText(@NotNull String text) {
        Intrinsics.checkNotNullParameter((Object)text, (String)"text");
        FabricServerAudiences fabricServerAudiences = SkiesClear.Companion.getINSTANCE().getAdventure();
        Intrinsics.checkNotNull((Object)fabricServerAudiences);
        class_2561 class_25612 = fabricServerAudiences.toNative(miniMessage.deserialize((Object)text));
        Intrinsics.checkNotNullExpressionValue((Object)class_25612, (String)"toNative(...)");
        return class_25612;
    }

    public final void printDebug(@Nullable String message, boolean bypassCheck) {
        if (bypassCheck || ConfigManager.Companion.getCONFIG().getDebug()) {
            SkiesClear.Companion.getLOGGER().info("[SkiesClear] DEBUG: " + message);
        }
    }

    public static /* synthetic */ void printDebug$default(Utils utils, String string, boolean bl, int n, Object object) {
        if ((n & 2) != 0) {
            bl = false;
        }
        utils.printDebug(string, bl);
    }

    public final void printError(@Nullable String message) {
        SkiesClear.Companion.getLOGGER().error("[SkiesClear] ERROR: " + message);
    }

    public final void printInfo(@Nullable String message) {
        SkiesClear.Companion.getLOGGER().info("[SkiesClear] " + message);
    }

    @NotNull
    public final String getFormattedTime(long time) {
        if (time <= 0L) {
            return "0";
        }
        List timeFormatted = new ArrayList();
        long days = time / (long)86400;
        long hours = time % (long)86400 / (long)3600;
        long minutes = time % (long)86400 % (long)3600 / (long)60;
        long seconds = time % (long)86400 % (long)3600 % (long)60;
        if (days > 0L) {
            timeFormatted.add(days + "d");
        }
        if (hours > 0L) {
            timeFormatted.add(hours + "h");
        }
        if (minutes > 0L) {
            timeFormatted.add(minutes + "m");
        }
        if (seconds > 0L) {
            timeFormatted.add(seconds + "s");
        }
        String string = String.join((CharSequence)" ", timeFormatted);
        Intrinsics.checkNotNullExpressionValue((Object)string, (String)"join(...)");
        return string;
    }

    static {
        MiniMessage miniMessage = MiniMessage.miniMessage();
        Intrinsics.checkNotNullExpressionValue((Object)miniMessage, (String)"miniMessage(...)");
        Utils.miniMessage = miniMessage;
        Pattern pattern = Pattern.compile("^\\w+:\\*$");
        Intrinsics.checkNotNullExpressionValue((Object)pattern, (String)"compile(...)");
        wildcardPattern = pattern;
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000P\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0086\b\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u00028\u00000\u00022\b\u0012\u0004\u0012\u00028\u00000\u0003B\u0015\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0007J/\u0010\u000e\u001a\u0004\u0018\u00018\u00002\b\u0010\t\u001a\u0004\u0018\u00010\b2\b\u0010\u000b\u001a\u0004\u0018\u00010\n2\b\u0010\r\u001a\u0004\u0018\u00010\fH\u0016\u00a2\u0006\u0004\b\u000e\u0010\u000fJ-\u0010\u0013\u001a\u00020\b2\b\u0010\u0010\u001a\u0004\u0018\u00018\u00002\b\u0010\u0011\u001a\u0004\u0018\u00010\n2\b\u0010\r\u001a\u0004\u0018\u00010\u0012H\u0016\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0016\u0010\u0015\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004H\u00c6\u0003\u00a2\u0006\u0004\b\u0015\u0010\u0016J&\u0010\u0017\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004H\u00c6\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018J\u001a\u0010\u001c\u001a\u00020\u001b2\b\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u00d6\u0003\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u0010\u0010\u001f\u001a\u00020\u001eH\u00d6\u0001\u00a2\u0006\u0004\b\u001f\u0010 J\u0010\u0010\"\u001a\u00020!H\u00d6\u0001\u00a2\u0006\u0004\b\"\u0010#R\u001d\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0005\u0010$\u001a\u0004\b%\u0010\u0016\u00a8\u0006&"}, d2={"Lcom/pokeskies/skiesclear/utils/Utils$CodecSerializer;", "T", "Lcom/google/gson/JsonSerializer;", "Lcom/google/gson/JsonDeserializer;", "Lcom/mojang/serialization/Codec;", "codec", "<init>", "(Lcom/mojang/serialization/Codec;)V", "Lcom/google/gson/JsonElement;", "json", "Ljava/lang/reflect/Type;", "typeOfT", "Lcom/google/gson/JsonDeserializationContext;", "context", "deserialize", "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)Ljava/lang/Object;", "src", "typeOfSrc", "Lcom/google/gson/JsonSerializationContext;", "serialize", "(Ljava/lang/Object;Ljava/lang/reflect/Type;Lcom/google/gson/JsonSerializationContext;)Lcom/google/gson/JsonElement;", "component1", "()Lcom/mojang/serialization/Codec;", "copy", "(Lcom/mojang/serialization/Codec;)Lcom/pokeskies/skiesclear/utils/Utils$CodecSerializer;", "", "other", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "Lcom/mojang/serialization/Codec;", "getCodec", "SkiesClear"})
    public static final class CodecSerializer<T>
    implements JsonSerializer<T>,
    JsonDeserializer<T> {
        @NotNull
        private final Codec<T> codec;

        public CodecSerializer(@NotNull Codec<T> codec) {
            Intrinsics.checkNotNullParameter(codec, (String)"codec");
            this.codec = codec;
        }

        @NotNull
        public final Codec<T> getCodec() {
            return this.codec;
        }

        @Nullable
        public T deserialize(@Nullable JsonElement json, @Nullable Type typeOfT, @Nullable JsonDeserializationContext context) throws JsonParseException {
            Object object;
            try {
                object = ((Pair)this.codec.decode((DynamicOps)JsonOps.INSTANCE, (Object)json).getOrThrow()).getFirst();
            }
            catch (Throwable e) {
                INSTANCE.printError("There was an error while deserializing a Codec: " + this.codec);
                object = null;
            }
            return (T)object;
        }

        @NotNull
        public JsonElement serialize(@Nullable T src, @Nullable Type typeOfSrc, @Nullable JsonSerializationContext context) {
            JsonElement jsonElement;
            try {
                jsonElement = src != null ? (JsonElement)this.codec.encodeStart((DynamicOps)JsonOps.INSTANCE, src).getOrThrow() : (JsonElement)JsonNull.INSTANCE;
            }
            catch (Throwable e) {
                INSTANCE.printError("There was an error while serializing a Codec: " + this.codec);
                jsonElement = (JsonElement)JsonNull.INSTANCE;
            }
            return jsonElement;
        }

        @NotNull
        public final Codec<T> component1() {
            return this.codec;
        }

        @NotNull
        public final CodecSerializer<T> copy(@NotNull Codec<T> codec) {
            Intrinsics.checkNotNullParameter(codec, (String)"codec");
            return new CodecSerializer<T>(codec);
        }

        public static /* synthetic */ CodecSerializer copy$default(CodecSerializer codecSerializer, Codec codec, int n, Object object) {
            if ((n & 1) != 0) {
                codec = codecSerializer.codec;
            }
            return codecSerializer.copy(codec);
        }

        @NotNull
        public String toString() {
            return "CodecSerializer(codec=" + this.codec + ")";
        }

        public int hashCode() {
            return this.codec.hashCode();
        }

        public boolean equals(@Nullable Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof CodecSerializer)) {
                return false;
            }
            CodecSerializer codecSerializer = (CodecSerializer)other;
            return Intrinsics.areEqual(this.codec, codecSerializer.codec);
        }
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000P\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0086\b\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u00028\u00000\u00022\b\u0012\u0004\u0012\u00028\u00000\u0003B\u0015\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0007J)\u0010\u000e\u001a\u0004\u0018\u00018\u00002\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\fH\u0016\u00a2\u0006\u0004\b\u000e\u0010\u000fJ'\u0010\u0013\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00028\u00002\u0006\u0010\u0011\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0016\u0010\u0015\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004H\u00c6\u0003\u00a2\u0006\u0004\b\u0015\u0010\u0016J&\u0010\u0017\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004H\u00c6\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018J\u001a\u0010\u001c\u001a\u00020\u001b2\b\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u00d6\u0003\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u0010\u0010\u001f\u001a\u00020\u001eH\u00d6\u0001\u00a2\u0006\u0004\b\u001f\u0010 J\u0010\u0010\"\u001a\u00020!H\u00d6\u0001\u00a2\u0006\u0004\b\"\u0010#R\u001d\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0005\u0010$\u001a\u0004\b%\u0010\u0016\u00a8\u0006&"}, d2={"Lcom/pokeskies/skiesclear/utils/Utils$RegistrySerializer;", "T", "Lcom/google/gson/JsonSerializer;", "Lcom/google/gson/JsonDeserializer;", "Lnet/minecraft/class_2378;", "registry", "<init>", "(Lnet/minecraft/class_2378;)V", "Lcom/google/gson/JsonElement;", "json", "Ljava/lang/reflect/Type;", "typeOfT", "Lcom/google/gson/JsonDeserializationContext;", "context", "deserialize", "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)Ljava/lang/Object;", "src", "typeOfSrc", "Lcom/google/gson/JsonSerializationContext;", "serialize", "(Ljava/lang/Object;Ljava/lang/reflect/Type;Lcom/google/gson/JsonSerializationContext;)Lcom/google/gson/JsonElement;", "component1", "()Lnet/minecraft/class_2378;", "copy", "(Lnet/minecraft/class_2378;)Lcom/pokeskies/skiesclear/utils/Utils$RegistrySerializer;", "", "other", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "Lnet/minecraft/class_2378;", "getRegistry", "SkiesClear"})
    public static final class RegistrySerializer<T>
    implements JsonSerializer<T>,
    JsonDeserializer<T> {
        @NotNull
        private final class_2378<T> registry;

        public RegistrySerializer(@NotNull class_2378<T> registry) {
            Intrinsics.checkNotNullParameter(registry, (String)"registry");
            this.registry = registry;
        }

        @NotNull
        public final class_2378<T> getRegistry() {
            return this.registry;
        }

        @Nullable
        public T deserialize(@NotNull JsonElement json, @NotNull Type typeOfT, @NotNull JsonDeserializationContext context) throws JsonParseException {
            Object parsed;
            Intrinsics.checkNotNullParameter((Object)json, (String)"json");
            Intrinsics.checkNotNullParameter((Object)typeOfT, (String)"typeOfT");
            Intrinsics.checkNotNullParameter((Object)context, (String)"context");
            Object object = parsed = json.isJsonPrimitive() ? this.registry.method_10223(class_2960.method_12829((String)json.getAsString())) : null;
            if (parsed == null) {
                INSTANCE.printError("There was an error while deserializing a Registry Type: " + this.registry);
            }
            return (T)parsed;
        }

        @NotNull
        public JsonElement serialize(T src, @NotNull Type typeOfSrc, @NotNull JsonSerializationContext context) {
            Intrinsics.checkNotNullParameter((Object)typeOfSrc, (String)"typeOfSrc");
            Intrinsics.checkNotNullParameter((Object)context, (String)"context");
            return (JsonElement)new JsonPrimitive(String.valueOf(this.registry.method_10206(src)));
        }

        @NotNull
        public final class_2378<T> component1() {
            return this.registry;
        }

        @NotNull
        public final RegistrySerializer<T> copy(@NotNull class_2378<T> registry) {
            Intrinsics.checkNotNullParameter(registry, (String)"registry");
            return new RegistrySerializer<T>(registry);
        }

        public static /* synthetic */ RegistrySerializer copy$default(RegistrySerializer registrySerializer, class_2378 class_23782, int n, Object object) {
            if ((n & 1) != 0) {
                class_23782 = registrySerializer.registry;
            }
            return registrySerializer.copy(class_23782);
        }

        @NotNull
        public String toString() {
            return "RegistrySerializer(registry=" + this.registry + ")";
        }

        public int hashCode() {
            return this.registry.hashCode();
        }

        public boolean equals(@Nullable Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof RegistrySerializer)) {
                return false;
            }
            RegistrySerializer registrySerializer = (RegistrySerializer)other;
            return Intrinsics.areEqual(this.registry, registrySerializer.registry);
        }
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u00012\b\u0012\u0004\u0012\u00020\u00020\u0003B\u0007\u00a2\u0006\u0004\b\u0004\u0010\u0005J)\u0010\f\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\b\f\u0010\rJ'\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000f\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\b\u0011\u0010\u0012\u00a8\u0006\u0013"}, d2={"Lcom/pokeskies/skiesclear/utils/Utils$ResourceLocationSerializer;", "Lcom/google/gson/JsonSerializer;", "Lnet/minecraft/class_2960;", "Lcom/google/gson/JsonDeserializer;", "<init>", "()V", "Lcom/google/gson/JsonElement;", "json", "Ljava/lang/reflect/Type;", "typeOfT", "Lcom/google/gson/JsonDeserializationContext;", "context", "deserialize", "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)Lnet/minecraft/class_2960;", "src", "typeOfSrc", "Lcom/google/gson/JsonSerializationContext;", "serialize", "(Lnet/minecraft/class_2960;Ljava/lang/reflect/Type;Lcom/google/gson/JsonSerializationContext;)Lcom/google/gson/JsonElement;", "SkiesClear"})
    public static final class ResourceLocationSerializer
    implements JsonSerializer<class_2960>,
    JsonDeserializer<class_2960> {
        @Nullable
        public class_2960 deserialize(@NotNull JsonElement json, @NotNull Type typeOfT, @NotNull JsonDeserializationContext context) throws JsonParseException {
            Intrinsics.checkNotNullParameter((Object)json, (String)"json");
            Intrinsics.checkNotNullParameter((Object)typeOfT, (String)"typeOfT");
            Intrinsics.checkNotNullParameter((Object)context, (String)"context");
            return class_2960.method_12829((String)json.getAsString());
        }

        @NotNull
        public JsonElement serialize(@NotNull class_2960 src, @NotNull Type typeOfSrc, @NotNull JsonSerializationContext context) {
            Intrinsics.checkNotNullParameter((Object)src, (String)"src");
            Intrinsics.checkNotNullParameter((Object)typeOfSrc, (String)"typeOfSrc");
            Intrinsics.checkNotNullParameter((Object)context, (String)"context");
            return (JsonElement)new JsonPrimitive(src.asString());
        }
    }
}

