# DIM世界清理功能修复说明

## 问题描述
原本插件只能在主世界(world)正常工作，但在DIM和DIM-1等其他维度世界中无法正常清理精灵。

## 问题原因
1. **配置文件缺少DIM世界**: 源配置文件中的 `enabled-worlds` 列表只包含标准世界名称，没有包含DIM格式的世界名称
2. **世界名称映射缺失**: 代码直接使用配置中的世界名称调用 `Bukkit.getWorld()`，但DIM格式的世界名称可能需要映射到实际的世界名称

## 修复内容

### 1. 添加世界名称映射功能
在 `PokemonCleaner.kt` 中添加了 `getWorldByName()` 方法，支持以下映射：

- **DIM1, DIM-1** → `world_nether` (下界)
- **DIM0** → `world` (主世界)  
- **DIM2** → `world_the_end` (末地)
- **反向映射**: 标准世界名称也可以映射到DIM格式

### 2. 更新配置文件
在 `src/main/resources/config.yml` 中添加了DIM格式的世界名称：

```yaml
enabled-worlds:
  - world
  - world_nether
  - world_the_end
  - DIM1          # 下界（DIM格式）
  - DIM-1         # 末地（DIM格式）
```

### 3. 添加世界调试命令
新增 `/pokemoncleaner worlds` 命令，用于：
- 列出服务器中所有可用的世界
- 显示配置中启用的世界状态
- 提供DIM格式映射提示

## 使用方法

### 1. 检查世界状态
```
/pokemoncleaner worlds
```
这个命令会显示：
- 服务器中所有可用的世界及其信息
- 配置中启用的世界是否能正确找到
- DIM格式映射提示

### 2. 配置世界清理
在 `config.yml` 中的 `enabled-worlds` 部分添加需要清理的世界名称：

```yaml
enabled-worlds:
  - world           # 主世界
  - world_nether    # 下界
  - world_the_end   # 末地
  - DIM1            # DIM格式下界
  - DIM-1           # DIM格式末地
  - DIM0            # DIM格式主世界
  - 你的自定义世界名称
```

### 3. 测试清理功能
```
/pokemoncleaner clean
```
手动执行一次清理，检查是否在所有配置的世界中正常工作。

## 技术细节

### 世界名称映射逻辑
```kotlin
private fun getWorldByName(worldName: String): World? {
    // 1. 首先尝试直接获取
    var world = Bukkit.getWorld(worldName)
    if (world != null) return world
    
    // 2. 尝试DIM格式映射
    val mappedNames = when (worldName.uppercase()) {
        "DIM1", "DIM-1" -> listOf("world_nether", "DIM-1", "DIM1")
        "DIM0" -> listOf("world", "DIM0")
        "DIM2" -> listOf("world_the_end", "DIM1", "DIM2")
        // ... 更多映射
    }
    
    // 3. 尝试所有可能的映射名称
    for (mappedName in mappedNames) {
        world = Bukkit.getWorld(mappedName)
        if (world != null) return world
    }
    
    return null
}
```

### 调试信息
启用日志记录后，插件会输出：
- 世界名称映射成功的信息
- 无法找到世界时的可用世界列表
- 每个世界的清理结果

## 验证修复

### 步骤1: 重新编译和部署
1. 重新编译插件
2. 重启服务器或重载插件

### 步骤2: 检查世界状态
使用新增的命令检查世界映射是否正常：
```
/pokemoncleaner worlds
```

这个命令会显示：
- 所有可用世界的详细信息
- 配置中启用的世界状态（找到/未找到/映射）
- DIM格式映射提示

### 步骤3: 测试清理功能
```
/pokemoncleaner clean
```
手动执行一次清理，观察：
- 是否在所有配置的世界中都执行了清理
- 日志中是否显示世界映射成功的信息
- 清理结果是否包含所有世界

### 步骤4: 启用详细日志
在 `config.yml` 中设置：
```yaml
enable-logging: true
debug-mode: true
```

重载配置后再次测试，检查日志输出：
- 世界名称映射成功的信息
- 每个世界的清理过程
- 任何错误或警告信息

### 步骤5: 验证特定世界
如果你的服务器使用特定的DIM格式世界名称：
1. 使用 `/pokemoncleaner worlds` 查看实际的世界名称
2. 在配置文件中添加对应的世界名称
3. 重载配置并测试

## 预期结果
修复成功后，你应该看到：
- ✅ 所有配置的世界都显示为"找到"或"映射"状态
- ✅ 清理功能在所有世界中正常工作
- ✅ 日志中显示世界映射成功的信息
- ✅ 清理统计包含所有世界的结果

## 故障排除

### 如果某个世界仍然显示"未找到"
1. 检查世界名称拼写是否正确
2. 确认该世界确实存在于服务器中
3. 尝试使用 `/pokemoncleaner worlds` 中显示的确切世界名称

### 如果清理功能仍然不工作
1. 检查是否有权限问题
2. 确认配置文件格式正确
3. 查看服务器日志中的错误信息
4. 尝试启用调试模式获取更多信息

## 注意事项
- 确保配置文件中的世界名称与服务器中的实际世界名称匹配
- 如果使用自定义世界名称，请直接添加到 `enabled-worlds` 列表中
- 建议先使用 `worlds` 命令确认世界名称，再进行配置
- DIM格式映射是双向的，既支持DIM→标准名称，也支持标准名称→DIM
