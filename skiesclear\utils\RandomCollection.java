/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.jvm.JvmOverloads
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.utils;

import java.util.Map;
import java.util.NavigableMap;
import java.util.Random;
import java.util.TreeMap;
import kotlin.Metadata;
import kotlin.jvm.JvmOverloads;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000:\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B\u0013\b\u0007\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J#\u0010\n\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00028\u0000\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0017\u0010\u000e\u001a\u00028\u00002\b\b\u0002\u0010\r\u001a\u00020\f\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\r\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\b\u0011\u0010\u0012J\u000f\u0010\u0014\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\b\u0014\u0010\u0015R\u0014\u0010\u0004\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\u0004\u0010\u0016R \u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00028\u00000\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\u0018\u0010\u0019R\u0016\u0010\u001a\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u001a\u0010\u001b\u00a8\u0006\u001c"}, d2={"Lcom/pokeskies/skiesclear/utils/RandomCollection;", "E", "", "Ljava/util/Random;", "random", "<init>", "(Ljava/util/Random;)V", "", "weight", "result", "add", "(DLjava/lang/Object;)Lcom/pokeskies/skiesclear/utils/RandomCollection;", "", "remove", "next", "(Z)Ljava/lang/Object;", "", "size", "()I", "", "toString", "()Ljava/lang/String;", "Ljava/util/Random;", "Ljava/util/NavigableMap;", "map", "Ljava/util/NavigableMap;", "total", "D", "SkiesClear"})
public final class RandomCollection<E> {
    @NotNull
    private final Random random;
    @NotNull
    private final NavigableMap<Double, E> map;
    private double total;

    @JvmOverloads
    public RandomCollection(@NotNull Random random) {
        Intrinsics.checkNotNullParameter((Object)random, (String)"random");
        this.random = random;
        this.map = new TreeMap();
    }

    public /* synthetic */ RandomCollection(Random random, int n, DefaultConstructorMarker defaultConstructorMarker) {
        if ((n & 1) != 0) {
            random = new Random();
        }
        this(random);
    }

    @NotNull
    public final RandomCollection<E> add(double weight, E result) {
        if (weight <= 0.0) {
            return this;
        }
        this.total += weight;
        ((Map)this.map).put(this.total, result);
        return this;
    }

    public final E next(boolean remove) {
        double value = this.random.nextDouble() * this.total;
        Map.Entry<Double, E> entry = this.map.higherEntry(value);
        if (remove) {
            Double d = entry.getKey();
            Intrinsics.checkNotNullExpressionValue((Object)d, (String)"<get-key>(...)");
            this.total -= ((Number)d).doubleValue();
            this.map.remove(entry.getKey());
        }
        return entry.getValue();
    }

    public static /* synthetic */ Object next$default(RandomCollection randomCollection, boolean bl, int n, Object object) {
        if ((n & 1) != 0) {
            bl = false;
        }
        return randomCollection.next(bl);
    }

    public final int size() {
        return this.map.size();
    }

    @NotNull
    public String toString() {
        return "RandomCollection(random=" + this.random + ", map=" + this.map + ", total=" + this.total + ")";
    }

    @JvmOverloads
    public RandomCollection() {
        this(null, 1, null);
    }
}

