{"md5": "29173dfac1fd50048280676ce20a1eff", "sha2": "3b6c4e94948f51061b506d1fe0fd668e21566ee7", "sha256": "7e762d824fc1d04b19b8a2ea57024c67ef1893ae8081af0532f87af3e47432eb", "contents": {"classes": {"classes/jdk/random/L128X1024MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L128X1024MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJJJJJJJJJJJJJJJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "N", "dsc": "I", "val": 16}, {"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "ML", "dsc": "J", "val": -3024805186288043011}, {"acc": 18, "nme": "ah", "dsc": "J"}, {"acc": 18, "nme": "al", "dsc": "J"}, {"acc": 2, "nme": "sh", "dsc": "J"}, {"acc": 2, "nme": "sl", "dsc": "J"}, {"acc": 18, "nme": "x", "dsc": "[J"}, {"acc": 2, "nme": "p", "dsc": "I"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L128X1024MixRandom", "group", "LXM", "i", 1024, "j", 1, "k", 128, "equidistribution", 1]}]}, "classes/jdk/random/Xoroshiro128PlusPlus.class": {"ver": 65, "acc": 49, "nme": "jdk/random/Xoroshiro128PlusPlus", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "copy", "acc": 1, "dsc": "()Ljdk/random/Xoroshiro128PlusPlus;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "jumpDistance", "acc": 1, "dsc": "()D"}, {"nme": "leapDistance", "acc": 1, "dsc": "()D"}, {"nme": "jump", "acc": 1, "dsc": "()V"}, {"nme": "leap", "acc": 1, "dsc": "()V"}, {"nme": "jumpAlgorithm", "acc": 2, "dsc": "([J)V"}, {"nme": "copy", "acc": 4161, "dsc": "()Ljava/util/random/RandomGenerator$LeapableGenerator;"}, {"nme": "copy", "acc": 4161, "dsc": "()Ljava/util/random/RandomGenerator$JumpableGenerator;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GROUP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<PERSON><PERSON><PERSON><PERSON>"}, {"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}, {"acc": 26, "nme": "JUMP_TABLE", "dsc": "[J"}, {"acc": 26, "nme": "LEAP_TABLE", "dsc": "[J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "Xoroshiro128PlusPlus", "group", "<PERSON><PERSON><PERSON><PERSON>", "i", 128, "j", 1, "k", 0, "equidistribution", 1]}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/random/L64X128StarStarRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L64X128StarStarRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "M", "dsc": "J", "val": -3372029247567499371}, {"acc": 18, "nme": "a", "dsc": "J"}, {"acc": 2, "nme": "s", "dsc": "J"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L64X128StarStarRandom", "group", "LXM", "i", 128, "j", 1, "k", 64, "equidistribution", 2]}]}, "classes/jdk/random/L64X1024MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L64X1024MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJJJJJJJJJJJJJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "N", "dsc": "I", "val": 16}, {"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "M", "dsc": "J", "val": -3372029247567499371}, {"acc": 18, "nme": "a", "dsc": "J"}, {"acc": 2, "nme": "s", "dsc": "J"}, {"acc": 18, "nme": "x", "dsc": "[J"}, {"acc": 2, "nme": "p", "dsc": "I"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L64X1024MixRandom", "group", "LXM", "i", 1024, "j", 1, "k", 64, "equidistribution", 16]}]}, "classes/jdk/random/L32X64MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L32X64MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(IIII)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextInt", "acc": 1, "dsc": "()I"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "M", "dsc": "I", "val": -1380669139}, {"acc": 18, "nme": "a", "dsc": "I"}, {"acc": 2, "nme": "s", "dsc": "I"}, {"acc": 2, "nme": "x0", "dsc": "I"}, {"acc": 2, "nme": "x1", "dsc": "I"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L32X64MixRandom", "group", "LXM", "i", 64, "j", 1, "k", 32, "equidistribution", 1]}]}, "classes/jdk/random/L64X128MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L64X128MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "M", "dsc": "J", "val": -3372029247567499371}, {"acc": 18, "nme": "a", "dsc": "J"}, {"acc": 2, "nme": "s", "dsc": "J"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L64X128MixRandom", "group", "LXM", "i", 128, "j", 1, "k", 64, "equidistribution", 2]}]}, "classes/jdk/random/L64X256MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L64X256MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "M", "dsc": "J", "val": -3372029247567499371}, {"acc": 18, "nme": "a", "dsc": "J"}, {"acc": 2, "nme": "s", "dsc": "J"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}, {"acc": 2, "nme": "x2", "dsc": "J"}, {"acc": 2, "nme": "x3", "dsc": "J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L64X256MixRandom", "group", "LXM", "i", 256, "j", 1, "k", 64, "equidistribution", 4]}]}, "classes/jdk/random/Xoshiro256PlusPlus.class": {"ver": 65, "acc": 49, "nme": "jdk/random/Xoshiro256PlusPlus", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "copy", "acc": 1, "dsc": "()Ljdk/random/Xoshiro256PlusPlus;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "jumpDistance", "acc": 1, "dsc": "()D"}, {"nme": "leapDistance", "acc": 1, "dsc": "()D"}, {"nme": "jump", "acc": 1, "dsc": "()V"}, {"nme": "leap", "acc": 1, "dsc": "()V"}, {"nme": "jumpAlgorithm", "acc": 2, "dsc": "([J)V"}, {"nme": "copy", "acc": 4161, "dsc": "()Ljava/util/random/RandomGenerator$LeapableGenerator;"}, {"nme": "copy", "acc": 4161, "dsc": "()Ljava/util/random/RandomGenerator$JumpableGenerator;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEFAULT_GEN", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}, {"acc": 2, "nme": "x2", "dsc": "J"}, {"acc": 2, "nme": "x3", "dsc": "J"}, {"acc": 26, "nme": "JUMP_TABLE", "dsc": "[J"}, {"acc": 26, "nme": "LEAP_TABLE", "dsc": "[J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "Xoshiro256PlusPlus", "group", "<PERSON><PERSON><PERSON>", "i", 256, "j", 1, "k", 0, "equidistribution", 3]}]}, "classes/jdk/random/L128X128MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L128X128MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "ML", "dsc": "J", "val": -3024805186288043011}, {"acc": 18, "nme": "ah", "dsc": "J"}, {"acc": 18, "nme": "al", "dsc": "J"}, {"acc": 2, "nme": "sh", "dsc": "J"}, {"acc": 2, "nme": "sl", "dsc": "J"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L128X128MixRandom", "group", "LXM", "i", 128, "j", 1, "k", 128, "equidistribution", 1]}]}, "classes/jdk/random/L128X256MixRandom.class": {"ver": 65, "acc": 49, "nme": "jdk/random/L128X256MixRandom", "super": "jdk/internal/util/random/RandomSupport$AbstractSplittableWithBrineGenerator", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(JJJJJJJJ)V"}, {"nme": "<init>", "acc": 1, "dsc": "(J)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V"}, {"nme": "split", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/random/RandomGenerator$SplittableGenerator;J)Ljava/util/random/RandomGenerator$SplittableGenerator;"}, {"nme": "nextLong", "acc": 1, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultGen", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "EQUIDISTRIBUTION", "dsc": "I", "val": 1}, {"acc": 26, "nme": "ML", "dsc": "J", "val": -3024805186288043011}, {"acc": 18, "nme": "ah", "dsc": "J"}, {"acc": 18, "nme": "al", "dsc": "J"}, {"acc": 2, "nme": "sh", "dsc": "J"}, {"acc": 2, "nme": "sl", "dsc": "J"}, {"acc": 2, "nme": "x0", "dsc": "J"}, {"acc": 2, "nme": "x1", "dsc": "J"}, {"acc": 2, "nme": "x2", "dsc": "J"}, {"acc": 2, "nme": "x3", "dsc": "J"}], "vanns": [{"dsc": "Ljdk/internal/util/random/RandomSupport$RandomGeneratorProperties;", "vals": ["name", "L128X256MixRandom", "group", "LXM", "i", 256, "j", 1, "k", 128, "equidistribution", 1]}]}}}}