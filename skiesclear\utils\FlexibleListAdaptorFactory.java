/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.gson.Gson
 *  com.google.gson.TypeAdapter
 *  com.google.gson.TypeAdapterFactory
 *  com.google.gson.reflect.TypeToken
 *  com.google.gson.stream.JsonReader
 *  com.google.gson.stream.JsonToken
 *  com.google.gson.stream.JsonWriter
 *  com.google.gson.stream.MalformedJsonException
 *  kotlin.Metadata
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear.utils;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import com.google.gson.stream.MalformedJsonException;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u0000 \r*\u0004\b\u0000\u0010\u00012\u00020\u0002:\u0001\rB\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J3\u0010\u000b\u001a\n\u0012\u0004\u0012\u00028\u0001\u0018\u00010\n\"\u0004\b\u0001\u0010\u00052\u0006\u0010\u0007\u001a\u00020\u00062\f\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00010\bH\u0016\u00a2\u0006\u0004\b\u000b\u0010\f\u00a8\u0006\u000e"}, d2={"Lcom/pokeskies/skiesclear/utils/FlexibleListAdaptorFactory;", "E", "Lcom/google/gson/TypeAdapterFactory;", "<init>", "()V", "T", "Lcom/google/gson/Gson;", "gson", "Lcom/google/gson/reflect/TypeToken;", "typeToken", "Lcom/google/gson/TypeAdapter;", "create", "(Lcom/google/gson/Gson;Lcom/google/gson/reflect/TypeToken;)Lcom/google/gson/TypeAdapter;", "Companion", "SkiesClear"})
public final class FlexibleListAdaptorFactory<E>
implements TypeAdapterFactory {
    @NotNull
    public static final Companion Companion = new Companion(null);

    private FlexibleListAdaptorFactory() {
    }

    @Nullable
    public <T> TypeAdapter<T> create(@NotNull Gson gson, @NotNull TypeToken<T> typeToken) {
        Intrinsics.checkNotNullParameter((Object)gson, (String)"gson");
        Intrinsics.checkNotNullParameter(typeToken, (String)"typeToken");
        if (!List.class.isAssignableFrom(typeToken.getRawType())) {
            return null;
        }
        Type type = typeToken.getType();
        Intrinsics.checkNotNullExpressionValue((Object)type, (String)"getType(...)");
        Type elementType = FlexibleListAdaptorFactory.Companion.resolveTypeArgument(type);
        TypeAdapter typeAdapter = gson.getAdapter(TypeToken.get((Type)elementType));
        Intrinsics.checkNotNull((Object)typeAdapter, (String)"null cannot be cast to non-null type com.google.gson.TypeAdapter<E of com.pokeskies.skiesclear.utils.FlexibleListAdaptorFactory>");
        TypeAdapter elementTypeAdapter = typeAdapter;
        TypeAdapter typeAdapter2 = new Companion.ListLikeAdaptorFactory(elementTypeAdapter).nullSafe();
        Intrinsics.checkNotNull((Object)typeAdapter2, (String)"null cannot be cast to non-null type com.google.gson.TypeAdapter<T of com.pokeskies.skiesclear.utils.FlexibleListAdaptorFactory.create>");
        return typeAdapter2;
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001:\u0001\bB\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0017\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007\u00a8\u0006\t"}, d2={"Lcom/pokeskies/skiesclear/utils/FlexibleListAdaptorFactory$Companion;", "", "<init>", "()V", "Ljava/lang/reflect/Type;", "type", "resolveTypeArgument", "(Ljava/lang/reflect/Type;)Ljava/lang/reflect/Type;", "ListLikeAdaptorFactory", "SkiesClear"})
    public static final class Companion {
        private Companion() {
        }

        private final Type resolveTypeArgument(Type type) {
            if (!(type instanceof ParameterizedType)) {
                return (Type)((Object)Object.class);
            }
            ParameterizedType parameterizedType = (ParameterizedType)type;
            Type type2 = parameterizedType.getActualTypeArguments()[0];
            Intrinsics.checkNotNullExpressionValue((Object)type2, (String)"get(...)");
            return type2;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }

        @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000*\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0002\u0018\u0000*\u0004\b\u0001\u0010\u00012\u0010\u0012\f\u0012\n\u0012\u0004\u0012\u00028\u0001\u0018\u00010\u00030\u0002B\u0015\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00028\u00010\u0002\u00a2\u0006\u0004\b\u0005\u0010\u0006J)\u0010\u000b\u001a\u00020\n2\b\u0010\b\u001a\u0004\u0018\u00010\u00072\u000e\u0010\t\u001a\n\u0012\u0004\u0012\u00028\u0001\u0018\u00010\u0003H\u0016\u00a2\u0006\u0004\b\u000b\u0010\fJ\u001d\u0010\u000f\u001a\b\u0012\u0004\u0012\u00028\u00010\u00032\u0006\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\b\u000f\u0010\u0010R\u001a\u0010\u0004\u001a\b\u0012\u0004\u0012\u00028\u00010\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\u0004\u0010\u0011\u00a8\u0006\u0012"}, d2={"Lcom/pokeskies/skiesclear/utils/FlexibleListAdaptorFactory$Companion$ListLikeAdaptorFactory;", "E", "Lcom/google/gson/TypeAdapter;", "", "elementTypeAdapter", "<init>", "(Lcom/google/gson/TypeAdapter;)V", "Lcom/google/gson/stream/JsonWriter;", "out", "list", "", "write", "(Lcom/google/gson/stream/JsonWriter;Ljava/util/List;)V", "Lcom/google/gson/stream/JsonReader;", "in", "read", "(Lcom/google/gson/stream/JsonReader;)Ljava/util/List;", "Lcom/google/gson/TypeAdapter;", "SkiesClear"})
        private static final class ListLikeAdaptorFactory<E>
        extends TypeAdapter<List<? extends E>> {
            @NotNull
            private final TypeAdapter<E> elementTypeAdapter;

            public ListLikeAdaptorFactory(@NotNull TypeAdapter<E> elementTypeAdapter) {
                Intrinsics.checkNotNullParameter(elementTypeAdapter, (String)"elementTypeAdapter");
                this.elementTypeAdapter = elementTypeAdapter;
            }

            public void write(@Nullable JsonWriter out, @Nullable List<? extends E> list) {
                throw new UnsupportedOperationException();
            }

            @NotNull
            public List<E> read(@NotNull JsonReader in) throws IOException {
                Intrinsics.checkNotNullParameter((Object)in, (String)"in");
                List list = new ArrayList();
                JsonToken jsonToken = in.peek();
                Intrinsics.checkNotNullExpressionValue((Object)jsonToken, (String)"peek(...)");
                JsonToken token = jsonToken;
                switch (WhenMappings.$EnumSwitchMapping$0[token.ordinal()]) {
                    case 1: {
                        in.beginArray();
                        while (in.hasNext()) {
                            list.add(this.elementTypeAdapter.read(in));
                        }
                        in.endArray();
                        break;
                    }
                    case 2: 
                    case 3: 
                    case 4: 
                    case 5: {
                        list.add(this.elementTypeAdapter.read(in));
                        break;
                    }
                    case 6: {
                        throw new AssertionError((Object)"Must never happen: check if the type adapter configured with .nullSafe()");
                    }
                    case 7: 
                    case 8: 
                    case 9: 
                    case 10: {
                        throw new MalformedJsonException("Unexpected token: " + token);
                    }
                    default: {
                        throw new AssertionError((Object)("Must never happen: " + token));
                    }
                }
                return list;
            }

            @Metadata(mv={2, 0, 0}, k=3, xi=48)
            public final class WhenMappings {
                public static final /* synthetic */ int[] $EnumSwitchMapping$0;

                static {
                    int[] nArray = new int[JsonToken.values().length];
                    try {
                        nArray[JsonToken.BEGIN_ARRAY.ordinal()] = 1;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.BEGIN_OBJECT.ordinal()] = 2;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.STRING.ordinal()] = 3;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.NUMBER.ordinal()] = 4;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.BOOLEAN.ordinal()] = 5;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.NULL.ordinal()] = 6;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.NAME.ordinal()] = 7;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.END_ARRAY.ordinal()] = 8;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.END_OBJECT.ordinal()] = 9;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    try {
                        nArray[JsonToken.END_DOCUMENT.ordinal()] = 10;
                    }
                    catch (NoSuchFieldError noSuchFieldError) {
                        // empty catch block
                    }
                    $EnumSwitchMapping$0 = nArray;
                }
            }
        }
    }
}

