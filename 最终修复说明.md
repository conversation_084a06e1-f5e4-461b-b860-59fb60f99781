# 精灵清理排除功能最终修复说明

## 修复概述

基于SkiesClear mod的正确实现，完全重写了精灵清理插件的排除功能，使用官方的Cobblemon API而不是反射。

## 主要改进

### 1. 使用正确的Cobblemon API

```kotlin
// 导入正确的Cobblemon类
import com.cobblemon.mod.common.entity.pokemon.PokemonEntity
import com.cobblemon.mod.common.pokemon.Pokemon

// 正确的实体检查
entity is PokemonEntity

// 正确的属性检查
val pokemon = entity.pokemon
pokemon.shiny              // 检查闪光
pokemon.isLegendary()      // 检查传说
pokemon.isPlayerOwned()    // 检查是否被玩家拥有
entity.isBattling()        // 检查是否在战斗
entity.isBusy()           // 检查是否繁忙
entity.isUncatchable()    // 检查是否不可捕获
```

### 2. 重新设计配置结构

#### 基本排除列表 (excluded-pokemon)
```yaml
excluded-pokemon:
  - "shiny"      # 排除所有闪光精灵
  - "legendary"  # 排除所有传说精灵
  - "mythical"   # 排除所有幻之精灵
  - "pikachu"    # 排除特定种类（种类名称匹配）
```

#### 高级设置 (advanced)
```yaml
advanced:
  clean-owned: false        # 是否清理被玩家拥有的精灵
  clean-battling: false     # 是否清理正在战斗的精灵
  clean-busy: false         # 是否清理繁忙状态的精灵
  clean-riding: false       # 是否清理被骑乘的精灵
  clean-uncatchable: false  # 是否清理不可捕获的精灵
```

### 3. 完整的排除检查逻辑

现在的排除检查按以下顺序进行：

1. **基本名称匹配**：检查实体名称和种类名称
2. **闪光精灵**：使用 `pokemon.shiny` API检查
3. **传说精灵**：使用 `pokemon.isLegendary()` API检查
4. **幻之精灵**：基于种类名称的已知列表检查
5. **高级设置检查**：
   - 被拥有：`!cleanOwned && pokemon.isPlayerOwned()`
   - 战斗中：`!cleanBattling && entity.isBattling()`
   - 繁忙：`!cleanBusy && entity.isBusy()`
   - 不可捕获：`!cleanUncatchable && entity.isUncatchable()`

## 使用方法

### 1. 基本配置

```yaml
# 排除闪光、传说、幻之精灵
excluded-pokemon:
  - "shiny"
  - "legendary"
  - "mythical"

# 高级设置保持默认（不清理特殊状态精灵）
advanced:
  clean-owned: false
  clean-battling: false
  clean-busy: false
  clean-riding: false
  clean-uncatchable: false
```

### 2. 测试和调试

```bash
# 重载配置
/pokemoncleaner reload

# 调试附近精灵
/pokemoncleaner debug

# 手动清理测试
/pokemoncleaner clean
```

### 3. 调试输出示例

```
发现 3 只精灵:
1. [排除] cobblemon:pikachu [闪光, 已拥有]
2. [可清理] cobblemon:rattata
3. [排除] cobblemon:mewtwo [传说]

详细信息:
  实体类型: POKEMON
  实体类名: PokemonEntity
  是否为Cobblemon精灵: true
  精灵种类: cobblemon:pikachu
  是否闪光: true
  是否传说: false
  是否被玩家拥有: true
  是否在战斗: false
  是否繁忙: false
  是否被排除: true
  
  高级设置:
    清理被拥有精灵: false
    清理战斗中精灵: false
    清理繁忙精灵: false
    清理不可捕获精灵: false
  
  高级设置状态检查:
    被拥有: true (清理=false)
    战斗中: false (清理=false)
    繁忙: false (清理=false)
    不可捕获: false (清理=false)
```

## 预期效果

修复后的插件应该能够：

1. ✅ **正确识别Cobblemon精灵**：使用 `entity is PokemonEntity`
2. ✅ **正确检查闪光状态**：使用 `pokemon.shiny` API
3. ✅ **正确检查传说状态**：使用 `pokemon.isLegendary()` API
4. ✅ **正确检查拥有状态**：使用 `pokemon.isPlayerOwned()` API
5. ✅ **正确检查战斗状态**：使用 `entity.isBattling()` API
6. ✅ **正确检查繁忙状态**：使用 `entity.isBusy()` API
7. ✅ **提供详细调试信息**：帮助诊断问题

## 故障排除

如果排除功能仍然不工作：

1. **检查Cobblemon版本兼容性**：确保API调用正确
2. **使用调试命令**：`/pokemoncleaner debug` 查看详细信息
3. **检查配置格式**：确保YAML格式正确
4. **查看日志**：启用 `debug-mode: true` 查看详细日志

## 技术细节

- **无反射**：完全基于公开API，避免混淆问题
- **高性能**：直接API调用，无需字符串匹配
- **可扩展**：易于添加新的排除条件
- **向后兼容**：保持原有配置结构的兼容性

这个修复版本应该能够完全解决闪光精灵被误清理的问题。
