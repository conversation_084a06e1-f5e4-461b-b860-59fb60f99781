/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.gson.Gson
 *  com.google.gson.GsonBuilder
 *  com.google.gson.stream.JsonReader
 *  com.mojang.brigadier.CommandDispatcher
 *  kotlin.Metadata
 *  kotlin.Unit
 *  kotlin.io.CloseableKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  net.fabricmc.api.ModInitializer
 *  net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback
 *  net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents
 *  net.fabricmc.loader.api.FabricLoader
 *  net.kyori.adventure.platform.fabric.FabricServerAudiences
 *  net.minecraft.class_2168
 *  net.minecraft.class_2170$class_5364
 *  net.minecraft.class_2378
 *  net.minecraft.class_2960
 *  net.minecraft.class_3414
 *  net.minecraft.class_7157
 *  net.minecraft.class_7923
 *  net.minecraft.server.MinecraftServer
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.stream.JsonReader;
import com.mojang.brigadier.CommandDispatcher;
import com.pokeskies.skiesclear.ClearManager;
import com.pokeskies.skiesclear.commands.BaseCommand;
import com.pokeskies.skiesclear.config.ConfigManager;
import com.pokeskies.skiesclear.utils.Utils;
import java.io.Closeable;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.attribute.FileAttribute;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.fabricmc.loader.api.FabricLoader;
import net.kyori.adventure.platform.fabric.FabricServerAudiences;
import net.minecraft.class_2168;
import net.minecraft.class_2170;
import net.minecraft.class_2378;
import net.minecraft.class_2960;
import net.minecraft.class_3414;
import net.minecraft.class_7157;
import net.minecraft.class_7923;
import net.minecraft.server.MinecraftServer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 @2\u00020\u0001:\u0001@B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\b\u0005\u0010\u0003J\r\u0010\u0006\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0003J1\u0010\u000e\u001a\u00028\u0000\"\b\b\u0000\u0010\b*\u00020\u00072\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00028\u00002\b\b\u0002\u0010\r\u001a\u00020\f\u00a2\u0006\u0004\b\u000e\u0010\u000fJ#\u0010\u0011\u001a\u00020\f\"\u0004\b\u0000\u0010\b2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00028\u0000\u00a2\u0006\u0004\b\u0011\u0010\u0012R\"\u0010\u0014\u001a\u00020\u00138\u0006@\u0006X\u0086.\u00a2\u0006\u0012\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019R\"\u0010\u001b\u001a\u00020\u001a8\u0006@\u0006X\u0086.\u00a2\u0006\u0012\n\u0004\b\u001b\u0010\u001c\u001a\u0004\b\u001d\u0010\u001e\"\u0004\b\u001f\u0010 R$\u0010\"\u001a\u0004\u0018\u00010!8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\"\u0010#\u001a\u0004\b$\u0010%\"\u0004\b&\u0010'R$\u0010)\u001a\u0004\u0018\u00010(8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b)\u0010*\u001a\u0004\b+\u0010,\"\u0004\b-\u0010.R\"\u00100\u001a\u00020/8\u0006@\u0006X\u0086.\u00a2\u0006\u0012\n\u0004\b0\u00101\u001a\u0004\b2\u00103\"\u0004\b4\u00105R\"\u00107\u001a\u0002068\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b7\u00108\u001a\u0004\b9\u0010:\"\u0004\b;\u0010<R\"\u0010=\u001a\u0002068\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b=\u00108\u001a\u0004\b>\u0010:\"\u0004\b?\u0010<\u00a8\u0006A"}, d2={"Lcom/pokeskies/skiesclear/SkiesClear;", "Lnet/fabricmc/api/ModInitializer;", "<init>", "()V", "", "onInitialize", "reload", "", "T", "", "filename", "default", "", "create", "loadFile", "(Ljava/lang/String;Ljava/lang/Object;Z)Ljava/lang/Object;", "object", "saveFile", "(Ljava/lang/String;Ljava/lang/Object;)Z", "Ljava/io/File;", "configDir", "Ljava/io/File;", "getConfigDir", "()Ljava/io/File;", "setConfigDir", "(Ljava/io/File;)V", "Lcom/pokeskies/skiesclear/config/ConfigManager;", "configManager", "Lcom/pokeskies/skiesclear/config/ConfigManager;", "getConfigManager", "()Lcom/pokeskies/skiesclear/config/ConfigManager;", "setConfigManager", "(Lcom/pokeskies/skiesclear/config/ConfigManager;)V", "Lnet/kyori/adventure/platform/fabric/FabricServerAudiences;", "adventure", "Lnet/kyori/adventure/platform/fabric/FabricServerAudiences;", "getAdventure", "()Lnet/kyori/adventure/platform/fabric/FabricServerAudiences;", "setAdventure", "(Lnet/kyori/adventure/platform/fabric/FabricServerAudiences;)V", "Lnet/minecraft/server/MinecraftServer;", "server", "Lnet/minecraft/server/MinecraftServer;", "getServer", "()Lnet/minecraft/server/MinecraftServer;", "setServer", "(Lnet/minecraft/server/MinecraftServer;)V", "Lcom/pokeskies/skiesclear/ClearManager;", "clearManager", "Lcom/pokeskies/skiesclear/ClearManager;", "getClearManager", "()Lcom/pokeskies/skiesclear/ClearManager;", "setClearManager", "(Lcom/pokeskies/skiesclear/ClearManager;)V", "Lcom/google/gson/Gson;", "gson", "Lcom/google/gson/Gson;", "getGson", "()Lcom/google/gson/Gson;", "setGson", "(Lcom/google/gson/Gson;)V", "gsonPretty", "getGsonPretty", "setGsonPretty", "Companion", "SkiesClear"})
public final class SkiesClear
implements ModInitializer {
    @NotNull
    public static final Companion Companion = new Companion(null);
    public File configDir;
    public ConfigManager configManager;
    @Nullable
    private FabricServerAudiences adventure;
    @Nullable
    private MinecraftServer server;
    public ClearManager clearManager;
    @NotNull
    private Gson gson;
    @NotNull
    private Gson gsonPretty;
    public static SkiesClear INSTANCE;
    private static final Logger LOGGER;
    private static boolean COBBLEMON_PRESENT;

    public SkiesClear() {
        GsonBuilder gsonBuilder = new GsonBuilder().disableHtmlEscaping().registerTypeAdapter((Type)((Object)class_2960.class), (Object)new Utils.ResourceLocationSerializer());
        class_2378 class_23782 = class_7923.field_41172;
        Intrinsics.checkNotNullExpressionValue((Object)class_23782, (String)"SOUND_EVENT");
        Gson gson = gsonBuilder.registerTypeHierarchyAdapter(class_3414.class, new Utils.RegistrySerializer(class_23782)).create();
        Intrinsics.checkNotNullExpressionValue((Object)gson, (String)"create(...)");
        this.gson = gson;
        Gson gson2 = this.gson.newBuilder().setPrettyPrinting().create();
        Intrinsics.checkNotNullExpressionValue((Object)gson2, (String)"create(...)");
        this.gsonPretty = gson2;
    }

    @NotNull
    public final File getConfigDir() {
        File file = this.configDir;
        if (file != null) {
            return file;
        }
        Intrinsics.throwUninitializedPropertyAccessException((String)"configDir");
        return null;
    }

    public final void setConfigDir(@NotNull File file) {
        Intrinsics.checkNotNullParameter((Object)file, (String)"<set-?>");
        this.configDir = file;
    }

    @NotNull
    public final ConfigManager getConfigManager() {
        ConfigManager configManager = this.configManager;
        if (configManager != null) {
            return configManager;
        }
        Intrinsics.throwUninitializedPropertyAccessException((String)"configManager");
        return null;
    }

    public final void setConfigManager(@NotNull ConfigManager configManager) {
        Intrinsics.checkNotNullParameter((Object)configManager, (String)"<set-?>");
        this.configManager = configManager;
    }

    @Nullable
    public final FabricServerAudiences getAdventure() {
        return this.adventure;
    }

    public final void setAdventure(@Nullable FabricServerAudiences fabricServerAudiences) {
        this.adventure = fabricServerAudiences;
    }

    @Nullable
    public final MinecraftServer getServer() {
        return this.server;
    }

    public final void setServer(@Nullable MinecraftServer minecraftServer) {
        this.server = minecraftServer;
    }

    @NotNull
    public final ClearManager getClearManager() {
        ClearManager clearManager = this.clearManager;
        if (clearManager != null) {
            return clearManager;
        }
        Intrinsics.throwUninitializedPropertyAccessException((String)"clearManager");
        return null;
    }

    public final void setClearManager(@NotNull ClearManager clearManager) {
        Intrinsics.checkNotNullParameter((Object)clearManager, (String)"<set-?>");
        this.clearManager = clearManager;
    }

    @NotNull
    public final Gson getGson() {
        return this.gson;
    }

    public final void setGson(@NotNull Gson gson) {
        Intrinsics.checkNotNullParameter((Object)gson, (String)"<set-?>");
        this.gson = gson;
    }

    @NotNull
    public final Gson getGsonPretty() {
        return this.gsonPretty;
    }

    public final void setGsonPretty(@NotNull Gson gson) {
        Intrinsics.checkNotNullParameter((Object)gson, (String)"<set-?>");
        this.gsonPretty = gson;
    }

    public void onInitialize() {
        Companion.setINSTANCE(this);
        this.setConfigDir(new File(FabricLoader.getInstance().getConfigDirectory(), "skiesclear"));
        this.setConfigManager(new ConfigManager(this.getConfigDir()));
        this.setClearManager(new ClearManager());
        ServerLifecycleEvents.SERVER_STARTING.register(arg_0 -> SkiesClear.onInitialize$lambda$0(this, arg_0));
        ServerLifecycleEvents.SERVER_STARTED.register(SkiesClear::onInitialize$lambda$1);
        ServerLifecycleEvents.SERVER_STOPPED.register(arg_0 -> SkiesClear.onInitialize$lambda$2(this, arg_0));
        CommandRegistrationCallback.EVENT.register(SkiesClear::onInitialize$lambda$3);
    }

    public final void reload() {
        this.getConfigManager().reload();
        this.getClearManager().reload();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @NotNull
    public final <T> T loadFile(@NotNull String filename, @NotNull T t, boolean create) {
        Object value;
        block13: {
            Intrinsics.checkNotNullParameter((Object)filename, (String)"filename");
            Intrinsics.checkNotNullParameter(t, (String)"default");
            File file = new File(this.getConfigDir(), filename);
            value = null;
            value = t;
            try {
                Files.createDirectories(this.getConfigDir().toPath(), new FileAttribute[0]);
                if (file.exists()) {
                    Closeable closeable = new FileReader(file);
                    Throwable throwable = null;
                    try {
                        FileReader reader = (FileReader)closeable;
                        boolean bl = false;
                        JsonReader jsonReader = new JsonReader((Reader)reader);
                        value = this.gsonPretty.fromJson(jsonReader, (Type)t.getClass());
                        reader = Unit.INSTANCE;
                        break block13;
                    }
                    catch (Throwable reader) {
                        throwable = reader;
                        throw reader;
                    }
                    finally {
                        CloseableKt.closeFinally((Closeable)closeable, (Throwable)throwable);
                    }
                }
                if (!create) break block13;
                Files.createFile(file.toPath(), new FileAttribute[0]);
                Closeable closeable = new FileWriter(file);
                Throwable throwable = null;
                try {
                    FileWriter fileWriter = (FileWriter)closeable;
                    boolean bl = false;
                    fileWriter.write(this.gsonPretty.toJson(t));
                    fileWriter.flush();
                    Unit unit = Unit.INSTANCE;
                }
                catch (Throwable throwable2) {
                    throwable = throwable2;
                    throw throwable2;
                }
                finally {
                    CloseableKt.closeFinally((Closeable)closeable, (Throwable)throwable);
                }
            }
            catch (Exception e) {
                Utils.INSTANCE.printError("An error has occured while attempting to load file '" + filename + "', with stacktrace:}");
                e.printStackTrace();
            }
        }
        return (T)value;
    }

    public static /* synthetic */ Object loadFile$default(SkiesClear skiesClear, String string, Object object, boolean bl, int n, Object object2) {
        if ((n & 4) != 0) {
            bl = false;
        }
        return skiesClear.loadFile(string, object, bl);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final <T> boolean saveFile(@NotNull String filename, T object) {
        Intrinsics.checkNotNullParameter((Object)filename, (String)"filename");
        File file = new File(this.getConfigDir(), filename);
        try {
            Closeable closeable = new FileWriter(file);
            Throwable throwable = null;
            try {
                FileWriter fileWriter = (FileWriter)closeable;
                boolean bl = false;
                fileWriter.write(this.gsonPretty.toJson(object));
                fileWriter.flush();
                Unit unit = Unit.INSTANCE;
            }
            catch (Throwable throwable2) {
                throwable = throwable2;
                throw throwable2;
            }
            finally {
                CloseableKt.closeFinally((Closeable)closeable, (Throwable)throwable);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    private static final void onInitialize$lambda$0(SkiesClear this$0, MinecraftServer server) {
        Intrinsics.checkNotNullParameter((Object)this$0, (String)"this$0");
        this$0.adventure = FabricServerAudiences.of((MinecraftServer)server);
        this$0.server = server;
    }

    private static final void onInitialize$lambda$1(MinecraftServer minecraftServer) {
        if (FabricLoader.getInstance().isModLoaded("cobblemon")) {
            COBBLEMON_PRESENT = true;
        }
    }

    private static final void onInitialize$lambda$2(SkiesClear this$0, MinecraftServer minecraftServer) {
        Intrinsics.checkNotNullParameter((Object)this$0, (String)"this$0");
        this$0.adventure = null;
    }

    private static final void onInitialize$lambda$3(CommandDispatcher dispatcher, class_7157 class_71572, class_2170.class_5364 class_53642) {
        BaseCommand baseCommand = new BaseCommand();
        Intrinsics.checkNotNull((Object)dispatcher);
        baseCommand.register((CommandDispatcher<class_2168>)dispatcher);
    }

    static {
        LOGGER = LogManager.getLogger((String)"skiesclear");
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\"\u0010\u0005\u001a\u00020\u00048\u0006@\u0006X\u0086.\u00a2\u0006\u0012\n\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u001f\u0010\r\u001a\n \f*\u0004\u0018\u00010\u000b0\u000b8\u0006\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010R\"\u0010\u0012\u001a\u00020\u00118\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017\u00a8\u0006\u0018"}, d2={"Lcom/pokeskies/skiesclear/SkiesClear$Companion;", "", "<init>", "()V", "Lcom/pokeskies/skiesclear/SkiesClear;", "INSTANCE", "Lcom/pokeskies/skiesclear/SkiesClear;", "getINSTANCE", "()Lcom/pokeskies/skiesclear/SkiesClear;", "setINSTANCE", "(Lcom/pokeskies/skiesclear/SkiesClear;)V", "Lorg/apache/logging/log4j/Logger;", "kotlin.jvm.PlatformType", "LOGGER", "Lorg/apache/logging/log4j/Logger;", "getLOGGER", "()Lorg/apache/logging/log4j/Logger;", "", "COBBLEMON_PRESENT", "Z", "getCOBBLEMON_PRESENT", "()Z", "setCOBBLEMON_PRESENT", "(Z)V", "SkiesClear"})
    public static final class Companion {
        private Companion() {
        }

        @NotNull
        public final SkiesClear getINSTANCE() {
            SkiesClear skiesClear = INSTANCE;
            if (skiesClear != null) {
                return skiesClear;
            }
            Intrinsics.throwUninitializedPropertyAccessException((String)"INSTANCE");
            return null;
        }

        public final void setINSTANCE(@NotNull SkiesClear skiesClear) {
            Intrinsics.checkNotNullParameter((Object)skiesClear, (String)"<set-?>");
            INSTANCE = skiesClear;
        }

        public final Logger getLOGGER() {
            return LOGGER;
        }

        public final boolean getCOBBLEMON_PRESENT() {
            return COBBLEMON_PRESENT;
        }

        public final void setCOBBLEMON_PRESENT(boolean bl) {
            COBBLEMON_PRESENT = bl;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

