# 命名牌保护功能说明

## 功能概述

精灵清理插件现在支持命名牌保护功能。任何使用命名牌命名的精灵实体都不会被清理系统清理，无论其他清理条件是否满足。

## 功能特点

### 1. 自动检测命名牌
- 插件会自动检测精灵实体是否有自定义名称（命名牌）
- 检测基于 Bukkit API 的 `entity.customName` 属性
- 支持任何非空的自定义名称，包括空字符串

### 2. 优先级保护
- 命名牌检查具有最高优先级
- 即使精灵满足其他所有清理条件，有命名牌的精灵也不会被清理
- 检查顺序：死亡检查 → 命名牌检查 → 排除列表检查 → 其他条件检查

### 3. 日志记录
- 当启用日志记录时，会记录命名牌保护的详细信息
- 日志会显示："精灵有命名牌，不清理: [精灵信息]"
- 精灵信息中会包含 `[有命名牌]` 标签

## 实现细节

### 代码修改位置

1. **shouldCleanPokemonEntity 方法**
   - 在基本检查后立即添加命名牌检查
   - 位置：第314-321行

```kotlin
// 检查是否有命名牌（自定义名称）
if (entity.customName != null) {
    if (configManager.enableLogging) {
        val entityInfo = getPokemonEntityInfo(entity)
        plugin.logger.info("精灵有命名牌，不清理: $entityInfo")
    }
    return false
}
```

2. **getPokemonEntityInfo 方法**
   - 在标签列表中添加命名牌状态
   - 位置：第1425行

```kotlin
if (customName != null) tags.add("有命名牌")
```

3. **getDetailedPokemonInfo 方法**
   - 在调试信息中添加命名牌状态
   - 位置：第2034行

```kotlin
info.add("是否有命名牌: ${entity.customName != null}")
```

### 检测逻辑

```kotlin
// 检测实体是否有命名牌
val hasNameTag = entity.customName != null

// 命名牌类型：
// - null: 没有命名牌
// - "": 空字符串命名牌（仍被视为有命名牌）
// - "任何文本": 有效的命名牌
```

## 使用示例

### 场景1：保护特殊精灵
```
玩家使用命名牌将皮卡丘命名为"我的皮卡丘"
→ 清理系统检测到命名牌
→ 该精灵不会被清理
→ 日志显示："精灵有命名牌，不清理: 我的皮卡丘 (Pikachu) [有命名牌]"
```

### 场景2：普通精灵清理
```
野生皮卡丘没有命名牌
→ 清理系统跳过命名牌检查
→ 继续检查其他清理条件
→ 如果满足清理条件，精灵会被清理
```

### 场景3：空命名牌保护
```
玩家使用命名牌但没有输入名称（空字符串）
→ 清理系统仍然检测到命名牌存在
→ 该精灵不会被清理
→ 日志显示："精灵有命名牌，不清理: (Pikachu) [有命名牌]"
```

## 兼容性

- 兼容所有现有的清理配置
- 不影响原版生物和掉落物的清理逻辑
- 与排除列表、高级设置等功能完全兼容
- 命名牌保护优先级高于所有其他设置

## 测试建议

1. **基本功能测试**
   - 给精灵使用命名牌，验证不会被清理
   - 移除命名牌，验证可以正常清理

2. **边界情况测试**
   - 测试空字符串命名牌
   - 测试特殊字符命名牌
   - 测试超长名称命名牌

3. **日志验证**
   - 启用日志记录
   - 检查命名牌保护的日志输出
   - 验证精灵信息中的标签显示

## 注意事项

1. 此功能仅适用于精灵实体，不影响原版生物清理
2. 命名牌检查基于 Bukkit API，与服务器版本兼容性良好
3. 功能自动启用，无需额外配置
4. 建议在生产环境使用前进行充分测试
