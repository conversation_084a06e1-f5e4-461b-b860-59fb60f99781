/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.collections.MapsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.config;

import com.pokeskies.skiesclear.config.ClearConfig;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u00002\u00020\u0001B'\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\n\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\b\n\u0010\u000bR\"\u0010\u0003\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0003\u0010\f\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R#\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0007\u0010\u0011\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006\u0014"}, d2={"Lcom/pokeskies/skiesclear/config/MainConfig;", "", "", "debug", "", "", "Lcom/pokeskies/skiesclear/config/ClearConfig;", "clears", "<init>", "(ZLjava/util/Map;)V", "toString", "()Ljava/lang/String;", "Z", "getDebug", "()Z", "setDebug", "(Z)V", "Ljava/util/Map;", "getClears", "()Ljava/util/Map;", "SkiesClear"})
public final class MainConfig {
    private boolean debug;
    @NotNull
    private final Map<String, ClearConfig> clears;

    public MainConfig(boolean debug, @NotNull Map<String, ClearConfig> clears) {
        Intrinsics.checkNotNullParameter(clears, (String)"clears");
        this.debug = debug;
        this.clears = clears;
    }

    public /* synthetic */ MainConfig(boolean bl, Map map, int n, DefaultConstructorMarker defaultConstructorMarker) {
        if ((n & 1) != 0) {
            bl = false;
        }
        if ((n & 2) != 0) {
            map = MapsKt.emptyMap();
        }
        this(bl, map);
    }

    public final boolean getDebug() {
        return this.debug;
    }

    public final void setDebug(boolean bl) {
        this.debug = bl;
    }

    @NotNull
    public final Map<String, ClearConfig> getClears() {
        return this.clears;
    }

    @NotNull
    public String toString() {
        return "MainConfig(debug=" + this.debug + ", clears=" + this.clears + ")";
    }

    public MainConfig() {
        this(false, null, 3, null);
    }
}

