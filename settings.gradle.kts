pluginManagement{
    repositories{
        maven("https://maven.fabricmc.net/")
        maven("https://maven.architectury.dev/")
        maven("https://files.minecraftforge.net/maven/")
        gradlePluginPortal()
    }
}
plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.8.0"
}
//rootProject.name = "CobblemonApi"
//include("CobblemonBasis")
//include("CobblemonLib")


rootProject.name = "AcePokemonCleaner"
