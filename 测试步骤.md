# 精灵清理排除功能测试步骤

## 准备工作

1. **确保配置正确**
   ```yaml
   # config.yml
   excluded-pokemon:
     - "shiny"      # 排除闪光精灵
     - "legendary"  # 排除传说精灵
     - "mythical"   # 排除幻之精灵
   
   debug-mode: true
   enable-logging: true
   ```

2. **重载插件配置**
   ```
   /pokemoncleaner reload
   ```

## 测试步骤

### 第一步：基础调试

1. **找到一些精灵**（最好包括闪光精灵）

2. **执行调试命令**
   ```
   /pokemoncleaner debug
   ```

3. **查看输出信息**
   - 检查是否正确识别了精灵实体
   - 查看实体类型Key（应该包含"cobblemon:pokemon"）
   - 查看实体类名（应该包含"Pokemon"）
   - 查看排除状态

### 第二步：闪光精灵测试

1. **寻找闪光精灵**
   - 如果有闪光精灵，记录其名称

2. **检查调试输出**
   - 查看"是否闪光"字段
   - 查看"是否被排除"字段
   - 查看名称匹配检查结果

3. **手动清理测试**
   ```
   /pokemoncleaner clean
   ```
   - 观察闪光精灵是否被清理
   - 查看日志中的排除信息

### 第三步：名称匹配测试

1. **给精灵设置包含关键词的名称**
   - 使用命名牌给精灵命名为包含"shiny"的名称
   - 或者寻找名称中包含"闪光"的精灵

2. **再次执行调试**
   ```
   /pokemoncleaner debug
   ```

3. **验证排除功能**
   ```
   /pokemoncleaner clean
   ```

## 预期结果

### 正常情况下应该看到：

1. **调试输出示例**
   ```
   发现 3 只精灵:
   1. [排除] 皮卡丘 [闪光]
   2. [可清理] 小火龙
   3. [排除] 超梦 [传说]
   
   详细信息:
     实体类型: POKEMON
     实体类型Key: cobblemon:pokemon
     实体类名: PokemonEntity
     是否闪光: true
     是否被排除: true
   ```

2. **清理日志示例**
   ```
   [INFO] 精灵被排除（闪光精灵）: 皮卡丘
   [INFO] 清理了精灵: 小火龙 在世界 world
   [INFO] 精灵被排除（传说精灵）: 超梦
   ```

## 故障排除

### 如果闪光精灵仍被清理：

1. **检查配置文件**
   - 确保 `excluded-pokemon` 包含 `"shiny"`
   - 确保配置文件格式正确

2. **检查调试输出**
   - 查看"是否闪光"是否为true
   - 查看"是否被排除"是否为true
   - 查看名称匹配检查结果

3. **检查实体信息**
   - 查看实体类型Key是否正确
   - 查看实体名称是否包含闪光关键词

### 如果无法识别精灵实体：

1. **检查实体类型**
   - 确保实体类型Key包含"cobblemon"
   - 确保实体类名包含"Pokemon"

2. **更新识别逻辑**
   - 可能需要根据具体的Cobblemon版本调整识别条件

## 日志分析

启用调试模式后，关键日志信息：

```
[INFO] 检查精灵排除状态:
[INFO]   实体名称: 皮卡丘
[INFO]   自定义名称: 
[INFO]   显示名称: 皮卡丘
[INFO]   实体类型: cobblemon:pokemon
[INFO]   实体类名: PokemonEntity
[INFO] 通过实体名称检测到闪光精灵: 皮卡丘
[INFO] 精灵被排除（闪光精灵）: 皮卡丘
```

这些日志可以帮助确定排除功能是否正常工作。
