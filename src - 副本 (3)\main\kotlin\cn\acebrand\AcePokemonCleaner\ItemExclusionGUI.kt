package cn.acebrand.AcePokemonCleaner

import org.bukkit.Bukkit
import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryClickEvent
import org.bukkit.event.inventory.InventoryCloseEvent
import org.bukkit.inventory.Inventory
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta
import org.bukkit.plugin.Plugin

/**
 * 掉落物排除管理GUI
 */
class ItemExclusionGUI(
    private val plugin: Plugin,
    private val exclusionManager: ItemExclusionManager
) : Listener {
    
    // 存储玩家当前页面
    private val playerPages = mutableMapOf<Player, Int>()

    // 存储打开的GUI
    private val openGUIs = mutableSetOf<Inventory>()

    // 存储玩家临时的排除物品列表（未保存）
    private val playerTempExcludedItems = mutableMapOf<Player, MutableSet<String>>()

    // 存储玩家临时的物品显示名称映射
    private val playerTempDisplayNames = mutableMapOf<Player, MutableMap<String, String>>()
    
    /**
     * 打开GUI菜单
     */
    fun openGUI(player: Player, page: Int = 0) {
        // 检查管理员权限
        if (!player.hasPermission("pokemoncleaner.admin") && !player.isOp) {
            player.sendMessage("§c只有管理员才能管理掉落物排除列表！")
            return
        }

        if (!exclusionManager.guiEnabled) {
            player.sendMessage(exclusionManager.getMessage("gui-disabled"))
            return
        }

        // 初始化玩家的临时数据
        if (!playerTempExcludedItems.containsKey(player)) {
            playerTempExcludedItems[player] = exclusionManager.getGuiExcludedItems().toMutableSet()
            playerTempDisplayNames[player] = mutableMapOf()

            // 复制现有的显示名称映射
            exclusionManager.getGuiExcludedItems().forEach { itemId ->
                val displayName = exclusionManager.getItemDisplayName(itemId)
                if (displayName != itemId) {
                    playerTempDisplayNames[player]!![itemId] = displayName
                }
            }
        }

        val inventory = createGUI(player, page)
        player.openInventory(inventory)
        playerPages[player] = page
        openGUIs.add(inventory)
    }
    
    /**
     * 创建GUI界面
     */
    private fun createGUI(player: Player, page: Int): Inventory {
        val inventory = Bukkit.createInventory(null, exclusionManager.guiSize * 9, exclusionManager.guiTitle)

        // 获取玩家的临时排除物品列表
        val excludedItems = playerTempExcludedItems[player]?.toList() ?: emptyList()
        val totalPages = (excludedItems.size + exclusionManager.itemsPerPage - 1) / exclusionManager.itemsPerPage
        val startIndex = page * exclusionManager.itemsPerPage
        val endIndex = minOf(startIndex + exclusionManager.itemsPerPage, excludedItems.size)

        // 添加排除的物品
        for (i in startIndex until endIndex) {
            val itemId = excludedItems[i]
            val itemStack = exclusionManager.createItemStack(itemId)

            if (itemStack != null) {
                val meta = itemStack.itemMeta
                if (meta != null) {
                    val displayName = playerTempDisplayNames[player]?.get(itemId) ?: getSimpleDisplayName(itemId)
                    meta.setDisplayName("§e$displayName")

                    val lore = mutableListOf<String>()
                    lore.add("§7基础ID: §f${getBaseId(itemId)}")

                    // 显示详细信息
                    val details = parseItemDetails(itemId)
                    details.forEach { (key, value) ->
                        if (key != "base") {
                            lore.add("§7$key: §f$value")
                        }
                    }

                    lore.add("§7状态: §a已排除")
                    lore.add("§c右键点击移除")
                    lore.add("§7或拖拽到背包移除")

                    meta.lore = lore
                    itemStack.itemMeta = meta
                }

                inventory.setItem(i - startIndex, itemStack)
            }
        }

        // 添加控制按钮
        addControlButtons(inventory, page, totalPages, player)

        return inventory
    }
    
    /**
     * 添加控制按钮
     */
    private fun addControlButtons(inventory: Inventory, currentPage: Int, totalPages: Int, player: Player) {
        val size = inventory.size

        // 计算未保存的更改数量
        val originalItems = exclusionManager.getGuiExcludedItems()
        val tempItems = playerTempExcludedItems[player] ?: mutableSetOf()
        val hasChanges = originalItems != tempItems
        val changeCount = (tempItems - originalItems).size + (originalItems - tempItems).size

        // 信息显示按钮
        val infoButton = createButton(
            Material.BOOK,
            "§6§l操作说明",
            listOf(
                "§7§l添加物品:",
                "§7• 拖拽背包物品到空槽位",
                "§7• Shift+左键背包物品快速添加",
                "",
                "§7§l移除物品:",
                "§7• 右键点击菜单中的物品",
                "§7• 拖拽菜单物品到背包",
                "",
                "§7§l其他:",
                "§7• 重复添加会替换物品信息",
                "§7• 移除操作会实时同步本地文件",
                "§7• 点击保存按钮批量保存更改",
                "",
                "§7当前排除物品: §e${tempItems.size}",
                if (hasChanges) "§c未保存更改: §e$changeCount" else "§a所有更改已保存"
            )
        )
        inventory.setItem(size - 5, infoButton) // slot 49 for 6-row inventory

        // 保存配置按钮
        val saveButton = createButton(
            if (hasChanges) Material.LIME_STAINED_GLASS_PANE else Material.GREEN_STAINED_GLASS_PANE,
            if (hasChanges) "§a§l保存更改 §e($changeCount)" else "§a§l已保存",
            if (hasChanges) listOf(
                "§7保存当前配置到文件",
                "§7有 §e$changeCount §7个未保存的更改",
                "§e左键: 保存配置"
            ) else listOf(
                "§7配置已保存",
                "§7所有更改都已保存到文件"
            )
        )
        inventory.setItem(size - 4, saveButton) // slot 50

        // 重置按钮
        val resetButton = createButton(
            Material.ORANGE_STAINED_GLASS_PANE,
            "§6§l重置更改",
            listOf(
                "§7撤销所有未保存的更改",
                "§7恢复到上次保存的状态",
                "§e左键: 重置更改"
            )
        )
        inventory.setItem(size - 3, resetButton) // slot 51

        // 重载配置按钮
        val reloadButton = createButton(
            Material.YELLOW_STAINED_GLASS_PANE,
            "§e§l重载配置",
            listOf(
                "§7重新加载配置文件",
                "§7应用最新的配置更改",
                "§c注意: 会丢失未保存的更改",
                "§e左键: 重载配置"
            )
        )
        inventory.setItem(size - 2, reloadButton) // slot 52

        // 关闭菜单按钮
        val closeButton = createButton(
            Material.BARRIER,
            "§c§l关闭菜单",
            listOf(
                "§7关闭当前菜单",
                if (hasChanges) "§c注意: 有未保存的更改" else "§7所有更改已保存",
                "§e左键: 关闭"
            )
        )
        inventory.setItem(size - 6, closeButton) // slot 48
        
        // 分页按钮
        if (totalPages > 1) {

            // 上一页按钮
            if (currentPage > 0) {
                val prevButton = createButton(
                    Material.ARROW,
                    "§f§l上一页",
                    listOf(
                        "§7查看上一页的物品",
                        "§7当前页: §e${currentPage + 1}§7/§e$totalPages",
                        "§e左键: 上一页"
                    )
                )
                inventory.setItem(size - 9, prevButton) // slot 45
            }

            // 下一页按钮
            if (currentPage < totalPages - 1) {
                val nextButton = createButton(
                    Material.ARROW,
                    "§f§l下一页",
                    listOf(
                        "§7查看下一页的物品",
                        "§7当前页: §e${currentPage + 1}§7/§e$totalPages",
                        "§e左键: 下一页"
                    )
                )
                inventory.setItem(size - 1, nextButton) // slot 53
            }
        } else {
        }
    }
    
    /**
     * 创建按钮物品
     */
    private fun createButton(material: Material, name: String, lore: List<String>): ItemStack {
        val item = ItemStack(material)
        val meta = item.itemMeta
        if (meta != null) {
            meta.setDisplayName(name)
            meta.lore = lore
            item.itemMeta = meta
        }
        return item
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    fun onInventoryClick(event: InventoryClickEvent) {
        val inventory = event.inventory
        if (!openGUIs.contains(inventory)) return

        val player = event.whoClicked as? Player ?: return
        val slot = event.slot
        val size = inventory.size

        // 检查是否点击控制按钮
        when (slot) {
            size - 5 -> { // 信息按钮
                event.isCancelled = true
                return
            }
            size - 4 -> { // 保存配置
                event.isCancelled = true
                handleSaveConfig(player)
                return
            }
            size - 3 -> { // 重置更改
                event.isCancelled = true
                handleResetChanges(player)
                return
            }
            size - 2 -> { // 重载配置
                event.isCancelled = true
                handleReloadConfig(player)
                return
            }
            size - 6 -> { // 关闭菜单
                event.isCancelled = true
                player.closeInventory()
                return
            }
            size - 9 -> { // 上一页
                event.isCancelled = true
                handlePreviousPage(player)
                return
            }
            size - 1 -> { // 下一页
                event.isCancelled = true
                handleNextPage(player)
                return
            }
        }

        // 处理物品槽位的操作
        if (slot < exclusionManager.itemsPerPage) {
            handleItemSlotClick(event, player, slot)
        } else {
            // 其他槽位取消事件
            event.isCancelled = true
        }
    }

    /**
     * 处理Shift+点击事件（快速添加）
     */
    @EventHandler
    fun onInventoryShiftClick(event: InventoryClickEvent) {
        // 检查是否是从玩家背包Shift+左键点击
        if (event.click.isShiftClick && event.click.isLeftClick) {
            val player = event.whoClicked as? Player ?: return

            // 检查是否有打开的GUI
            if (!playerPages.containsKey(player)) return

            // 检查是否点击的是玩家背包
            if (event.clickedInventory == player.inventory) {
                val clickedItem = event.currentItem
                if (clickedItem != null && clickedItem.type != Material.AIR) {
                    event.isCancelled = true
                    handleShiftClickAdd(player, clickedItem)
                }
            }
        }
    }
    
    /**
     * 处理物品槽位点击
     */
    private fun handleItemSlotClick(event: InventoryClickEvent, player: Player, slot: Int) {
        val clickedItem = event.currentItem
        val cursorItem = event.cursor

        when {
            // 右键点击已有物品 - 移除
            event.isRightClick && clickedItem != null && clickedItem.type != Material.AIR -> {
                event.isCancelled = true
                handleRemoveItemFromSlot(player, slot)
            }

            // 拖拽物品到空槽位 - 添加
            cursorItem != null && cursorItem.type != Material.AIR &&
            (clickedItem == null || clickedItem.type == Material.AIR) -> {
                event.isCancelled = true
                handleAddItemToSlot(player, cursorItem, slot)
            }

            // 拖拽物品到已有物品 - 替换
            cursorItem != null && cursorItem.type != Material.AIR &&
            clickedItem != null && clickedItem.type != Material.AIR -> {
                event.isCancelled = true
                handleReplaceItemInSlot(player, cursorItem, slot)
            }

            // 拖拽已有物品到背包 - 移除
            event.click.name.contains("PICKUP") && clickedItem != null && clickedItem.type != Material.AIR -> {
                event.isCancelled = true
                handleRemoveItemFromSlot(player, slot)
            }

            else -> {
                event.isCancelled = true
            }
        }
    }
    
    /**
     * 处理Shift+点击快速添加
     */
    private fun handleShiftClickAdd(player: Player, item: ItemStack) {
        val itemId = getItemId(item)
        val tempItems = playerTempExcludedItems[player] ?: return

        if (!tempItems.contains(itemId)) {
            tempItems.add(itemId)

            // 添加显示名称映射
            val displayName = getItemDisplayName(item)
            if (displayName.isNotEmpty() && displayName != itemId) {
                playerTempDisplayNames[player]?.set(itemId, displayName)
            }

            val itemName = getItemName(item)
            player.sendMessage("§a[快速添加] 已添加物品 §e$itemName §a到排除列表（未保存）")

            // 检查是否需要跳转到新页面
            checkAndJumpToNewItemPage(player, tempItems.size)
        } else {
            val itemName = getItemName(item)
            player.sendMessage("§e物品 §6$itemName §e已经在排除列表中")
        }
    }

    /**
     * 处理添加物品到槽位
     */
    private fun handleAddItemToSlot(player: Player, item: ItemStack, slot: Int) {
        val itemId = getItemId(item)
        val tempItems = playerTempExcludedItems[player] ?: return

        // 如果物品已存在，替换（更新显示名称等）
        if (tempItems.contains(itemId)) {
            // 更新显示名称映射
            val displayName = getItemDisplayName(item)
            if (displayName.isNotEmpty() && displayName != itemId) {
                playerTempDisplayNames[player]?.set(itemId, displayName)
            }

            val itemName = getItemName(item)
            player.sendMessage("§6已更新物品 §e$itemName §6的信息（未保存）")
            refreshGUI(player)
        } else {
            tempItems.add(itemId)

            // 添加显示名称映射
            val displayName = getItemDisplayName(item)
            if (displayName.isNotEmpty() && displayName != itemId) {
                playerTempDisplayNames[player]?.set(itemId, displayName)
            }

            val itemName = getItemName(item)
            player.sendMessage("§a已添加物品 §e$itemName §a到排除列表（未保存）")

            // 检查是否需要跳转到新页面
            checkAndJumpToNewItemPage(player, tempItems.size)
        }
    }

    /**
     * 处理替换槽位中的物品
     */
    private fun handleReplaceItemInSlot(player: Player, newItem: ItemStack, slot: Int) {
        // 先移除旧物品
        handleRemoveItemFromSlot(player, slot)
        // 再添加新物品
        handleAddItemToSlot(player, newItem, slot)
    }

    /**
     * 处理从槽位移除物品
     */
    private fun handleRemoveItemFromSlot(player: Player, slot: Int) {
        val currentPage = playerPages[player] ?: 0
        val tempItems = playerTempExcludedItems[player] ?: return
        val itemsList = tempItems.toList()
        val itemIndex = currentPage * exclusionManager.itemsPerPage + slot

        if (itemIndex < itemsList.size) {
            val itemId = itemsList[itemIndex]
            val displayName = playerTempDisplayNames[player]?.get(itemId) ?: itemId

            tempItems.remove(itemId)
            playerTempDisplayNames[player]?.remove(itemId)

            // 实时同步到本地文件
            exclusionManager.removeExcludedItemById(itemId)

            player.sendMessage("§c已从排除列表移除物品 §e$displayName")
            player.sendMessage("§7本地文件已同步更新")

            // 检查移除后是否需要调整页面
            val newTotalPages = if (tempItems.isEmpty()) 1 else (tempItems.size + exclusionManager.itemsPerPage - 1) / exclusionManager.itemsPerPage
            val newCurrentPage = if (currentPage >= newTotalPages) {
                maxOf(0, newTotalPages - 1)
            } else {
                currentPage
            }

            if (newCurrentPage != currentPage) {
                player.sendMessage("§7自动跳转到第 §e${newCurrentPage + 1} §7页")
            }

            refreshGUI(player)
        }
    }
    
    /**
     * 处理保存配置
     */
    private fun handleSaveConfig(player: Player) {
        val tempItems = playerTempExcludedItems[player] ?: return
        val tempDisplayNames = playerTempDisplayNames[player] ?: return

        // 使用批量更新方法
        exclusionManager.updateExcludedItems(tempItems, tempDisplayNames)

        player.sendMessage("§a配置已保存！共保存 §e${tempItems.size} §a个排除物品")
        player.sendMessage("§7本地文件已同步更新")
        refreshGUI(player)
    }

    /**
     * 处理重置更改
     */
    private fun handleResetChanges(player: Player) {
        // 重置为原始数据
        playerTempExcludedItems[player] = exclusionManager.getGuiExcludedItems().toMutableSet()
        playerTempDisplayNames[player] = mutableMapOf()

        // 复制现有的显示名称映射
        exclusionManager.getGuiExcludedItems().forEach { itemId ->
            val displayName = exclusionManager.getItemDisplayName(itemId)
            if (displayName != itemId) {
                playerTempDisplayNames[player]!![itemId] = displayName
            }
        }

        player.sendMessage("§6已重置所有未保存的更改")
        refreshGUI(player)
    }
    
    /**
     * 处理重载配置
     */
    private fun handleReloadConfig(player: Player) {
        exclusionManager.loadConfig()

        // 重置玩家的临时数据
        playerTempExcludedItems[player] = exclusionManager.getGuiExcludedItems().toMutableSet()
        playerTempDisplayNames[player] = mutableMapOf()

        // 复制现有的显示名称映射
        exclusionManager.getGuiExcludedItems().forEach { itemId ->
            val displayName = exclusionManager.getItemDisplayName(itemId)
            if (displayName != itemId) {
                playerTempDisplayNames[player]!![itemId] = displayName
            }
        }

        player.sendMessage("§a配置已重新加载！")
        refreshGUI(player)
    }
    
    /**
     * 处理上一页
     */
    private fun handlePreviousPage(player: Player) {
        val currentPage = playerPages[player] ?: 0
        if (currentPage > 0) {
            openGUI(player, currentPage - 1)
        }
    }
    
    /**
     * 处理下一页
     */
    private fun handleNextPage(player: Player) {
        val currentPage = playerPages[player] ?: 0
        val tempItems = playerTempExcludedItems[player] ?: return
        val totalPages = (tempItems.size + exclusionManager.itemsPerPage - 1) / exclusionManager.itemsPerPage

        if (currentPage < totalPages - 1) {
            openGUI(player, currentPage + 1)
        }
    }
    
    /**
     * 检查并跳转到新添加物品的页面
     */
    private fun checkAndJumpToNewItemPage(player: Player, totalItems: Int) {
        val currentPage = playerPages[player] ?: 0
        val totalPages = if (totalItems == 0) 1 else (totalItems + exclusionManager.itemsPerPage - 1) / exclusionManager.itemsPerPage

        // 计算新添加的物品应该在哪一页
        val newItemIndex = totalItems - 1 // 最后一个物品的索引
        val newItemPage = newItemIndex / exclusionManager.itemsPerPage

        if (newItemPage != currentPage) {
            player.sendMessage("§7自动跳转到第 §e${newItemPage + 1} §7页查看新添加的物品")
            openGUI(player, newItemPage)
        } else {
            refreshGUI(player)
        }
    }

    /**
     * 刷新GUI
     */
    private fun refreshGUI(player: Player) {
        val currentPage = playerPages[player] ?: 0
        val tempItems = playerTempExcludedItems[player] ?: mutableSetOf()
        val totalPages = if (tempItems.isEmpty()) 1 else (tempItems.size + exclusionManager.itemsPerPage - 1) / exclusionManager.itemsPerPage

        // 如果当前页面超出了总页数，跳转到最后一页
        val validPage = if (currentPage >= totalPages) {
            maxOf(0, totalPages - 1)
        } else {
            currentPage
        }

        openGUI(player, validPage)
    }
    
    /**
     * 获取物品ID
     */
    private fun getItemId(itemStack: ItemStack): String {
        return try {
            // 尝试获取完整的物品ID（包括命名空间）
            val material = itemStack.type
            val key = material.key
            "${key.namespace}:${key.key}"
        } catch (e: Exception) {
            // 如果失败，使用简单的材料名称
            itemStack.type.name.lowercase()
        }
    }

    /**
     * 获取物品显示名称
     */
    private fun getItemDisplayName(itemStack: ItemStack): String {
        return try {
            // 优先使用自定义名称
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                itemStack.itemMeta?.displayName ?: ""
            } else {
                // 使用材料名称
                itemStack.type.name.lowercase().replace("_", " ")
            }
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 解析物品详细信息
     */
    private fun parseItemDetails(itemInfo: String): Map<String, String> {
        val details = mutableMapOf<String, String>()
        val parts = itemInfo.split(";")

        if (parts.isNotEmpty()) {
            details["base"] = parts[0]
        }

        for (i in 1 until parts.size) {
            val part = parts[i]
            if (part.contains(":")) {
                val keyValue = part.split(":", limit = 2)
                if (keyValue.size == 2) {
                    details[keyValue[0]] = keyValue[1]
                }
            }
        }

        return details
    }

    /**
     * 获取基础ID
     */
    private fun getBaseId(itemInfo: String): String {
        return itemInfo.split(";")[0]
    }

    /**
     * 获取简单的显示名称
     */
    private fun getSimpleDisplayName(itemId: String): String {
        val baseId = getBaseId(itemId)
        val details = parseItemDetails(itemId)

        // 如果有自定义名称，使用自定义名称
        details["name"]?.let { name ->
            return name.replace("_", " ")
        }

        // 否则使用基础ID的物品名部分
        val parts = baseId.split(":")
        return if (parts.size >= 2) {
            parts[1].replace("_", " ").split(" ").joinToString(" ") { word ->
                word.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
            }
        } else {
            baseId.replace("_", " ")
        }
    }

    /**
     * 获取物品名称
     */
    private fun getItemName(item: ItemStack): String {
        return if (item.hasItemMeta() && item.itemMeta?.hasDisplayName() == true) {
            item.itemMeta?.displayName ?: item.type.name
        } else {
            item.type.name
        }
    }
    
    /**
     * 处理GUI关闭事件
     */
    @EventHandler
    fun onInventoryClose(event: InventoryCloseEvent) {
        val inventory = event.inventory
        if (openGUIs.contains(inventory)) {
            openGUIs.remove(inventory)
            val player = event.player as? Player
            if (player != null) {
                playerPages.remove(player)

                // 检查是否有未保存的更改
                val originalItems = exclusionManager.getGuiExcludedItems()
                val tempItems = playerTempExcludedItems[player] ?: mutableSetOf()

                if (originalItems != tempItems) {
                    player.sendMessage("§e注意: 你有未保存的更改！使用 §6/pokemoncleaner items §e重新打开菜单并保存")
                }

                // 清理临时数据（可选，如果想保留到下次打开可以注释掉）
                // playerTempExcludedItems.remove(player)
                // playerTempDisplayNames.remove(player)
            }
        }
    }
}
