{"md5": "2808b39bf34fb2bed1a4bd1e0f07986c", "sha2": "88f0adafbd4652ef2a653ee90bb42c86bbadab98", "sha256": "a8a1a3417deca5bdfe79c595ff200e04c8633c41c72ed05d10844b9d6f79afca", "contents": {"classes": {"classes/com/sun/jndi/dns/Resolver.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/Resolver", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;II)V", "exs": ["javax/naming/NamingException"]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "query", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IIZZ)Lcom/sun/jndi/dns/ResourceRecords;", "exs": ["javax/naming/NamingException"]}, {"nme": "queryZone", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IZ)Lcom/sun/jndi/dns/ResourceRecords;", "exs": ["javax/naming/NamingException"]}, {"nme": "findZoneName", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IZ)Lcom/sun/jndi/dns/DnsName;", "exs": ["javax/naming/NamingException"]}, {"nme": "findSoa", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IZ)Lcom/sun/jndi/dns/ResourceRecord;", "exs": ["javax/naming/NamingException"]}, {"nme": "findNameServers", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsName;Z)[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["javax/naming/NamingException"]}], "flds": [{"acc": 2, "nme": "dnsClient", "dsc": "Lcom/sun/jndi/dns/DnsClient;"}, {"acc": 2, "nme": "timeout", "dsc": "I"}, {"acc": 2, "nme": "retries", "dsc": "I"}]}, "classes/com/sun/jndi/dns/DnsNameParser.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/DnsNameParser", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "parse", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/naming/Name;", "exs": ["javax/naming/NamingException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "classes/com/sun/jndi/url/dns/dnsURLContextFactory.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/url/dns/dnsURLContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getObjectInstance", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Lja<PERSON>/util/Hashtable;)Ljava/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Object;Ljavax/naming/Name;Ljavax/naming/Context;Ljava/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getUsingURL", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "getUsingURLs", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}], "flds": []}, "classes/com/sun/jndi/dns/DNSDatagramChannelFactory.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/DNSDatagramChannelFactory", "super": "java/lang/Object", "mthds": [{"nme": "findFirstFreePort", "acc": 10, "dsc": "()I"}, {"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Random;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Random;Ljava/net/ProtocolFamily;II)V"}, {"nme": "open", "acc": 33, "dsc": "()Ljava/nio/channels/DatagramChannel;", "exs": ["java/io/IOException"]}, {"nme": "openDefault", "acc": 2, "dsc": "()Ljava/nio/channels/DatagramChannel;", "exs": ["java/io/IOException"]}, {"nme": "isUsingNativePortRandomization", "acc": 32, "dsc": "()Z"}, {"nme": "isUsingJavaPortRandomization", "acc": 32, "dsc": "()Z"}, {"nme": "isUndecided", "acc": 32, "dsc": "()Z"}, {"nme": "farEnough", "acc": 2, "dsc": "(I)Z"}, {"nme": "openRandom", "acc": 2, "dsc": "()Ljava/nio/channels/DatagramChannel;", "exs": ["java/io/IOException"]}, {"nme": "getLocalPort", "acc": 10, "dsc": "(Ljava/nio/channels/DatagramChannel;)I", "exs": ["java/io/IOException"]}, {"nme": "lambda$findFirstFreePort$0", "acc": 4106, "dsc": "()Ljava/net/DatagramSocket;", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "DEVIATION", "dsc": "I", "val": 3}, {"acc": 24, "nme": "THRESHOLD", "dsc": "I", "val": 6}, {"acc": 24, "nme": "BIT_DEVIATION", "dsc": "I", "val": 2}, {"acc": 24, "nme": "HISTORY", "dsc": "I", "val": 32}, {"acc": 24, "nme": "MAX_RANDOM_TRIES", "dsc": "I", "val": 5}, {"acc": 0, "nme": "lastport", "dsc": "I"}, {"acc": 0, "nme": "lastSystemAllocated", "dsc": "I"}, {"acc": 0, "nme": "suitablePortCount", "dsc": "I"}, {"acc": 0, "nme": "unsuitablePortCount", "dsc": "I"}, {"acc": 16, "nme": "family", "dsc": "Ljava/net/ProtocolFamily;"}, {"acc": 16, "nme": "thresholdCount", "dsc": "I"}, {"acc": 16, "nme": "deviation", "dsc": "I"}, {"acc": 16, "nme": "random", "dsc": "<PERSON><PERSON><PERSON>/util/Random;"}, {"acc": 16, "nme": "history", "dsc": "Lcom/sun/jndi/dns/DNSDatagramChannelFactory$PortHistory;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/com/sun/jndi/dns/BaseNameClassPairEnumeration.class": {"ver": 65, "acc": 1056, "nme": "com/sun/jndi/dns/BaseNameClassPairEnumeration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsContext;<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(Lcom/sun/jndi/dns/DnsContext;Ljava/util/Hashtable<Ljava/lang/String;Lcom/sun/jndi/dns/NameNode;>;)V"}, {"nme": "close", "acc": 17, "dsc": "()V"}, {"nme": "hasMore", "acc": 17, "dsc": "()Z"}, {"nme": "hasMoreElements", "acc": 17, "dsc": "()Z"}, {"nme": "next", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;", "exs": ["javax/naming/NamingException"]}, {"nme": "nextElement", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;"}], "flds": [{"acc": 4, "nme": "nodes", "dsc": "Ljava/util/Enumeration;", "sig": "Ljava/util/Enumeration<Lcom/sun/jndi/dns/NameNode;>;"}, {"acc": 4, "nme": "ctx", "dsc": "Lcom/sun/jndi/dns/DnsContext;"}]}, "classes/com/sun/jndi/dns/DnsName$1.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/DnsName$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;)V"}, {"nme": "hasMoreElements", "acc": 1, "dsc": "()Z"}, {"nme": "nextElement", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nextElement", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 0, "nme": "pos", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lcom/sun/jndi/dns/DnsName;"}]}, "classes/com/sun/jndi/dns/ZoneNode.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/ZoneNode", "super": "com/sun/jndi/dns/NameNode", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "newNameNode", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jndi/dns/NameNode;"}, {"nme": "depopulate", "acc": 32, "dsc": "()V"}, {"nme": "isPopulated", "acc": 32, "dsc": "()Z"}, {"nme": "getContents", "acc": 32, "dsc": "()Lcom/sun/jndi/dns/NameNode;"}, {"nme": "isExpired", "acc": 32, "dsc": "()Z"}, {"nme": "getDeepestPopulated", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;)Lcom/sun/jndi/dns/ZoneNode;"}, {"nme": "populate", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;Lcom/sun/jndi/dns/ResourceRecords;)Lcom/sun/jndi/dns/NameNode;"}, {"nme": "setExpiration", "acc": 2, "dsc": "(J)V"}, {"nme": "getMinimumTtl", "acc": 10, "dsc": "(Lcom/sun/jndi/dns/ResourceRecord;)J"}, {"nme": "compareSerialNumberTo", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/ResourceRecord;)I"}, {"nme": "getSerialNumber", "acc": 10, "dsc": "(Lcom/sun/jndi/dns/ResourceRecord;)J"}], "flds": [{"acc": 2, "nme": "contentsRef", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/SoftReference;", "sig": "Ljava/lang/ref/SoftReference<Lcom/sun/jndi/dns/NameNode;>;"}, {"acc": 2, "nme": "serialNumber", "dsc": "J"}, {"acc": 2, "nme": "expiration", "dsc": "<PERSON><PERSON><PERSON>/util/Date;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/com/sun/jndi/dns/Tcp.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/Tcp", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/net/InetAddress;II)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "readWithTimeout", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/Tcp$SocketReadOp;)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 0, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 0, "dsc": "([BII)I", "exs": ["java/io/IOException"]}, {"nme": "lambda$read$1", "acc": 4098, "dsc": "([BII)I", "exs": ["java/io/IOException"]}, {"nme": "lambda$read$0", "acc": 4098, "dsc": "()I", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "sock", "dsc": "Ljava/net/Socket;"}, {"acc": 18, "nme": "in", "dsc": "Ljava/io/InputStream;"}, {"acc": 16, "nme": "out", "dsc": "Ljava/io/OutputStream;"}, {"acc": 2, "nme": "timeoutLeft", "dsc": "I"}]}, "classes/com/sun/jndi/url/dns/dnsURLContext.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/url/dns/dnsURLContext", "super": "com/sun/jndi/toolkit/url/GenericURLDirContext", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Hashtable<**>;)V"}, {"nme": "getRootURLContext", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)Ljavax/naming/spi/ResolveResult;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/util/Hashtable<**>;)Ljavax/naming/spi/ResolveResult;", "exs": ["javax/naming/NamingException"]}], "flds": []}, "classes/com/sun/jndi/dns/BindingEnumeration.class": {"ver": 65, "acc": 48, "nme": "com/sun/jndi/dns/BindingEnumeration", "super": "com/sun/jndi/dns/BaseNameClassPairEnumeration", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsContext;<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(Lcom/sun/jndi/dns/DnsContext;Ljava/util/Hashtable<Ljava/lang/String;Lcom/sun/jndi/dns/NameNode;>;)V"}, {"nme": "next", "acc": 1, "dsc": "()Ljavax/naming/Binding;", "exs": ["javax/naming/NamingException"]}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}], "flds": []}, "classes/com/sun/jndi/dns/DnsContextFactory.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/dns/DnsContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getInitialContext", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/Hashtable;)Ljavax/naming/Context;", "sig": "(L<PERSON><PERSON>/util/Hashtable<**>;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "getContext", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Ljava/util/Hashtable;)Lcom/sun/jndi/dns/DnsContext;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Ljava/util/Hashtable<**>;)Lcom/sun/jndi/dns/DnsContext;", "exs": ["javax/naming/NamingException"]}, {"nme": "getContext", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lcom/sun/jndi/dns/DnsUrl;Ljava/util/Hashtable;)Lcom/sun/jndi/dns/DnsContext;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Lcom/sun/jndi/dns/DnsUrl;Ljava/util/Hashtable<**>;)Lcom/sun/jndi/dns/DnsContext;", "exs": ["javax/naming/NamingException"]}, {"nme": "platformServersAvailable", "acc": 9, "dsc": "()Z"}, {"nme": "urlToContext", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Hashtable;)Ljavax/naming/Context;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Hashtable<**>;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "serversForUrls", "acc": 10, "dsc": "([Lcom/sun/jndi/dns/DnsUrl;)[L<PERSON><PERSON>/lang/String;", "exs": ["javax/naming/NamingException"]}, {"nme": "platformServersUsed", "acc": 10, "dsc": "([Lcom/sun/jndi/dns/DnsUrl;)Z"}, {"nme": "constructProviderUrl", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getInitCtxUrl", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Hashtable;)<PERSON><PERSON><PERSON>/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/util/Hashtable<**>;)Ljava/lang/String;"}, {"nme": "filterNameServers", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Z)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/String;>;Z)Ljava/util/List<Ljava/lang/String;>;"}], "flds": [{"acc": 26, "nme": "DEFAULT_URL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dns:"}, {"acc": 26, "nme": "DEFAULT_PORT", "dsc": "I", "val": 53}]}, "classes/com/sun/jndi/dns/DnsClient.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/dns/DnsClient", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;II)V", "exs": ["javax/naming/NamingException"]}, {"nme": "getDatagramChannel", "acc": 0, "dsc": "()Ljava/nio/channels/DatagramChannel;", "exs": ["javax/naming/NamingException"]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "query", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IIZZ)Lcom/sun/jndi/dns/ResourceRecords;", "exs": ["javax/naming/NamingException"]}, {"nme": "queryZone", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;IZ)Lcom/sun/jndi/dns/ResourceRecords;", "exs": ["javax/naming/NamingException"]}, {"nme": "doUdpQuery", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/Packet;Ljava/net/InetAddress;III)[B", "exs": ["java/io/IOException", "javax/naming/NamingException"]}, {"nme": "blockingReceive", "acc": 0, "dsc": "(Ljava/nio/channels/DatagramChannel;Ljava/nio/Byte<PERSON>uffer;J)Z", "exs": ["java/io/IOException"]}, {"nme": "doTcpQuery", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/Tcp;Lcom/sun/jndi/dns/Packet;)[B", "exs": ["java/io/IOException"]}, {"nme": "continueTcpQuery", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/Tcp;)[B", "exs": ["java/io/IOException"]}, {"nme": "makeQueryPacket", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsName;IIIZ)Lcom/sun/jndi/dns/Packet;"}, {"nme": "makeQueryName", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsName;Lcom/sun/jndi/dns/Packet;I)V"}, {"nme": "lookupResponse", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)[B", "exs": ["javax/naming/NamingException"]}, {"nme": "isMatchResponse", "acc": 2, "dsc": "([BI)Z", "exs": ["javax/naming/NamingException"]}, {"nme": "checkResponseCode", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/Header;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "dprint", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(Ljava/nio/channels/Selector;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "IDENT_OFFSET", "dsc": "I", "val": 0}, {"acc": 26, "nme": "FLAGS_OFFSET", "dsc": "I", "val": 2}, {"acc": 26, "nme": "NUMQ_OFFSET", "dsc": "I", "val": 4}, {"acc": 26, "nme": "NUMANS_OFFSET", "dsc": "I", "val": 6}, {"acc": 26, "nme": "NUMAUTH_OFFSET", "dsc": "I", "val": 8}, {"acc": 26, "nme": "NUMADD_OFFSET", "dsc": "I", "val": 10}, {"acc": 26, "nme": "DNS_HDR_SIZE", "dsc": "I", "val": 12}, {"acc": 26, "nme": "NO_ERROR", "dsc": "I", "val": 0}, {"acc": 26, "nme": "FORMAT_ERROR", "dsc": "I", "val": 1}, {"acc": 26, "nme": "SERVER_FAILURE", "dsc": "I", "val": 2}, {"acc": 26, "nme": "NAME_ERROR", "dsc": "I", "val": 3}, {"acc": 26, "nme": "NOT_IMPL", "dsc": "I", "val": 4}, {"acc": 26, "nme": "REFUSED", "dsc": "I", "val": 5}, {"acc": 26, "nme": "rcodeDescription", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "DEFAULT_PORT", "dsc": "I", "val": 53}, {"acc": 26, "nme": "TRANSACTION_ID_BOUND", "dsc": "I", "val": 65536}, {"acc": 26, "nme": "MIN_TIMEOUT", "dsc": "I", "val": 50}, {"acc": 26, "nme": "random", "dsc": "Ljava/security/SecureRandom;"}, {"acc": 2, "nme": "servers", "dsc": "[Ljava/net/InetAddress;"}, {"acc": 2, "nme": "serverPorts", "dsc": "[I"}, {"acc": 2, "nme": "timeout", "dsc": "I"}, {"acc": 2, "nme": "retries", "dsc": "I"}, {"acc": 18, "nme": "udpChannelLock", "dsc": "Ljava/util/concurrent/locks/ReentrantLock;"}, {"acc": 18, "nme": "udpChannelSelector", "dsc": "Ljava/nio/channels/Selector;"}, {"acc": 18, "nme": "selector<PERSON><PERSON><PERSON>", "dsc": "Ljava/lang/ref/Cleaner$Cleanable;"}, {"acc": 26, "nme": "factory", "dsc": "Lcom/sun/jndi/dns/DNSDatagramChannelFactory;"}, {"acc": 2, "nme": "reqs", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;Lcom/sun/jndi/dns/ResourceRecord;>;"}, {"acc": 2, "nme": "resps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;[B>;"}, {"acc": 2, "nme": "queuesLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "debug", "dsc": "Z", "val": 0}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/com/sun/jndi/dns/DnsUrl.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/dns/DnsUrl", "super": "com/sun/jndi/toolkit/url/Uri", "mthds": [{"nme": "fromList", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lcom/sun/jndi/dns/DnsUrl;", "exs": ["java/net/MalformedURLException"]}, {"nme": "parseMode", "acc": 4, "dsc": "()Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"nme": "isSchemeOnly", "acc": 20, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "checkSchemeOnly", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "newInvalidURISchemeException", "acc": 20, "dsc": "(Lja<PERSON>/lang/String;)Ljava/net/MalformedURLException;"}, {"nme": "isDnsSchemeOnly", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "validateURI", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "exs": ["java/net/URISyntaxException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/net/MalformedURLException"]}, {"nme": "getDomain", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PARSE_MODE_PROP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.dnsURLParsing"}, {"acc": 26, "nme": "DEFAULT_PARSE_MODE", "dsc": "Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"acc": 25, "nme": "PARSE_MODE", "dsc": "Lcom/sun/jndi/toolkit/url/Uri$ParseMode;"}, {"acc": 2, "nme": "domain", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/jndi/dns/Header.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/Header", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([BI)V", "exs": ["javax/naming/NamingException"]}, {"nme": "decode", "acc": 2, "dsc": "([BI)V", "exs": ["javax/naming/NamingException"]}, {"nme": "getShort", "acc": 10, "dsc": "([BI)I"}], "flds": [{"acc": 24, "nme": "HEADER_SIZE", "dsc": "I", "val": 12}, {"acc": 24, "nme": "QR_BIT", "dsc": "S", "val": -32768}, {"acc": 24, "nme": "OPCODE_MASK", "dsc": "S", "val": 30720}, {"acc": 24, "nme": "OPCODE_SHIFT", "dsc": "I", "val": 11}, {"acc": 24, "nme": "AA_BIT", "dsc": "S", "val": 1024}, {"acc": 24, "nme": "TC_BIT", "dsc": "S", "val": 512}, {"acc": 24, "nme": "RD_BIT", "dsc": "S", "val": 256}, {"acc": 24, "nme": "RA_BIT", "dsc": "S", "val": 128}, {"acc": 24, "nme": "RCODE_MASK", "dsc": "S", "val": 15}, {"acc": 0, "nme": "xid", "dsc": "I"}, {"acc": 0, "nme": "query", "dsc": "Z"}, {"acc": 0, "nme": "opcode", "dsc": "I"}, {"acc": 0, "nme": "authoritative", "dsc": "Z"}, {"acc": 0, "nme": "truncated", "dsc": "Z"}, {"acc": 0, "nme": "recursionDesired", "dsc": "Z"}, {"acc": 0, "nme": "recursionAvail", "dsc": "Z"}, {"acc": 0, "nme": "rcode", "dsc": "I"}, {"acc": 0, "nme": "numQuestions", "dsc": "I"}, {"acc": 0, "nme": "numAnswers", "dsc": "I"}, {"acc": 0, "nme": "numAuthorities", "dsc": "I"}, {"acc": 0, "nme": "numAdditionals", "dsc": "I"}]}, "classes/com/sun/jndi/dns/CT.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/CT", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}], "flds": [{"acc": 0, "nme": "rrclass", "dsc": "I"}, {"acc": 0, "nme": "rrtype", "dsc": "I"}]}, "classes/com/sun/jndi/dns/Packet.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/Packet", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V"}, {"nme": "<init>", "acc": 0, "dsc": "([BI)V"}, {"nme": "putInt", "acc": 0, "dsc": "(II)V"}, {"nme": "putShort", "acc": 0, "dsc": "(II)V"}, {"nme": "putByte", "acc": 0, "dsc": "(II)V"}, {"nme": "putBytes", "acc": 0, "dsc": "([BIII)V"}, {"nme": "length", "acc": 0, "dsc": "()I"}, {"nme": "getData", "acc": 0, "dsc": "()[B"}], "flds": [{"acc": 0, "nme": "buf", "dsc": "[B"}]}, "classes/com/sun/jndi/dns/Tcp$SocketReadOp.class": {"ver": 65, "acc": 1536, "nme": "com/sun/jndi/dns/Tcp$SocketReadOp", "super": "java/lang/Object", "mthds": [{"nme": "read", "acc": 1025, "dsc": "()I", "exs": ["java/io/IOException"]}], "flds": []}, "classes/com/sun/jndi/dns/DnsContext.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/dns/DnsContext", "super": "com/sun/jndi/toolkit/ctx/ComponentDirContext", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Lja<PERSON>/util/Hashtable;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Ljava/util/Hashtable<**>;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsContext;Lcom/sun/jndi/dns/DnsName;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsContext;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "p_getEnvironment", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "()<PERSON><PERSON><PERSON>/util/Hashtable<**>;"}, {"nme": "getEnvironment", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "()<PERSON><PERSON><PERSON>/util/Hashtable<**>;", "exs": ["javax/naming/NamingException"]}, {"nme": "addToEnvironment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "removeFromEnvironment", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "setProviderUrl", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initFromEnvironment", "acc": 2, "dsc": "()V", "exs": ["javax/naming/directory/InvalidAttributeIdentifierException"]}, {"nme": "getLookupCT", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jndi/dns/CT;", "exs": ["javax/naming/directory/InvalidAttributeIdentifierException"]}, {"nme": "c_lookup", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_lookupLink", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljava/lang/Object;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_list", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration<Ljavax/naming/NameClassPair;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_listBindings", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration<Ljavax/naming/Binding;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_bind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_rebind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_unbind", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_rename", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_createSubcontext", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/Context;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_destroySubcontext", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_getNameP<PERSON>er", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NameParser;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_bind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;Ljavax/naming/directory/Attributes;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_rebind", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/Object;Ljavax/naming/directory/Attributes;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_createSubcontext", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/directory/Attributes;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/directory/DirContext;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_getAttributes", "acc": 1, "dsc": "(Ljavax/naming/Name;[Ljava/lang/String;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/directory/Attributes;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_modifyAttributes", "acc": 1, "dsc": "(Ljavax/naming/Name;ILjavax/naming/directory/Attributes;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_modifyAttributes", "acc": 1, "dsc": "(Ljavax/naming/Name;[Ljavax/naming/directory/ModificationItem;Lcom/sun/jndi/toolkit/ctx/Continuation;)V", "exs": ["javax/naming/NamingException"]}, {"nme": "c_search", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/directory/Attributes;[Ljava/lang/String;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;Ljavax/naming/directory/Attributes;[Ljava/lang/String;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration<Ljavax/naming/directory/SearchResult;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_search", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/String;Ljavax/naming/directory/SearchControls;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;Ljava/lang/String;Ljavax/naming/directory/SearchControls;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration<Ljavax/naming/directory/SearchResult;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_search", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljava/lang/String;[Ljava/lang/Object;Ljavax/naming/directory/SearchControls;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration;", "sig": "(Ljavax/naming/Name;Ljava/lang/String;[Ljava/lang/Object;Ljavax/naming/directory/SearchControls;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/NamingEnumeration<Ljavax/naming/directory/SearchResult;>;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_getSchema", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/directory/DirContext;", "exs": ["javax/naming/NamingException"]}, {"nme": "c_getSchemaClassDefinition", "acc": 1, "dsc": "(Ljavax/naming/Name;Lcom/sun/jndi/toolkit/ctx/Continuation;)Ljavax/naming/directory/DirContext;", "exs": ["javax/naming/NamingException"]}, {"nme": "getNameInNamespace", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "composeName", "acc": 1, "dsc": "(Ljavax/naming/Name;Ljavax/naming/Name;)Ljavax/naming/Name;", "exs": ["javax/naming/NamingException"]}, {"nme": "getResolver", "acc": 34, "dsc": "()Lcom/sun/jndi/dns/Resolver;", "exs": ["javax/naming/NamingException"]}, {"nme": "fullyQualify", "acc": 0, "dsc": "(Ljavax/naming/Name;)Lcom/sun/jndi/dns/DnsName;", "exs": ["javax/naming/NamingException"]}, {"nme": "rrsToAttrs", "acc": 10, "dsc": "(Lcom/sun/jndi/dns/ResourceRecords;[Lcom/sun/jndi/dns/CT;)Ljavax/naming/directory/Attributes;"}, {"nme": "classAndTypeMatch", "acc": 10, "dsc": "(II[Lcom/sun/jndi/dns/CT;)Z"}, {"nme": "toAttrId", "acc": 10, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "fromAttrId", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jndi/dns/CT;", "exs": ["javax/naming/directory/InvalidAttributeIdentifierException"]}, {"nme": "attrIdsToClassesAndTypes", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Lcom/sun/jndi/dns/CT;", "exs": ["javax/naming/directory/InvalidAttributeIdentifierException"]}, {"nme": "getClassAndTypeToQuery", "acc": 10, "dsc": "([Lcom/sun/jndi/dns/CT;)Lcom/sun/jndi/dns/CT;"}, {"nme": "getNameNode", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsName;)Lcom/sun/jndi/dns/NameNode;", "exs": ["javax/naming/NamingException"]}, {"nme": "populateZone", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/ZoneNode;Lcom/sun/jndi/dns/DnsName;)Lcom/sun/jndi/dns/NameNode;", "exs": ["javax/naming/NamingException"]}, {"nme": "isZoneCurrent", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/ZoneNode;Lcom/sun/jndi/dns/DnsName;)Z", "exs": ["javax/naming/NamingException"]}, {"nme": "dprint", "acc": 26, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "domain", "dsc": "Lcom/sun/jndi/dns/DnsName;"}, {"acc": 0, "nme": "environment", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "<PERSON><PERSON><PERSON>/util/Hashtable<Ljava/lang/Object;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "envShared", "dsc": "Z"}, {"acc": 2, "nme": "parentIsDns", "dsc": "Z"}, {"acc": 2, "nme": "servers", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "resolver", "dsc": "Lcom/sun/jndi/dns/Resolver;"}, {"acc": 2, "nme": "authoritative", "dsc": "Z"}, {"acc": 2, "nme": "recursion", "dsc": "Z"}, {"acc": 2, "nme": "timeout", "dsc": "I"}, {"acc": 2, "nme": "retries", "dsc": "I"}, {"acc": 24, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/naming/NameParser;"}, {"acc": 26, "nme": "DEFAULT_INIT_TIMEOUT", "dsc": "I", "val": 1000}, {"acc": 26, "nme": "DEFAULT_RETRIES", "dsc": "I", "val": 4}, {"acc": 26, "nme": "INIT_TIMEOUT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.dns.timeout.initial"}, {"acc": 26, "nme": "RETRIES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.dns.timeout.retries"}, {"acc": 2, "nme": "lookupCT", "dsc": "Lcom/sun/jndi/dns/CT;"}, {"acc": 26, "nme": "LOOKUP_ATTR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.dns.lookup.attr"}, {"acc": 26, "nme": "RECURSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.jndi.dns.recursion"}, {"acc": 26, "nme": "ANY", "dsc": "I", "val": 255}, {"acc": 26, "nme": "zoneTree", "dsc": "Lcom/sun/jndi/dns/ZoneNode;"}, {"acc": 26, "nme": "debug", "dsc": "Z", "val": 0}]}, "classes/com/sun/jndi/dns/ResourceRecords.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/ResourceRecords", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([BILcom/sun/jndi/dns/Header;Z)V", "exs": ["javax/naming/NamingException"]}, {"nme": "getFirstAnsType", "acc": 0, "dsc": "()I"}, {"nme": "getLastAnsType", "acc": 0, "dsc": "()I"}, {"nme": "add", "acc": 0, "dsc": "([BILcom/sun/jndi/dns/Header;)V", "exs": ["javax/naming/NamingException"]}], "flds": [{"acc": 0, "nme": "question", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Lcom/sun/jndi/dns/ResourceRecord;>;"}, {"acc": 0, "nme": "answer", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Lcom/sun/jndi/dns/ResourceRecord;>;"}, {"acc": 0, "nme": "authority", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Lcom/sun/jndi/dns/ResourceRecord;>;"}, {"acc": 0, "nme": "additional", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Lcom/sun/jndi/dns/ResourceRecord;>;"}, {"acc": 0, "nme": "zoneXfer", "dsc": "Z"}]}, "classes/com/sun/jndi/dns/DNSDatagramChannelFactory$EphemeralPortRange.class": {"ver": 65, "acc": 48, "nme": "com/sun/jndi/dns/DNSDatagramChannelFactory$EphemeralPortRange", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "LOWER", "dsc": "I"}, {"acc": 24, "nme": "UPPER", "dsc": "I"}, {"acc": 24, "nme": "RANGE", "dsc": "I"}]}, "classes/com/sun/jndi/dns/DnsName.class": {"ver": 65, "acc": 49, "nme": "com/sun/jndi/dns/DnsName", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/jndi/dns/DnsName;II)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isHostName", "acc": 1, "dsc": "()Z"}, {"nme": "getOctets", "acc": 1, "dsc": "()S"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "startsWith", "acc": 1, "dsc": "(Ljavax/naming/Name;)Z"}, {"nme": "endsWith", "acc": 1, "dsc": "(Ljavax/naming/Name;)Z"}, {"nme": "get", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAll", "acc": 1, "dsc": "()Ljava/util/Enumeration;", "sig": "()Ljava/util/Enumeration<Ljava/lang/String;>;"}, {"nme": "getPrefix", "acc": 1, "dsc": "(I)Ljavax/naming/Name;"}, {"nme": "getSuffix", "acc": 1, "dsc": "(I)Ljavax/naming/Name;"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "remove", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/naming/Name;", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "add", "acc": 1, "dsc": "(ILjava/lang/String;)Ljavax/naming/Name;", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "addAll", "acc": 1, "dsc": "(Ljavax/naming/Name;)Ljavax/naming/Name;", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "addAll", "acc": 1, "dsc": "(ILjavax/naming/Name;)Ljavax/naming/Name;", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "hasRootLabel", "acc": 0, "dsc": "()Z"}, {"nme": "compareRange", "acc": 2, "dsc": "(IILjavax/naming/Name;)I"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "parse", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "getEscapedOctet", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)C", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "verify<PERSON>abel", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["javax/naming/InvalidNameException"]}, {"nme": "isHostNameLabel", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "isHostNameChar", "acc": 10, "dsc": "(C)Z"}, {"nme": "isDigit", "acc": 10, "dsc": "(C)Z"}, {"nme": "escape", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "compare<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "key<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 2, "nme": "domain", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "labels", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/String;>;"}, {"acc": 2, "nme": "octets", "dsc": "S"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 7040187611324710271}]}, "classes/com/sun/jndi/dns/ResourceRecord.class": {"ver": 65, "acc": 33, "nme": "com/sun/jndi/dns/ResourceRecord", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([BIIZZ)V", "exs": ["javax/naming/CommunicationException"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()Lcom/sun/jndi/dns/DnsName;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "getType", "acc": 1, "dsc": "()I"}, {"nme": "getRrclass", "acc": 1, "dsc": "()I"}, {"nme": "getRdata", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getTypeName", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getRrclassName", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRrclass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "valueToName", "acc": 10, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "nameToValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)I"}, {"nme": "compareSerialNumbers", "acc": 9, "dsc": "(JJ)I"}, {"nme": "decode", "acc": 2, "dsc": "(Z)V", "exs": ["javax/naming/CommunicationException"]}, {"nme": "getUByte", "acc": 2, "dsc": "(I)I"}, {"nme": "getUShort", "acc": 2, "dsc": "(I)I"}, {"nme": "getInt", "acc": 2, "dsc": "(I)I"}, {"nme": "getUInt", "acc": 2, "dsc": "(I)J"}, {"nme": "decodeName", "acc": 2, "dsc": "(I)Lcom/sun/jndi/dns/DnsName;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeName", "acc": 2, "dsc": "(ILcom/sun/jndi/dns/DnsName;)I", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeRdata", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeMx", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeSoa", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeSrv", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeNaptr", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["javax/naming/CommunicationException"]}, {"nme": "decodeTxt", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "decodeHinfo", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "decodeCharString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuffer;)I"}, {"nme": "decodeA", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "decodeAAAA", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "dprint", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "TYPE_A", "dsc": "I", "val": 1}, {"acc": 24, "nme": "TYPE_NS", "dsc": "I", "val": 2}, {"acc": 24, "nme": "TYPE_CNAME", "dsc": "I", "val": 5}, {"acc": 24, "nme": "TYPE_SOA", "dsc": "I", "val": 6}, {"acc": 24, "nme": "TYPE_PTR", "dsc": "I", "val": 12}, {"acc": 24, "nme": "TYPE_HINFO", "dsc": "I", "val": 13}, {"acc": 24, "nme": "TYPE_MX", "dsc": "I", "val": 15}, {"acc": 24, "nme": "TYPE_TXT", "dsc": "I", "val": 16}, {"acc": 24, "nme": "TYPE_AAAA", "dsc": "I", "val": 28}, {"acc": 24, "nme": "TYPE_SRV", "dsc": "I", "val": 33}, {"acc": 24, "nme": "TYPE_NAPTR", "dsc": "I", "val": 35}, {"acc": 24, "nme": "QTYPE_AXFR", "dsc": "I", "val": 252}, {"acc": 24, "nme": "QTYPE_STAR", "dsc": "I", "val": 255}, {"acc": 24, "nme": "rrTypeNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "CLASS_INTERNET", "dsc": "I", "val": 1}, {"acc": 24, "nme": "CLASS_HESIOD", "dsc": "I", "val": 2}, {"acc": 24, "nme": "QCLASS_STAR", "dsc": "I", "val": 255}, {"acc": 24, "nme": "rrClassNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "MAXIMUM_COMPRESSION_REFERENCES", "dsc": "I", "val": 16}, {"acc": 0, "nme": "msg", "dsc": "[B"}, {"acc": 0, "nme": "msgLen", "dsc": "I"}, {"acc": 0, "nme": "qSection", "dsc": "Z"}, {"acc": 0, "nme": "offset", "dsc": "I"}, {"acc": 0, "nme": "r<PERSON>n", "dsc": "I"}, {"acc": 0, "nme": "name", "dsc": "Lcom/sun/jndi/dns/DnsName;"}, {"acc": 0, "nme": "rrtype", "dsc": "I"}, {"acc": 0, "nme": "rrtypeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "rrclass", "dsc": "I"}, {"acc": 0, "nme": "rrclassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "ttl", "dsc": "I"}, {"acc": 0, "nme": "rdlen", "dsc": "I"}, {"acc": 0, "nme": "rdata", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "debug", "dsc": "Z", "val": 0}]}, "classes/com/sun/jndi/dns/DNSDatagramChannelFactory$PortHistory.class": {"ver": 65, "acc": 48, "nme": "com/sun/jndi/dns/DNSDatagramChannelFactory$PortHistory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ILjava/util/Random;)V"}, {"nme": "contains", "acc": 1, "dsc": "(I)Z"}, {"nme": "add", "acc": 1, "dsc": "(I)Z"}, {"nme": "offer", "acc": 1, "dsc": "(I)Z"}], "flds": [{"acc": 16, "nme": "capacity", "dsc": "I"}, {"acc": 16, "nme": "ports", "dsc": "[I"}, {"acc": 16, "nme": "random", "dsc": "<PERSON><PERSON><PERSON>/util/Random;"}, {"acc": 0, "nme": "index", "dsc": "I"}]}, "classes/com/sun/jndi/dns/NameClassPairEnumeration.class": {"ver": 65, "acc": 48, "nme": "com/sun/jndi/dns/NameClassPairEnumeration", "super": "com/sun/jndi/dns/BaseNameClassPairEnumeration", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsContext;<PERSON><PERSON><PERSON>/util/Hashtable;)V", "sig": "(Lcom/sun/jndi/dns/DnsContext;Ljava/util/Hashtable<Ljava/lang/String;Lcom/sun/jndi/dns/NameNode;>;)V"}, {"nme": "next", "acc": 1, "dsc": "()Ljavax/naming/NameClassPair;", "exs": ["javax/naming/NamingException"]}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/naming/NamingException"]}], "flds": []}, "classes/com/sun/jndi/dns/NameNode.class": {"ver": 65, "acc": 32, "nme": "com/sun/jndi/dns/NameNode", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "newNameNode", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jndi/dns/NameNode;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "depth", "acc": 0, "dsc": "()I"}, {"nme": "isZoneCut", "acc": 0, "dsc": "()Z"}, {"nme": "setZoneCut", "acc": 0, "dsc": "(Z)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "()Ljava/util/Hashtable<Ljava/lang/String;Lcom/sun/jndi/dns/NameNode;>;"}, {"nme": "get", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Lcom/sun/jndi/dns/NameNode;"}, {"nme": "get", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;I)Lcom/sun/jndi/dns/NameNode;"}, {"nme": "add", "acc": 0, "dsc": "(Lcom/sun/jndi/dns/DnsName;I)Lcom/sun/jndi/dns/NameNode;"}], "flds": [{"acc": 2, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "children", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Ljava/util/Hashtable<Ljava/lang/String;Lcom/sun/jndi/dns/NameNode;>;"}, {"acc": 2, "nme": "isZoneCut", "dsc": "Z"}, {"acc": 2, "nme": "depth", "dsc": "I"}]}}}}