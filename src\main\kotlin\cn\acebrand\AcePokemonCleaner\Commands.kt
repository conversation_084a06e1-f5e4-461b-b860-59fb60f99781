package cn.acebrand.AcePokemonCleaner

import org.bukkit.ChatColor
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player
import java.text.SimpleDateFormat
import java.util.*

/**
 * 命令处理器
 * 处理插件的所有命令
 */
class Commands(
    private val plugin: AcePokemonCleaner,
    private val configManager: ConfigManager,
    private val pokemonCleaner: PokemonCleaner,
    private val itemExclusionGUI: ItemExclusionGUI
) : CommandExecutor, TabCompleter {

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    override fun onCommand(
        sender: CommandSender,
        command: Command,
        label: String,
        args: Array<out String>
    ): Boolean {

        // 检查权限
        if (!sender.hasPermission("pokemoncleaner.admin")) {
            sender.sendMessage("${ChatColor.RED}你没有权限使用此命令！")
            return true
        }

        if (args.isEmpty()) {
            sendHelp(sender)
            return true
        }

        when (args[0].lowercase()) {
            "reload" -> handleReload(sender)
            "clean" -> handleClean(sender)
            "status" -> handleStatus(sender)
            "debug" -> handleDebug(sender)
            "items", "gui" -> handleItemsGUI(sender)
            "help" -> sendHelp(sender)
            else -> {
                sender.sendMessage("${ChatColor.RED}未知的子命令: ${args[0]}")
                sendHelp(sender)
            }
        }

        return true
    }

    /**
     * 处理重载命令
     */
    private fun handleReload(sender: CommandSender) {
        try {
            sender.sendMessage("${ChatColor.YELLOW}正在重载配置...")
            plugin.reloadConfiguration()
            sender.sendMessage("${ChatColor.GREEN}配置重载完成！")

            // 显示新的配置信息
            sender.sendMessage("${ChatColor.AQUA}当前配置:")
            sender.sendMessage("${ChatColor.GRAY}  清理间隔: ${ChatColor.WHITE}${configManager.cleanInterval} 秒")
            sender.sendMessage("${ChatColor.GRAY}  启用世界: ${ChatColor.WHITE}${configManager.enabledWorlds.joinToString(", ")}")
            sender.sendMessage("${ChatColor.GRAY}  排除精灵: ${ChatColor.WHITE}${configManager.excludedPokemon.joinToString(", ").ifEmpty { "无" }}")

        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}重载配置时发生错误: ${e.message}")
            plugin.logger.severe("重载配置时发生错误: ${e.message}")
        }
    }

    /**
     * 处理手动清理命令
     */
    private fun handleClean(sender: CommandSender) {
        sender.sendMessage("${ChatColor.YELLOW}正在执行手动清理...")

        try {
            val result = pokemonCleaner.performClean()

            if (result.success) {
                sender.sendMessage("${ChatColor.GREEN}清理完成！")
                sender.sendMessage("${ChatColor.GRAY}  精灵清理数量: ${ChatColor.WHITE}${result.pokemonCleanedCount} 只")
                sender.sendMessage("${ChatColor.GRAY}  原版生物清理数量: ${ChatColor.WHITE}${result.vanillaMobCleanedCount} 只")
                sender.sendMessage("${ChatColor.GRAY}  掉落物清理数量: ${ChatColor.WHITE}${result.itemCleanedCount} 个")
                sender.sendMessage("${ChatColor.GRAY}  总清理数量: ${ChatColor.WHITE}${result.totalCleanedCount} 个")
                sender.sendMessage("${ChatColor.GRAY}  耗时: ${ChatColor.WHITE}${result.duration} 毫秒")

                if (result.worldResults.isNotEmpty()) {
                    sender.sendMessage("${ChatColor.GRAY}  各世界清理情况:")
                    result.worldResults.forEach { (world, worldResult) ->
                        sender.sendMessage("${ChatColor.GRAY}    $world: ${ChatColor.WHITE}${worldResult.pokemonCount} 只精灵, ${worldResult.vanillaMobCount} 只原版生物, ${worldResult.itemCount} 个掉落物")
                    }
                }

                if (result.totalCleanedCount >= configManager.maxCleanPerRun) {
                    sender.sendMessage("${ChatColor.YELLOW}注意: 已达到单次清理上限 (${configManager.maxCleanPerRun})")
                }
            } else {
                sender.sendMessage("${ChatColor.RED}清理失败: ${result.error ?: "未知错误"}")
            }

        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}执行清理时发生错误: ${e.message}")
            plugin.logger.severe("执行手动清理时发生错误: ${e.message}")
        }
    }

    /**
     * 处理状态查询命令
     */
    private fun handleStatus(sender: CommandSender) {
        try {
            // 创建简单的统计信息
            val stats = CleanStats(
                totalCleaned = 0, // 这里可以添加统计跟踪
                lastCleanTime = System.currentTimeMillis(),
                isRunning = true
            )

            sender.sendMessage("${ChatColor.GOLD}=== AcePokemonCleaner 状态 ===")
            sender.sendMessage("${ChatColor.GRAY}插件版本: ${ChatColor.WHITE}${plugin.description.version}")
            sender.sendMessage("${ChatColor.GRAY}运行状态: ${ChatColor.WHITE}${if (stats.isRunning) "${ChatColor.GREEN}运行中" else "${ChatColor.RED}已停止"}")
            sender.sendMessage("${ChatColor.GRAY}总清理数量: ${ChatColor.WHITE}${stats.totalCleaned} 只精灵")

            if (stats.lastCleanTime > 0) {
                val lastCleanDate = Date(stats.lastCleanTime)
                sender.sendMessage("${ChatColor.GRAY}上次清理时间: ${ChatColor.WHITE}${dateFormat.format(lastCleanDate)}")
            } else {
                sender.sendMessage("${ChatColor.GRAY}上次清理时间: ${ChatColor.WHITE}从未执行")
            }

            sender.sendMessage("${ChatColor.GOLD}=== 当前配置 ===")
            sender.sendMessage("${ChatColor.GRAY}清理间隔: ${ChatColor.WHITE}${configManager.cleanInterval} 秒")
            sender.sendMessage("${ChatColor.GRAY}启用世界: ${ChatColor.WHITE}${configManager.enabledWorlds.joinToString(", ")}")
            sender.sendMessage("${ChatColor.GRAY}最大距离: ${ChatColor.WHITE}${configManager.maxDistance} 格")
            sender.sendMessage("${ChatColor.GRAY}精灵最小年龄: ${ChatColor.WHITE}${configManager.minAge} 秒")
            sender.sendMessage("${ChatColor.GRAY}只清理野生: ${ChatColor.WHITE}${if (configManager.cleanOnlyWild) "是" else "否"}")
            sender.sendMessage("${ChatColor.GRAY}每次最大清理: ${ChatColor.WHITE}${configManager.maxCleanPerRun} 只")

            sender.sendMessage("${ChatColor.GOLD}=== 原版生物清理配置 ===")
            sender.sendMessage("${ChatColor.GRAY}启用原版生物清理: ${ChatColor.WHITE}${if (configManager.enableVanillaMobCleaning) "是" else "否"}")
            if (configManager.enableVanillaMobCleaning) {
                sender.sendMessage("${ChatColor.GRAY}原版生物最小年龄: ${ChatColor.WHITE}${configManager.vanillaMobMinAge} 秒")
                sender.sendMessage("${ChatColor.GRAY}原版生物每次最大清理: ${ChatColor.WHITE}${configManager.vanillaMobMaxCleanPerRun} 只")
            }

            if (configManager.excludedPokemon.isNotEmpty()) {
                sender.sendMessage("${ChatColor.GRAY}排除精灵: ${ChatColor.WHITE}${configManager.excludedPokemon.joinToString(", ")}")
            }

            if (configManager.excludedVanillaMobs.isNotEmpty()) {
                sender.sendMessage("${ChatColor.GRAY}排除原版生物: ${ChatColor.WHITE}${configManager.excludedVanillaMobs.joinToString(", ")}")
            }

        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}获取状态时发生错误: ${e.message}")
            plugin.logger.severe("获取状态时发生错误: ${e.message}")
        }
    }

    /**
     * 处理调试命令
     */
    private fun handleDebug(sender: CommandSender) {
        if (sender !is Player) {
            sender.sendMessage("${ChatColor.RED}此命令只能由玩家执行")
            return
        }

        sender.sendMessage("${ChatColor.YELLOW}正在扫描附近的精灵...")

        try {
            val nearbyEntities = sender.getNearbyEntities(50.0, 50.0, 50.0)
            val pokemonEntities = nearbyEntities.filter { pokemonCleaner.isPokemonEntityPublic(it) }

            if (pokemonEntities.isEmpty()) {
                sender.sendMessage("${ChatColor.GRAY}附近没有发现精灵实体")

                // 显示所有附近实体用于调试
                sender.sendMessage("${ChatColor.YELLOW}附近的所有实体:")
                nearbyEntities.take(10).forEachIndexed { index, entity ->
                    sender.sendMessage("${ChatColor.GRAY}${index + 1}. ${entity.type.name} (${entity.javaClass.simpleName})")
                }
                return
            }

            sender.sendMessage("${ChatColor.GREEN}发现 ${pokemonEntities.size} 只精灵:")

            pokemonEntities.take(5).forEachIndexed { index, entity ->
                val entityInfo = pokemonCleaner.getPokemonEntityInfoPublic(entity)
                val isExcluded = pokemonCleaner.isPokemonExcludedPublic(entity)
                val excludeStatus = if (isExcluded) "${ChatColor.RED}[排除]" else "${ChatColor.GREEN}[可清理]"

                sender.sendMessage("${ChatColor.GRAY}${index + 1}. $excludeStatus ${ChatColor.WHITE}$entityInfo")

                // 显示详细调试信息
                if (index == 0) { // 只显示第一个精灵的详细信息
                    sender.sendMessage("${ChatColor.YELLOW}详细信息:")
                    val detailedInfo = pokemonCleaner.getDetailedPokemonInfo(entity)
                    detailedInfo.split("\n").forEach { line ->
                        sender.sendMessage("${ChatColor.GRAY}  $line")
                    }
                }
            }

            // 显示排除配置
            sender.sendMessage("${ChatColor.YELLOW}当前排除配置:")
            if (configManager.excludedPokemon.isEmpty()) {
                sender.sendMessage("${ChatColor.GRAY}  (无排除配置)")
            } else {
                configManager.excludedPokemon.forEach { excluded ->
                    sender.sendMessage("${ChatColor.GRAY}  - $excluded")
                }
            }

        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}调试时发生错误: ${e.message}")
            plugin.logger.warning("调试命令执行错误: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 处理掉落物管理GUI命令
     */
    private fun handleItemsGUI(sender: CommandSender) {
        if (sender !is Player) {
            sender.sendMessage("${ChatColor.RED}此命令只能由玩家执行！")
            return
        }

        // 检查管理员权限
        if (!sender.hasPermission("pokemoncleaner.admin") && !sender.isOp) {
            sender.sendMessage("${ChatColor.RED}只有管理员才能管理掉落物排除列表！")
            return
        }

        try {
            itemExclusionGUI.openGUI(sender)
        } catch (e: Exception) {
            sender.sendMessage("${ChatColor.RED}打开掉落物管理GUI时发生错误: ${e.message}")
            plugin.logger.severe("打开掉落物管理GUI时发生错误: ${e.message}")
        }
    }

    /**
     * 发送帮助信息
     */
    private fun sendHelp(sender: CommandSender) {
        sender.sendMessage("${ChatColor.GOLD}=== AcePokemonCleaner 命令帮助 ===")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner reload ${ChatColor.GRAY}- 重载配置文件")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner clean ${ChatColor.GRAY}- 手动执行一次清理")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner status ${ChatColor.GRAY}- 查看插件状态和统计信息")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner debug ${ChatColor.GRAY}- 调试附近的精灵信息")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner items ${ChatColor.GRAY}- 打开掉落物管理GUI")
        sender.sendMessage("${ChatColor.YELLOW}/pokemoncleaner help ${ChatColor.GRAY}- 显示此帮助信息")
    }

    override fun onTabComplete(
        sender: CommandSender,
        command: Command,
        alias: String,
        args: Array<out String>
    ): List<String>? {

        if (!sender.hasPermission("pokemoncleaner.admin")) {
            return emptyList()
        }

        return when (args.size) {
            1 -> {
                val subCommands = listOf("reload", "clean", "status", "debug", "items", "gui", "help")
                subCommands.filter { it.startsWith(args[0].lowercase()) }
            }
            else -> emptyList()
        }
    }
}

/**
 * 清理统计数据类
 */
data class CleanStats(
    val totalCleaned: Int,
    val lastCleanTime: Long,
    val isRunning: Boolean
)
