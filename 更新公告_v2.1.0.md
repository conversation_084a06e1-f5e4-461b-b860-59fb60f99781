# 🎉 AcePokemonCleaner v2.1.0 更新公告

## 📅 发布日期
2025年7月4日

## 🆕 新增功能

### 🛡️ 原版生物命名牌保护功能
- **新增配置项**：`protect-named-vanilla-mobs`
- **功能描述**：保护被命名牌命名的原版生物不被清理系统清理
- **默认状态**：启用（`true`）
- **适用场景**：
  - 保护被驯服的宠物（猫、狼、马等）
  - 保护特殊用途的生物（村民、铁傀儡等）
  - 保护装饰性生物和农场动物
  - 保护玩家特别命名的任何原版生物

### 📝 详细日志记录
- 新增命名牌保护的详细日志输出
- 支持空命名牌的特殊显示格式
- 错误处理和调试信息优化

## ⚙️ 配置更新

### 新增配置项
```yaml
# 是否保护被命名牌命名的原版生物
# 启用后，任何使用命名牌命名的原版生物都不会被清理
# 这包括被驯服的动物、特殊命名的生物等
protect-named-vanilla-mobs: true
```

### 配置说明
- `true`：启用保护，有命名牌的原版生物不会被清理
- `false`：禁用保护，有命名牌的原版生物也可能被清理

## 🔧 技术改进

### 代码优化
- 优化原版生物清理逻辑
- 增强错误处理机制
- 改进日志记录系统

### 性能提升
- 命名牌检查采用O(1)时间复杂度
- 不增加额外内存开销
- 对整体清理性能影响极小

## 📋 使用指南

### 管理员设置
1. **启用功能**：
   ```yaml
   protect-named-vanilla-mobs: true
   ```

2. **禁用功能**：
   ```yaml
   protect-named-vanilla-mobs: false
   ```

3. **重载配置**：
   ```
   /pokemoncleaner reload
   ```

### 玩家使用
1. **制作命名牌**：
   ```
   /give @s name_tag
   ```

2. **重命名**：在铁砧上给命名牌起名（可选）

3. **使用**：右键点击要保护的原版生物

4. **效果**：该生物将不会被清理系统清理

## 🎯 功能特点

### ✨ 智能保护
- 自动检测所有有命名牌的原版生物
- 支持空字符串命名牌
- 支持特殊字符和超长名称

### 🔄 兼容性
- 完全兼容现有配置
- 不影响精灵和掉落物清理
- 与排除列表功能完全兼容

### 📊 优先级
检查顺序：死亡检查 → 排除列表检查 → **命名牌检查** → 其他条件检查

## 📖 示例场景

### 场景1：保护农场动物
```
玩家给牛使用命名牌，命名为"奶牛小白"
→ 清理系统检测到命名牌
→ 该牛不会被清理
→ 日志：原版生物有命名牌，不清理: 奶牛小白 (COW)
```

### 场景2：保护宠物
```
玩家给狼使用命名牌，命名为"我的狗狗"
→ 即使狼不在默认排除列表中
→ 由于有命名牌，不会被清理
→ 日志：原版生物有命名牌，不清理: 我的狗狗 (WOLF)
```

### 场景3：空命名牌保护
```
玩家使用命名牌但不输入名称
→ 系统仍然检测到命名牌存在
→ 该生物不会被清理
→ 日志：原版生物有命名牌，不清理: COW（空命名牌）
```

## 🛠️ 故障排除

### 常见问题
1. **命名牌不生效**
   - 检查配置：`protect-named-vanilla-mobs: true`
   - 确认正确应用命名牌到生物

2. **日志不显示**
   - 启用日志：`enable-logging: true`
   - 重载配置后测试

3. **生物仍被清理**
   - 检查是否在排除列表中（排除列表优先级更高）
   - 确认原版生物清理功能已启用

### 调试方法
1. 启用详细日志记录
2. 使用调试命令检查生物状态
3. 查看控制台错误信息

## 🔄 升级说明

### 自动升级
- 配置文件会自动添加新配置项
- 默认启用命名牌保护功能
- 无需手动修改现有配置

### 手动配置
如需自定义设置，请编辑 `config.yml`：
```yaml
protect-named-vanilla-mobs: true  # 或 false
```

## 📞 技术支持

- **QQ技术支持**：337871509
- **问题反馈**：请提供详细的错误日志和配置信息
- **功能建议**：欢迎提出改进建议

## 🎊 感谢

感谢所有用户的反馈和建议，让我们能够不断改进插件功能！

---

**AceBrand 开发团队**  
2025年7月4日
