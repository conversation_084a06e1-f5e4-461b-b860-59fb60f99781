/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.text.StringsKt
 *  net.minecraft.class_1297
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear.config.clearables;

import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.utils.CobblemonAdaptor;
import java.util.ArrayList;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;
import net.minecraft.class_1297;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0015\u0018\u00002\u00020\u0001B1\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\b\u0010\tJ\u0015\u0010\f\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\b\f\u0010\rJ\u0015\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\b\u000e\u0010\rJ\u0013\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0013\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\u0011\u0010\u0010J\u000f\u0010\u0012\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\u0014\u001a\u0004\b\u0015\u0010\u0016R(\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0006\u0010\u0017\u001a\u0004\b\u0018\u0010\u0010\"\u0004\b\u0019\u0010\u001aR(\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0007\u0010\u0017\u001a\u0004\b\u001b\u0010\u0010\"\u0004\b\u001c\u0010\u001aR\u001e\u0010\u001d\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u001d\u0010\u0017R\u001e\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u001e\u0010\u0017\u00a8\u0006\u001f"}, d2={"Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;", "", "", "enabled", "", "", "blacklist", "whitelist", "<init>", "(ZLjava/util/List;Ljava/util/List;)V", "Lnet/minecraft/class_1297;", "entity", "isEntityType", "(Lnet/minecraft/class_1297;)Z", "shouldClear", "getBlacklistedAspects", "()Ljava/util/List;", "getWhitelistedAspects", "toString", "()Ljava/lang/String;", "Z", "getEnabled", "()Z", "Ljava/util/List;", "getBlacklist", "setBlacklist", "(Ljava/util/List;)V", "getWhitelist", "setWhitelist", "blacklistedAspects", "whitelistedAspects", "SkiesClear"})
public final class CobblemonClearable {
    private final boolean enabled;
    @NotNull
    private List<String> blacklist;
    @NotNull
    private List<String> whitelist;
    @Nullable
    private transient List<String> blacklistedAspects;
    @Nullable
    private transient List<String> whitelistedAspects;

    public CobblemonClearable(boolean enabled, @NotNull List<String> blacklist, @NotNull List<String> whitelist) {
        Intrinsics.checkNotNullParameter(blacklist, (String)"blacklist");
        Intrinsics.checkNotNullParameter(whitelist, (String)"whitelist");
        this.enabled = enabled;
        this.blacklist = blacklist;
        this.whitelist = whitelist;
    }

    public /* synthetic */ CobblemonClearable(boolean bl, List list, List list2, int n, DefaultConstructorMarker defaultConstructorMarker) {
        if ((n & 1) != 0) {
            bl = true;
        }
        if ((n & 2) != 0) {
            list = CollectionsKt.emptyList();
        }
        if ((n & 4) != 0) {
            list2 = CollectionsKt.emptyList();
        }
        this(bl, list, list2);
    }

    public final boolean getEnabled() {
        return this.enabled;
    }

    @NotNull
    public final List<String> getBlacklist() {
        return this.blacklist;
    }

    public final void setBlacklist(@NotNull List<String> list) {
        Intrinsics.checkNotNullParameter(list, (String)"<set-?>");
        this.blacklist = list;
    }

    @NotNull
    public final List<String> getWhitelist() {
        return this.whitelist;
    }

    public final void setWhitelist(@NotNull List<String> list) {
        Intrinsics.checkNotNullParameter(list, (String)"<set-?>");
        this.whitelist = list;
    }

    public final boolean isEntityType(@NotNull class_1297 entity) {
        Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
        return SkiesClear.Companion.getCOBBLEMON_PRESENT() ? CobblemonAdaptor.INSTANCE.isEntityType(entity) : false;
    }

    public final boolean shouldClear(@NotNull class_1297 entity) {
        Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
        return SkiesClear.Companion.getCOBBLEMON_PRESENT() ? CobblemonAdaptor.INSTANCE.shouldClearEntity(this, entity) : false;
    }

    @NotNull
    public final List<String> getBlacklistedAspects() {
        if (this.blacklistedAspects != null) {
            List<String> list = this.blacklistedAspects;
            Intrinsics.checkNotNull(list);
            return list;
        }
        List newAspects = new ArrayList();
        for (String entry : this.blacklist) {
            if (!StringsKt.startsWith((String)entry, (String)"#aspect=", (boolean)true)) continue;
            String[] stringArray = new String[]{"="};
            List split = StringsKt.split((CharSequence)entry, (String[])stringArray, (boolean)true, (int)2);
            if (split.size() != 2) continue;
            newAspects.add(split.get(1));
        }
        this.blacklistedAspects = newAspects;
        return newAspects;
    }

    @NotNull
    public final List<String> getWhitelistedAspects() {
        if (this.whitelistedAspects != null) {
            List<String> list = this.whitelistedAspects;
            Intrinsics.checkNotNull(list);
            return list;
        }
        List newAspects = new ArrayList();
        for (String entry : this.whitelist) {
            if (!StringsKt.startsWith((String)entry, (String)"#aspect=", (boolean)true)) continue;
            String[] stringArray = new String[]{"="};
            List split = StringsKt.split((CharSequence)entry, (String[])stringArray, (boolean)true, (int)2);
            if (split.size() != 2) continue;
            newAspects.add(split.get(1));
        }
        this.whitelistedAspects = newAspects;
        return newAspects;
    }

    @NotNull
    public String toString() {
        return "CobblemonClearable(enabled=" + this.enabled + ", blacklist=" + this.blacklist + ", whitelist=" + this.whitelist + ", blacklistedAspects=" + this.blacklistedAspects + ", whitelistedAspects=" + this.whitelistedAspects + ")";
    }

    public CobblemonClearable() {
        this(false, null, null, 7, null);
    }
}

