/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.google.gson.annotations.JsonAdapter
 *  com.google.gson.annotations.SerializedName
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.collections.MapsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear.config;

import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.pokeskies.skiesclear.config.clearables.CobblemonClearable;
import com.pokeskies.skiesclear.config.clearables.EntityClearable;
import com.pokeskies.skiesclear.config.clearables.ItemClearable;
import com.pokeskies.skiesclear.utils.FlexibleListAdaptorFactory;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b!\u0018\u00002\u00020\u0001:\u0003+,-Bi\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\u0016\u001a\u0004\b\u0017\u0010\u0018R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0005\u0010\u0019\u001a\u0004\b\u001a\u0010\u001bR \u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u00068\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\b\u0010\u001c\u001a\u0004\b\u001d\u0010\u001eR\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\f\n\u0004\b\n\u0010\u001f\u001a\u0004\b \u0010!R\u0017\u0010\f\u001a\u00020\u000b8\u0006\u00a2\u0006\f\n\u0004\b\f\u0010\"\u001a\u0004\b#\u0010$R\u0019\u0010\u000e\u001a\u0004\u0018\u00010\r8\u0006\u00a2\u0006\f\n\u0004\b\u000e\u0010%\u001a\u0004\b&\u0010'R\u001a\u0010\u000f\u001a\u00020\u00028\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\u000f\u0010\u0016\u001a\u0004\b(\u0010\u0018R\u001a\u0010\u0010\u001a\u00020\u00028\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\u0010\u0010\u0016\u001a\u0004\b)\u0010\u0018R\u001a\u0010\u0011\u001a\u00020\u00028\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\u0011\u0010\u0016\u001a\u0004\b*\u0010\u0018\u00a8\u0006."}, d2={"Lcom/pokeskies/skiesclear/config/ClearConfig;", "", "", "enabled", "", "interval", "", "", "dimensions", "Lcom/pokeskies/skiesclear/config/ClearConfig$Messages;", "messages", "Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds;", "sounds", "Lcom/pokeskies/skiesclear/config/ClearConfig$Clearables;", "clearables", "clearPersistent", "clearNamed", "informDimensionsOnly", "<init>", "(ZILjava/util/List;Lcom/pokeskies/skiesclear/config/ClearConfig$Messages;Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds;Lcom/pokeskies/skiesclear/config/ClearConfig$Clearables;ZZZ)V", "toString", "()Ljava/lang/String;", "Z", "getEnabled", "()Z", "I", "getInterval", "()I", "Ljava/util/List;", "getDimensions", "()Ljava/util/List;", "Lcom/pokeskies/skiesclear/config/ClearConfig$Messages;", "getMessages", "()Lcom/pokeskies/skiesclear/config/ClearConfig$Messages;", "Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds;", "getSounds", "()Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds;", "Lcom/pokeskies/skiesclear/config/ClearConfig$Clearables;", "getClearables", "()Lcom/pokeskies/skiesclear/config/ClearConfig$Clearables;", "getClearPersistent", "getClearNamed", "getInformDimensionsOnly", "Messages", "Sounds", "Clearables", "SkiesClear"})
public final class ClearConfig {
    private final boolean enabled;
    private final int interval;
    @JsonAdapter(value=FlexibleListAdaptorFactory.class)
    @NotNull
    private final List<String> dimensions;
    @NotNull
    private final Messages messages;
    @NotNull
    private final Sounds sounds;
    @Nullable
    private final Clearables clearables;
    @SerializedName(value="clear_persistent")
    private final boolean clearPersistent;
    @SerializedName(value="clear_named")
    private final boolean clearNamed;
    @SerializedName(value="inform_dimensions_only")
    private final boolean informDimensionsOnly;

    public ClearConfig(boolean enabled, int interval, @NotNull List<String> dimensions, @NotNull Messages messages, @NotNull Sounds sounds, @Nullable Clearables clearables, boolean clearPersistent, boolean clearNamed, boolean informDimensionsOnly) {
        Intrinsics.checkNotNullParameter(dimensions, (String)"dimensions");
        Intrinsics.checkNotNullParameter((Object)messages, (String)"messages");
        Intrinsics.checkNotNullParameter((Object)sounds, (String)"sounds");
        this.enabled = enabled;
        this.interval = interval;
        this.dimensions = dimensions;
        this.messages = messages;
        this.sounds = sounds;
        this.clearables = clearables;
        this.clearPersistent = clearPersistent;
        this.clearNamed = clearNamed;
        this.informDimensionsOnly = informDimensionsOnly;
    }

    public /* synthetic */ ClearConfig(boolean bl, int n, List list, Messages messages, Sounds sounds, Clearables clearables, boolean bl2, boolean bl3, boolean bl4, int n2, DefaultConstructorMarker defaultConstructorMarker) {
        if ((n2 & 1) != 0) {
            bl = true;
        }
        if ((n2 & 2) != 0) {
            n = 300;
        }
        if ((n2 & 4) != 0) {
            list = CollectionsKt.emptyList();
        }
        if ((n2 & 8) != 0) {
            messages = new Messages(null, null, null, 7, null);
        }
        if ((n2 & 0x10) != 0) {
            sounds = new Sounds(null, null, 3, null);
        }
        if ((n2 & 0x20) != 0) {
            clearables = null;
        }
        if ((n2 & 0x40) != 0) {
            bl2 = false;
        }
        if ((n2 & 0x80) != 0) {
            bl3 = false;
        }
        if ((n2 & 0x100) != 0) {
            bl4 = true;
        }
        this(bl, n, list, messages, sounds, clearables, bl2, bl3, bl4);
    }

    public final boolean getEnabled() {
        return this.enabled;
    }

    public final int getInterval() {
        return this.interval;
    }

    @NotNull
    public final List<String> getDimensions() {
        return this.dimensions;
    }

    @NotNull
    public final Messages getMessages() {
        return this.messages;
    }

    @NotNull
    public final Sounds getSounds() {
        return this.sounds;
    }

    @Nullable
    public final Clearables getClearables() {
        return this.clearables;
    }

    public final boolean getClearPersistent() {
        return this.clearPersistent;
    }

    public final boolean getClearNamed() {
        return this.clearNamed;
    }

    public final boolean getInformDimensionsOnly() {
        return this.informDimensionsOnly;
    }

    @NotNull
    public String toString() {
        return "ClearConfig(enabled=" + this.enabled + ", interval=" + this.interval + ", dimensions=" + this.dimensions + ", messages=" + this.messages + ", sounds=" + this.sounds + ", clearables=" + this.clearables + ")";
    }

    public ClearConfig() {
        this(false, 0, null, null, null, null, false, false, false, 511, null);
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\f\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\b\u000b\u0010\fR\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\r\u001a\u0004\b\u000e\u0010\u000fR\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0005\u0010\u0010\u001a\u0004\b\u0011\u0010\u0012R\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006\u00a2\u0006\f\n\u0004\b\u0007\u0010\u0013\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006\u0016"}, d2={"Lcom/pokeskies/skiesclear/config/ClearConfig$Clearables;", "", "Lcom/pokeskies/skiesclear/config/clearables/ItemClearable;", "items", "Lcom/pokeskies/skiesclear/config/clearables/EntityClearable;", "entities", "Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;", "cobblemon", "<init>", "(Lcom/pokeskies/skiesclear/config/clearables/ItemClearable;Lcom/pokeskies/skiesclear/config/clearables/EntityClearable;Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;)V", "", "toString", "()Ljava/lang/String;", "Lcom/pokeskies/skiesclear/config/clearables/ItemClearable;", "getItems", "()Lcom/pokeskies/skiesclear/config/clearables/ItemClearable;", "Lcom/pokeskies/skiesclear/config/clearables/EntityClearable;", "getEntities", "()Lcom/pokeskies/skiesclear/config/clearables/EntityClearable;", "Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;", "getCobblemon", "()Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;", "SkiesClear"})
    public static final class Clearables {
        @Nullable
        private final ItemClearable items;
        @Nullable
        private final EntityClearable entities;
        @Nullable
        private final CobblemonClearable cobblemon;

        public Clearables(@Nullable ItemClearable items, @Nullable EntityClearable entities, @Nullable CobblemonClearable cobblemon) {
            this.items = items;
            this.entities = entities;
            this.cobblemon = cobblemon;
        }

        public /* synthetic */ Clearables(ItemClearable itemClearable, EntityClearable entityClearable, CobblemonClearable cobblemonClearable, int n, DefaultConstructorMarker defaultConstructorMarker) {
            if ((n & 1) != 0) {
                itemClearable = null;
            }
            if ((n & 2) != 0) {
                entityClearable = null;
            }
            if ((n & 4) != 0) {
                cobblemonClearable = null;
            }
            this(itemClearable, entityClearable, cobblemonClearable);
        }

        @Nullable
        public final ItemClearable getItems() {
            return this.items;
        }

        @Nullable
        public final EntityClearable getEntities() {
            return this.entities;
        }

        @Nullable
        public final CobblemonClearable getCobblemon() {
            return this.cobblemon;
        }

        @NotNull
        public String toString() {
            return "Clearables(items=" + this.items + ", entities=" + this.entities + ", cobblemon=" + this.cobblemon + ")";
        }

        public Clearables() {
            this(null, null, null, 7, null);
        }
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0002\b\u000e\u0018\u00002\u00020\u0001BC\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0002\u0012\u001a\b\u0002\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\u0003\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00030\u00020\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\u0002\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\n\u001a\u00020\u0003H\u0016\u00a2\u0006\u0004\b\n\u0010\u000bR \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00028\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\u0004\u0010\f\u001a\u0004\b\r\u0010\u000eR)\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\u0003\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00030\u00020\u00058\u0006\u00a2\u0006\f\n\u0004\b\u0006\u0010\u000f\u001a\u0004\b\u0010\u0010\u0011R \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\u00028\u0006X\u0087\u0004\u00a2\u0006\f\n\u0004\b\u0007\u0010\f\u001a\u0004\b\u0012\u0010\u000e\u00a8\u0006\u0013"}, d2={"Lcom/pokeskies/skiesclear/config/ClearConfig$Messages;", "", "", "", "clear", "", "warnings", "info", "<init>", "(Ljava/util/List;Ljava/util/Map;Ljava/util/List;)V", "toString", "()Ljava/lang/String;", "Ljava/util/List;", "getClear", "()Ljava/util/List;", "Ljava/util/Map;", "getWarnings", "()Ljava/util/Map;", "getInfo", "SkiesClear"})
    public static final class Messages {
        @JsonAdapter(value=FlexibleListAdaptorFactory.class)
        @NotNull
        private final List<String> clear;
        @NotNull
        private final Map<String, List<String>> warnings;
        @JsonAdapter(value=FlexibleListAdaptorFactory.class)
        @NotNull
        private final List<String> info;

        public Messages(@NotNull List<String> clear, @NotNull Map<String, ? extends List<String>> warnings, @NotNull List<String> info) {
            Intrinsics.checkNotNullParameter(clear, (String)"clear");
            Intrinsics.checkNotNullParameter(warnings, (String)"warnings");
            Intrinsics.checkNotNullParameter(info, (String)"info");
            this.clear = clear;
            this.warnings = warnings;
            this.info = info;
        }

        public /* synthetic */ Messages(List list, Map map, List list2, int n, DefaultConstructorMarker defaultConstructorMarker) {
            if ((n & 1) != 0) {
                list = CollectionsKt.emptyList();
            }
            if ((n & 2) != 0) {
                map = MapsKt.emptyMap();
            }
            if ((n & 4) != 0) {
                list2 = CollectionsKt.emptyList();
            }
            this(list, map, list2);
        }

        @NotNull
        public final List<String> getClear() {
            return this.clear;
        }

        @NotNull
        public final Map<String, List<String>> getWarnings() {
            return this.warnings;
        }

        @NotNull
        public final List<String> getInfo() {
            return this.info;
        }

        @NotNull
        public String toString() {
            return "Messages(clear=" + this.clear + ", warnings=" + this.warnings + ", info=" + this.info + ")";
        }

        public Messages() {
            this(null, null, null, 7, null);
        }
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\r\u0018\u00002\u00020\u0001:\u0001\u0011B)\u0012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00020\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\t\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\b\t\u0010\nR\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\u000b\u001a\u0004\b\f\u0010\rR#\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00020\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0006\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0012"}, d2={"Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds;", "", "Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds$SoundSettings;", "clear", "", "", "warnings", "<init>", "(Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds$SoundSettings;Ljava/util/Map;)V", "toString", "()Ljava/lang/String;", "Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds$SoundSettings;", "getClear", "()Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds$SoundSettings;", "Ljava/util/Map;", "getWarnings", "()Ljava/util/Map;", "SoundSettings", "SkiesClear"})
    public static final class Sounds {
        @Nullable
        private final SoundSettings clear;
        @NotNull
        private final Map<String, SoundSettings> warnings;

        public Sounds(@Nullable SoundSettings clear, @NotNull Map<String, SoundSettings> warnings) {
            Intrinsics.checkNotNullParameter(warnings, (String)"warnings");
            this.clear = clear;
            this.warnings = warnings;
        }

        public /* synthetic */ Sounds(SoundSettings soundSettings, Map map, int n, DefaultConstructorMarker defaultConstructorMarker) {
            if ((n & 1) != 0) {
                soundSettings = null;
            }
            if ((n & 2) != 0) {
                map = MapsKt.emptyMap();
            }
            this(soundSettings, map);
        }

        @Nullable
        public final SoundSettings getClear() {
            return this.clear;
        }

        @NotNull
        public final Map<String, SoundSettings> getWarnings() {
            return this.warnings;
        }

        @NotNull
        public String toString() {
            return "Sounds(clear=" + this.clear + ", warnings=" + this.warnings + ")";
        }

        public Sounds() {
            this(null, null, 3, null);
        }

        @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\r\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\t\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\b\t\u0010\nR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\u000b\u001a\u0004\b\f\u0010\nR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0005\u0010\r\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0006\u001a\u00020\u00048\u0006\u00a2\u0006\f\n\u0004\b\u0006\u0010\r\u001a\u0004\b\u0010\u0010\u000f\u00a8\u0006\u0011"}, d2={"Lcom/pokeskies/skiesclear/config/ClearConfig$Sounds$SoundSettings;", "", "", "sound", "", "volume", "pitch", "<init>", "(Ljava/lang/String;FF)V", "toString", "()Ljava/lang/String;", "Ljava/lang/String;", "getSound", "F", "getVolume", "()F", "getPitch", "SkiesClear"})
        public static final class SoundSettings {
            @NotNull
            private final String sound;
            private final float volume;
            private final float pitch;

            public SoundSettings(@NotNull String sound, float volume, float pitch) {
                Intrinsics.checkNotNullParameter((Object)sound, (String)"sound");
                this.sound = sound;
                this.volume = volume;
                this.pitch = pitch;
            }

            public /* synthetic */ SoundSettings(String string, float f, float f2, int n, DefaultConstructorMarker defaultConstructorMarker) {
                if ((n & 1) != 0) {
                    string = "";
                }
                if ((n & 2) != 0) {
                    f = 1.0f;
                }
                if ((n & 4) != 0) {
                    f2 = 1.0f;
                }
                this(string, f, f2);
            }

            @NotNull
            public final String getSound() {
                return this.sound;
            }

            public final float getVolume() {
                return this.volume;
            }

            public final float getPitch() {
                return this.pitch;
            }

            @NotNull
            public String toString() {
                return "SoundSettings(sound='" + this.sound + "', volume=" + this.volume + ", pitch=" + this.pitch + ")";
            }

            public SoundSettings() {
                this(null, 0.0f, 0.0f, 7, null);
            }
        }
    }
}

