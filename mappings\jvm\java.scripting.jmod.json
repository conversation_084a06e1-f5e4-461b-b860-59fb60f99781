{"md5": "14261c47c372da2a673f712999b80ccb", "sha2": "10d715320ea29ed3a1e6062d59195a7c8f9945f7", "sha256": "907ab6eac9d7ed8109e1fc3146f6fc441ef8789f7166716f86f152e5b0b18944", "contents": {"classes": {"classes/javax/script/AbstractScriptEngine.class": {"ver": 65, "acc": 1057, "nme": "javax/script/AbstractScriptEngine", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/script/Bindings;)V"}, {"nme": "setContext", "acc": 1, "dsc": "(Ljavax/script/ScriptContext;)V"}, {"nme": "getContext", "acc": 1, "dsc": "()Ljavax/script/ScriptContext;"}, {"nme": "getBindings", "acc": 1, "dsc": "(I)Ljavax/script/Bindings;"}, {"nme": "setB<PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/script/Bindings;I)V"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "eval", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Ljavax/script/Bindings;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljavax/script/Bindings;)Ljava/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "getScriptContext", "acc": 4, "dsc": "(Ljavax/script/Bindings;)Ljavax/script/ScriptContext;"}], "flds": [{"acc": 4, "nme": "context", "dsc": "Ljavax/script/ScriptContext;"}]}, "classes/javax/script/Invocable.class": {"ver": 65, "acc": 1537, "nme": "javax/script/Invocable", "super": "java/lang/Object", "mthds": [{"nme": "invoke<PERSON><PERSON><PERSON>", "acc": 1153, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException", "java/lang/NoSuchMethodException"]}, {"nme": "invokeFunction", "acc": 1153, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException", "java/lang/NoSuchMethodException"]}, {"nme": "getInterface", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;)TT;"}, {"nme": "getInterface", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Object;Ljava/lang/Class<TT;>;)TT;"}], "flds": []}, "classes/javax/script/ScriptEngine.class": {"ver": 65, "acc": 1537, "nme": "javax/script/ScriptEngine", "super": "java/lang/Object", "mthds": [{"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljavax/script/ScriptContext;)Ljava/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Ljavax/script/ScriptContext;)Lja<PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1025, "dsc": "(L<PERSON><PERSON>/lang/String;Ljavax/script/Bindings;)Ljava/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;Ljavax/script/Bindings;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getBindings", "acc": 1025, "dsc": "(I)Ljavax/script/Bindings;"}, {"nme": "setB<PERSON><PERSON>", "acc": 1025, "dsc": "(Ljavax/script/Bindings;I)V"}, {"nme": "createBindings", "acc": 1025, "dsc": "()Ljavax/script/Bindings;"}, {"nme": "getContext", "acc": 1025, "dsc": "()Ljavax/script/ScriptContext;"}, {"nme": "setContext", "acc": 1025, "dsc": "(Ljavax/script/ScriptContext;)V"}, {"nme": "getFactory", "acc": 1025, "dsc": "()Ljavax/script/ScriptEngineFactory;"}], "flds": [{"acc": 25, "nme": "ARGV", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.argv"}, {"acc": 25, "nme": "FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.filename"}, {"acc": 25, "nme": "ENGINE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.engine"}, {"acc": 25, "nme": "ENGINE_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.engine_version"}, {"acc": 25, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.name"}, {"acc": 25, "nme": "LANGUAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.language"}, {"acc": 25, "nme": "LANGUAGE_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.script.language_version"}]}, "classes/javax/script/ScriptEngineManager.class": {"ver": 65, "acc": 33, "nme": "javax/script/ScriptEngineManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "getServiceLoader", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON>ja<PERSON>/util/ServiceLoader;", "sig": "(Ljava/lang/ClassLoader;)Ljava/util/ServiceLoader<Ljavax/script/ScriptEngineFactory;>;"}, {"nme": "initEngines", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "setB<PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/script/Bindings;)V"}, {"nme": "getBindings", "acc": 1, "dsc": "()Ljavax/script/Bindings;"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getEngineByName", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljavax/script/ScriptEngine;"}, {"nme": "getEngineByExtension", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljavax/script/ScriptEngine;"}, {"nme": "getEngineByMimeType", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljavax/script/ScriptEngine;"}, {"nme": "getEngineBy", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;Ljava/util/function/Function;)Ljavax/script/ScriptEngine;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljavax/script/ScriptEngineFactory;>;Ljava/util/function/Function<Ljavax/script/ScriptEngineFactory;Ljava/util/List<Ljava/lang/String;>;>;)Ljavax/script/ScriptEngine;"}, {"nme": "reportException", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "debugPrint", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getEngineFactories", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljavax/script/ScriptEngineFactory;>;"}, {"nme": "registerEngineName", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)V"}, {"nme": "registerEngineMimeType", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)V"}, {"nme": "registerEngineExtension", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)V"}, {"nme": "associateFactory", "acc": 10, "dsc": "(Ljava/util/Map;Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljavax/script/ScriptEngineFactory;>;Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)V"}, {"nme": "lambda$getEngineBy$2", "acc": 4098, "dsc": "(Ljavax/script/ScriptEngineFactory;)Ljavax/script/ScriptEngine;"}, {"nme": "lambda$getEngineBy$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Function;Ljava/lang/String;Ljavax/script/ScriptEngineFactory;)Z"}, {"nme": "lambda$initEngines$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON>ja<PERSON>/util/ServiceLoader;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEBUG", "dsc": "Z", "val": 0}, {"acc": 26, "nme": "COMPARATOR", "dsc": "<PERSON><PERSON><PERSON>/util/Comparator;", "sig": "Ljava/util/Comparator<Ljavax/script/ScriptEngineFactory;>;"}, {"acc": 18, "nme": "engineSpis", "dsc": "L<PERSON><PERSON>/util/TreeSet;", "sig": "Ljava/util/TreeSet<Ljavax/script/ScriptEngineFactory;>;"}, {"acc": 18, "nme": "nameAssociations", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Ljavax/script/ScriptEngineFactory;>;"}, {"acc": 18, "nme": "extensionAssociations", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Ljavax/script/ScriptEngineFactory;>;"}, {"acc": 18, "nme": "mimeTypeAssociations", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Ljavax/script/ScriptEngineFactory;>;"}, {"acc": 2, "nme": "globalScope", "dsc": "Ljavax/script/Bindings;"}]}, "classes/javax/script/SimpleScriptContext.class": {"ver": 65, "acc": 33, "nme": "javax/script/SimpleScriptContext", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/io/Writer;<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "setB<PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/script/Bindings;I)V"}, {"nme": "getAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "removeAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "getWriter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Reader;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V"}, {"nme": "setWriter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "getErrorWriter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "setErrorWriter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "getAttributesScope", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getBindings", "acc": 1, "dsc": "(I)Ljavax/script/Bindings;"}, {"nme": "getScopes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Integer;>;"}, {"nme": "checkName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4, "nme": "writer", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 4, "nme": "errorWriter", "dsc": "<PERSON><PERSON><PERSON>/io/Writer;"}, {"acc": 4, "nme": "reader", "dsc": "<PERSON><PERSON><PERSON>/io/Reader;"}, {"acc": 4, "nme": "engineScope", "dsc": "Ljavax/script/Bindings;"}, {"acc": 4, "nme": "globalScope", "dsc": "Ljavax/script/Bindings;"}, {"acc": 26, "nme": "scopes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Integer;>;"}]}, "classes/com/sun/tools/script/shell/Main$2.class": {"ver": 65, "acc": 32, "nme": "com/sun/tools/script/shell/Main$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/script/ScriptEngine;Lja<PERSON>/lang/String;Ljava/lang/String;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 4112, "nme": "val$se", "dsc": "Ljavax/script/ScriptEngine;"}, {"acc": 4112, "nme": "val$fileName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$encoding", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/com/sun/tools/script/shell/Main.class": {"ver": 65, "acc": 33, "nme": "com/sun/tools/script/shell/Main", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "processOptions", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)[Ljava/lang/String;"}, {"nme": "addInteractiveMode", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;)V"}, {"nme": "addFileSource", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Lja<PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "addStringSource", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Ljava/lang/String;)V"}, {"nme": "listScriptEngines", "acc": 10, "dsc": "()V"}, {"nme": "processSource", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Lja<PERSON>/lang/String;Ljava/lang/String;)V"}, {"nme": "evaluateString", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;<PERSON>ja<PERSON>/lang/String;Z)Ljava/lang/Object;"}, {"nme": "evaluateString", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Ljava/lang/String;)V"}, {"nme": "evaluate<PERSON><PERSON>er", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;<PERSON><PERSON><PERSON>/io/Reader;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "evaluateStream", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Ljava/io/InputStream;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Object;"}, {"nme": "usage", "acc": 10, "dsc": "(I)V"}, {"nme": "getPrompt", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;)Ljava/lang/String;"}, {"nme": "getMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "getIn", "acc": 10, "dsc": "()Ljava/io/InputStream;"}, {"nme": "getError", "acc": 10, "dsc": "()Ljava/io/PrintStream;"}, {"nme": "getScriptEngine", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljavax/script/ScriptEngine;"}, {"nme": "initScriptEngine", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;)V"}, {"nme": "checkClassPath", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "pathToURLs", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;)[Ljava/net/URL;"}, {"nme": "fileToURL", "acc": 10, "dsc": "(Ljava/io/File;)Ljava/net/URL;"}, {"nme": "setScriptArguments", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;[Ljava/lang/String;)V"}, {"nme": "setScriptFilename", "acc": 10, "dsc": "(Ljavax/script/ScriptEngine;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "EXIT_SUCCESS", "dsc": "I", "val": 0}, {"acc": 26, "nme": "EXIT_CMD_NO_CLASSPATH", "dsc": "I", "val": 1}, {"acc": 26, "nme": "EXIT_CMD_NO_FILE", "dsc": "I", "val": 2}, {"acc": 26, "nme": "EXIT_CMD_NO_SCRIPT", "dsc": "I", "val": 3}, {"acc": 26, "nme": "EXIT_CMD_NO_LANG", "dsc": "I", "val": 4}, {"acc": 26, "nme": "EXIT_CMD_NO_ENCODING", "dsc": "I", "val": 5}, {"acc": 26, "nme": "EXIT_CMD_NO_PROPNAME", "dsc": "I", "val": 6}, {"acc": 26, "nme": "EXIT_UNKNOWN_OPTION", "dsc": "I", "val": 7}, {"acc": 26, "nme": "EXIT_ENGINE_NOT_FOUND", "dsc": "I", "val": 8}, {"acc": 26, "nme": "EXIT_NO_ENCODING_FOUND", "dsc": "I", "val": 9}, {"acc": 26, "nme": "EXIT_SCRIPT_ERROR", "dsc": "I", "val": 10}, {"acc": 26, "nme": "EXIT_FILE_NOT_FOUND", "dsc": "I", "val": 11}, {"acc": 26, "nme": "EXIT_MULTIPLE_STDIN", "dsc": "I", "val": 12}, {"acc": 26, "nme": "DEFAULT_LANGUAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "js"}, {"acc": 10, "nme": "scripts", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/sun/tools/script/shell/Main$Command;>;"}, {"acc": 10, "nme": "engineManager", "dsc": "Ljavax/script/ScriptEngineManager;"}, {"acc": 10, "nme": "engines", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljavax/script/ScriptEngine;>;"}, {"acc": 10, "nme": "msgRes", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 10, "nme": "BUNDLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "PROGRAM_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/javax/script/SimpleBindings.class": {"ver": 65, "acc": 33, "nme": "javax/script/SimpleBindings", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "put", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "putAll", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<+Ljava/lang/String;+Ljava/lang/Object;>;)V"}, {"nme": "clear", "acc": 1, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "containsValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "entrySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/util/Map$Entry<Ljava/lang/String;Ljava/lang/Object;>;>;"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "keySet", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "size", "acc": 1, "dsc": "()I"}, {"nme": "values", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/Object;>;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "put", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 18, "nme": "map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}]}, "classes/javax/script/CompiledScript.class": {"ver": 65, "acc": 1057, "nme": "javax/script/CompiledScript", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "eval", "acc": 1025, "dsc": "(Ljavax/script/ScriptContext;)Ljava/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1, "dsc": "(Ljavax/script/Bindings;)Ljava/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "eval", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/script/ScriptException"]}, {"nme": "getEngine", "acc": 1025, "dsc": "()Ljavax/script/ScriptEngine;"}], "flds": []}, "classes/com/sun/tools/script/shell/Main$1.class": {"ver": 65, "acc": 32, "nme": "com/sun/tools/script/shell/Main$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/script/ScriptEngine;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 4112, "nme": "val$se", "dsc": "Ljavax/script/ScriptEngine;"}]}, "classes/javax/script/ScriptContext.class": {"ver": 65, "acc": 1537, "nme": "javax/script/ScriptContext", "super": "java/lang/Object", "mthds": [{"nme": "setB<PERSON><PERSON>", "acc": 1025, "dsc": "(Ljavax/script/Bindings;I)V"}, {"nme": "getBindings", "acc": 1025, "dsc": "(I)Ljavax/script/Bindings;"}, {"nme": "setAttribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "getAttribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "removeAttribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getAttribute", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getAttributesScope", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getWriter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "getErrorWriter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;"}, {"nme": "setWriter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "setErrorWriter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Writer;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/io/Reader;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/Reader;)V"}, {"nme": "getScopes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/Integer;>;"}], "flds": [{"acc": 25, "nme": "ENGINE_SCOPE", "dsc": "I", "val": 100}, {"acc": 25, "nme": "GLOBAL_SCOPE", "dsc": "I", "val": 200}]}, "classes/com/sun/tools/script/shell/Main$Command.class": {"ver": 65, "acc": 1536, "nme": "com/sun/tools/script/shell/Main$Command", "super": "java/lang/Object", "mthds": [{"nme": "run", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": []}, "classes/javax/script/ScriptException.class": {"ver": 65, "acc": 33, "nme": "javax/script/ScriptException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;II)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLineNumber", "acc": 1, "dsc": "()I"}, {"nme": "getColumnNumber", "acc": 1, "dsc": "()I"}, {"nme": "getFileName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8265071037049225001}, {"acc": 18, "nme": "fileName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "lineNumber", "dsc": "I"}, {"acc": 18, "nme": "columnNumber", "dsc": "I"}]}, "classes/javax/script/Compilable.class": {"ver": 65, "acc": 1537, "nme": "javax/script/Compilable", "super": "java/lang/Object", "mthds": [{"nme": "compile", "acc": 1025, "dsc": "(Ljava/lang/String;)Ljavax/script/CompiledScript;", "exs": ["javax/script/ScriptException"]}, {"nme": "compile", "acc": 1025, "dsc": "(Ljava/io/Reader;)Ljavax/script/CompiledScript;", "exs": ["javax/script/ScriptException"]}], "flds": []}, "classes/com/sun/tools/script/shell/Main$3.class": {"ver": 65, "acc": 32, "nme": "com/sun/tools/script/shell/Main$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/script/ScriptEngine;Ljava/lang/String;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 4112, "nme": "val$se", "dsc": "Ljavax/script/ScriptEngine;"}, {"acc": 4112, "nme": "val$source", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/javax/script/ScriptEngineFactory.class": {"ver": 65, "acc": 1537, "nme": "javax/script/ScriptEngineFactory", "super": "java/lang/Object", "mthds": [{"nme": "getEngineName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEngineVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getExtensions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getMimeTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getNames", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getLanguageName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getLanguageVersion", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParameter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getMethodCallSyntax", "acc": 1153, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "getOutputStatement", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getProgram", "acc": 1153, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getScriptEngine", "acc": 1025, "dsc": "()Ljavax/script/ScriptEngine;"}], "flds": []}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/javax/script/Bindings.class": {"ver": 65, "acc": 1537, "nme": "javax/script/Bindings", "super": "java/lang/Object", "mthds": [{"nme": "put", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "putAll", "acc": 1025, "dsc": "(Ljava/util/Map;)V", "sig": "(L<PERSON><PERSON>/util/Map<+Ljava/lang/String;+Ljava/lang/Object;>;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "get", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "remove", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "put", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}}}}