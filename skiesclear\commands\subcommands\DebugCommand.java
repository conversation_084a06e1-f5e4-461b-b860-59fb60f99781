/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.brigadier.builder.LiteralArgumentBuilder
 *  com.mojang.brigadier.context.CommandContext
 *  com.mojang.brigadier.tree.LiteralCommandNode
 *  kotlin.Metadata
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  me.lucko.fabric.api.permissions.v0.Permissions
 *  net.kyori.adventure.text.Component
 *  net.kyori.adventure.text.format.NamedTextColor
 *  net.kyori.adventure.text.format.TextColor
 *  net.minecraft.class_2168
 *  net.minecraft.class_2170
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.commands.subcommands;

import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.tree.LiteralCommandNode;
import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.config.ConfigManager;
import com.pokeskies.skiesclear.utils.SubCommand;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import me.lucko.fabric.api.permissions.v0.Permissions;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.minecraft.class_2168;
import net.minecraft.class_2170;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \b2\u00020\u0001:\u0001\bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u0016\u00a2\u0006\u0004\b\u0006\u0010\u0007\u00a8\u0006\t"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/DebugCommand;", "Lcom/pokeskies/skiesclear/utils/SubCommand;", "<init>", "()V", "Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Lnet/minecraft/class_2168;", "build", "()Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Companion", "SkiesClear"})
public final class DebugCommand
implements SubCommand {
    @NotNull
    public static final Companion Companion = new Companion(null);

    @Override
    @NotNull
    public LiteralCommandNode<class_2168> build() {
        LiteralCommandNode literalCommandNode = ((LiteralArgumentBuilder)((LiteralArgumentBuilder)class_2170.method_9247((String)"debug").requires(Permissions.require((String)"skiesclear.command.debug", (int)4))).executes(Companion::debug)).build();
        Intrinsics.checkNotNullExpressionValue((Object)literalCommandNode, (String)"build(...)");
        return literalCommandNode;
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u001b\u0010\b\u001a\u00020\u00072\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\b\u0010\t\u00a8\u0006\n"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/DebugCommand$Companion;", "", "<init>", "()V", "Lcom/mojang/brigadier/context/CommandContext;", "Lnet/minecraft/class_2168;", "ctx", "", "debug", "(Lcom/mojang/brigadier/context/CommandContext;)I", "SkiesClear"})
    public static final class Companion {
        private Companion() {
        }

        public final int debug(@NotNull CommandContext<class_2168> ctx) {
            Intrinsics.checkNotNullParameter(ctx, (String)"ctx");
            boolean newMode = !ConfigManager.Companion.getCONFIG().getDebug();
            ConfigManager.Companion.getCONFIG().setDebug(newMode);
            SkiesClear.Companion.getINSTANCE().saveFile("config.json", ConfigManager.Companion.getCONFIG());
            if (newMode) {
                ((class_2168)ctx.getSource()).sendMessage(Component.text((String)"Debug mode has been enabled!").color((TextColor)NamedTextColor.GREEN));
            } else {
                ((class_2168)ctx.getSource()).sendMessage(Component.text((String)"Debug mode has been disabled!").color((TextColor)NamedTextColor.RED));
            }
            return 1;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

