/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.jvm.internal.SourceDebugExtension
 *  net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents
 *  net.minecraft.server.MinecraftServer
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear;

import com.pokeskies.skiesclear.ClearTask;
import com.pokeskies.skiesclear.config.ClearConfig;
import com.pokeskies.skiesclear.config.ConfigManager;
import java.util.LinkedHashMap;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents;
import net.minecraft.server.MinecraftServer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0005\u0010\u0003J\u0017\u0010\t\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\b\t\u0010\nR \u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\b0\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\f\u0010\rR\u0016\u0010\u000f\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0011"}, d2={"Lcom/pokeskies/skiesclear/ClearManager;", "", "<init>", "()V", "", "reload", "", "id", "Lcom/pokeskies/skiesclear/ClearTask;", "getClearTask", "(Ljava/lang/String;)Lcom/pokeskies/skiesclear/ClearTask;", "", "clearTasks", "Ljava/util/Map;", "", "ticks", "I", "SkiesClear"})
@SourceDebugExtension(value={"SMAP\nClearManager.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ClearManager.kt\ncom/pokeskies/skiesclear/ClearManager\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,32:1\n1863#2,2:33\n*S KotlinDebug\n*F\n+ 1 ClearManager.kt\ncom/pokeskies/skiesclear/ClearManager\n*L\n17#1:33,2\n*E\n"})
public final class ClearManager {
    @NotNull
    private final Map<String, ClearTask> clearTasks = new LinkedHashMap();
    private int ticks;

    public ClearManager() {
        this.reload();
        ServerTickEvents.END_SERVER_TICK.register(arg_0 -> ClearManager._init_$lambda$1(this, arg_0));
    }

    public final void reload() {
        this.clearTasks.clear();
        for (Map.Entry<String, ClearConfig> entry : ConfigManager.Companion.getCONFIG().getClears().entrySet()) {
            String id = entry.getKey();
            ClearConfig clearConfig = entry.getValue();
            if (!clearConfig.getEnabled()) continue;
            this.clearTasks.put(id, new ClearTask(clearConfig));
        }
    }

    @Nullable
    public final ClearTask getClearTask(@NotNull String id) {
        Intrinsics.checkNotNullParameter((Object)id, (String)"id");
        return this.clearTasks.get(id);
    }

    private static final void _init_$lambda$1(ClearManager this$0, MinecraftServer server) {
        Intrinsics.checkNotNullParameter((Object)this$0, (String)"this$0");
        Intrinsics.checkNotNullParameter((Object)server, (String)"server");
        int n = this$0.ticks;
        this$0.ticks = n + 1;
        if (n > 20) {
            this$0.ticks = 0;
            Iterable $this$forEach$iv = CollectionsKt.toList((Iterable)this$0.clearTasks.values());
            boolean $i$f$forEach = false;
            for (Object element$iv : $this$forEach$iv) {
                ClearTask it = (ClearTask)element$iv;
                boolean bl = false;
                it.tick(server);
            }
        }
    }
}

