<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <bytecodeTargetLevel target="21" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_STRING" value="-AoutRefMapFile=C:\Users\<USER>\Desktop\菜品\ZhaiCobblemonFish\build\classes\java\main\ZhaiCobblemonFish-refmap.json -AdefaultObfuscationEnv=named:intermediary -Aquiet=true -AoutMapFileNamedIntermediary=C:\Users\<USER>\Desktop\菜品\ZhaiCobblemonFish\build\loom-cache\mixin-map-loom.mappings.1_21_1.layered+hash.40545-v2.main.tiny -AinMapFileNamedIntermediary=C:\Users\<USER>\.gradle\caches\fabric-loom\1.21.1\loom.mappings.1_21_1.layered+hash.40545-v2\mappings.tiny" />
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ZhaiCobblemonFish.test" options="-AoutRefMapFile=C:\Users\<USER>\Desktop\菜品\ZhaiCobblemonFish\build\classes\java\test\test-ZhaiCobblemonFish-refmap.json -AdefaultObfuscationEnv=named:intermediary -Aquiet=true -AoutMapFileNamedIntermediary=C:\Users\<USER>\Desktop\菜品\ZhaiCobblemonFish\build\loom-cache\mixin-map-loom.mappings.1_21_1.layered+hash.40545-v2.test.tiny -AinMapFileNamedIntermediary=C:\Users\<USER>\.gradle\caches\fabric-loom\1.21.1\loom.mappings.1_21_1.layered+hash.40545-v2\mappings.tiny" />
    </option>
  </component>
</project>