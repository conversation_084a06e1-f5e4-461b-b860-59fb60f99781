  AcePokemonCleaner cn.acebrand.AcePokemonCleaner  Any cn.acebrand.AcePokemonCleaner  Array cn.acebrand.AcePokemonCleaner  BarColor cn.acebrand.AcePokemonCleaner  BarStyle cn.acebrand.AcePokemonCleaner  Boolean cn.acebrand.AcePokemonCleaner  BossBar cn.acebrand.AcePokemonCleaner  
BossBarConfig cn.acebrand.AcePokemonCleaner  Bukkit cn.acebrand.AcePokemonCleaner  BukkitRunnable cn.acebrand.AcePokemonCleaner  	ChatColor cn.acebrand.AcePokemonCleaner  Class cn.acebrand.AcePokemonCleaner  CleanResult cn.acebrand.AcePokemonCleaner  
CleanStats cn.acebrand.AcePokemonCleaner  
Collection cn.acebrand.AcePokemonCleaner  Command cn.acebrand.AcePokemonCleaner  CommandExecutor cn.acebrand.AcePokemonCleaner  
CommandSender cn.acebrand.AcePokemonCleaner  Commands cn.acebrand.AcePokemonCleaner  ConcurrentHashMap cn.acebrand.AcePokemonCleaner  
ConfigManager cn.acebrand.AcePokemonCleaner  Date cn.acebrand.AcePokemonCleaner  Double cn.acebrand.AcePokemonCleaner  Entity cn.acebrand.AcePokemonCleaner  EventHandler cn.acebrand.AcePokemonCleaner  	Exception cn.acebrand.AcePokemonCleaner  File cn.acebrand.AcePokemonCleaner  FileConfiguration cn.acebrand.AcePokemonCleaner  IOException cn.acebrand.AcePokemonCleaner  Int cn.acebrand.AcePokemonCleaner  	Inventory cn.acebrand.AcePokemonCleaner  InventoryClickEvent cn.acebrand.AcePokemonCleaner  InventoryCloseEvent cn.acebrand.AcePokemonCleaner  ItemExclusionGUI cn.acebrand.AcePokemonCleaner  ItemExclusionManager cn.acebrand.AcePokemonCleaner  	ItemStack cn.acebrand.AcePokemonCleaner  
JavaPlugin cn.acebrand.AcePokemonCleaner  Level cn.acebrand.AcePokemonCleaner  LicenseManager cn.acebrand.AcePokemonCleaner  List cn.acebrand.AcePokemonCleaner  Listener cn.acebrand.AcePokemonCleaner  Long cn.acebrand.AcePokemonCleaner  Map cn.acebrand.AcePokemonCleaner  Material cn.acebrand.AcePokemonCleaner  
MutableMap cn.acebrand.AcePokemonCleaner  
MutableSet cn.acebrand.AcePokemonCleaner  Pair cn.acebrand.AcePokemonCleaner  Player cn.acebrand.AcePokemonCleaner  Plugin cn.acebrand.AcePokemonCleaner  PokemonCleaner cn.acebrand.AcePokemonCleaner  Runnable cn.acebrand.AcePokemonCleaner  Set cn.acebrand.AcePokemonCleaner  SimpleDateFormat cn.acebrand.AcePokemonCleaner  String cn.acebrand.AcePokemonCleaner  System cn.acebrand.AcePokemonCleaner  TabCompleter cn.acebrand.AcePokemonCleaner  World cn.acebrand.AcePokemonCleaner  WorldCleanResult cn.acebrand.AcePokemonCleaner  YamlConfiguration cn.acebrand.AcePokemonCleaner  any cn.acebrand.AcePokemonCleaner  coerceIn cn.acebrand.AcePokemonCleaner  
component1 cn.acebrand.AcePokemonCleaner  
component2 cn.acebrand.AcePokemonCleaner  
configManager cn.acebrand.AcePokemonCleaner  contains cn.acebrand.AcePokemonCleaner  	emptyList cn.acebrand.AcePokemonCleaner  emptyMap cn.acebrand.AcePokemonCleaner  equals cn.acebrand.AcePokemonCleaner  filter cn.acebrand.AcePokemonCleaner  find cn.acebrand.AcePokemonCleaner  forEach cn.acebrand.AcePokemonCleaner  forEachIndexed cn.acebrand.AcePokemonCleaner  ifEmpty cn.acebrand.AcePokemonCleaner  isBlank cn.acebrand.AcePokemonCleaner  isEmpty cn.acebrand.AcePokemonCleaner  
isInitialized cn.acebrand.AcePokemonCleaner  isLowerCase cn.acebrand.AcePokemonCleaner  
isNotEmpty cn.acebrand.AcePokemonCleaner  
isNullOrEmpty cn.acebrand.AcePokemonCleaner  iterator cn.acebrand.AcePokemonCleaner  java cn.acebrand.AcePokemonCleaner  	javaClass cn.acebrand.AcePokemonCleaner  joinToString cn.acebrand.AcePokemonCleaner  let cn.acebrand.AcePokemonCleaner  listOf cn.acebrand.AcePokemonCleaner  logger cn.acebrand.AcePokemonCleaner  	lowercase cn.acebrand.AcePokemonCleaner  map cn.acebrand.AcePokemonCleaner  mapOf cn.acebrand.AcePokemonCleaner  maxOf cn.acebrand.AcePokemonCleaner  minOf cn.acebrand.AcePokemonCleaner  minus cn.acebrand.AcePokemonCleaner  
mutableListOf cn.acebrand.AcePokemonCleaner  mutableMapOf cn.acebrand.AcePokemonCleaner  mutableSetOf cn.acebrand.AcePokemonCleaner  
nextCleanTime cn.acebrand.AcePokemonCleaner  org cn.acebrand.AcePokemonCleaner  
plusAssign cn.acebrand.AcePokemonCleaner  pokemonCleaner cn.acebrand.AcePokemonCleaner  replace cn.acebrand.AcePokemonCleaner  replaceFirstChar cn.acebrand.AcePokemonCleaner  sendCountdownAnnouncement cn.acebrand.AcePokemonCleaner  set cn.acebrand.AcePokemonCleaner  split cn.acebrand.AcePokemonCleaner  
startsWith cn.acebrand.AcePokemonCleaner  take cn.acebrand.AcePokemonCleaner  takeIf cn.acebrand.AcePokemonCleaner  	titlecase cn.acebrand.AcePokemonCleaner  to cn.acebrand.AcePokemonCleaner  toIntOrNull cn.acebrand.AcePokemonCleaner  toList cn.acebrand.AcePokemonCleaner  toMutableSet cn.acebrand.AcePokemonCleaner  toRegex cn.acebrand.AcePokemonCleaner  toSet cn.acebrand.AcePokemonCleaner  until cn.acebrand.AcePokemonCleaner  
updateBossBar cn.acebrand.AcePokemonCleaner  	uppercase cn.acebrand.AcePokemonCleaner  Bukkit /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  Commands /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
ConfigManager /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  ItemExclusionGUI /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  Level /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  LicenseManager /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  PokemonCleaner /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  Runnable /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  System /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  cleanerRunnable /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  coerceIn /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
configManager /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
countdownTask /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  currentBossBar /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  description /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
getCommand /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  initializeLicense /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  isBlank /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
isInitialized /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  itemExclusionGUI /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  licenseManager /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  logger /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
nextCleanTime /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  pokemonCleaner /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  reloadConfiguration /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  replace /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  sendCountdownAnnouncement /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  server /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  showCleanupCompleteBossBar /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  startBossBarTask /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  startCleanerTask /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  startCountdownTask /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  stopCleanerTask /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  
updateBossBar /cn.acebrand.AcePokemonCleaner.AcePokemonCleaner  color +cn.acebrand.AcePokemonCleaner.BossBarConfig  message +cn.acebrand.AcePokemonCleaner.BossBarConfig  style +cn.acebrand.AcePokemonCleaner.BossBarConfig  duration )cn.acebrand.AcePokemonCleaner.CleanResult  error )cn.acebrand.AcePokemonCleaner.CleanResult  itemCleanedCount )cn.acebrand.AcePokemonCleaner.CleanResult  pokemonCleanedCount )cn.acebrand.AcePokemonCleaner.CleanResult  success )cn.acebrand.AcePokemonCleaner.CleanResult  totalCleanedCount )cn.acebrand.AcePokemonCleaner.CleanResult  vanillaMobCleanedCount )cn.acebrand.AcePokemonCleaner.CleanResult  worldResults )cn.acebrand.AcePokemonCleaner.CleanResult  	isRunning (cn.acebrand.AcePokemonCleaner.CleanStats  
lastCleanTime (cn.acebrand.AcePokemonCleaner.CleanStats  totalCleaned (cn.acebrand.AcePokemonCleaner.CleanStats  	ChatColor &cn.acebrand.AcePokemonCleaner.Commands  
CleanStats &cn.acebrand.AcePokemonCleaner.Commands  Date &cn.acebrand.AcePokemonCleaner.Commands  SimpleDateFormat &cn.acebrand.AcePokemonCleaner.Commands  System &cn.acebrand.AcePokemonCleaner.Commands  
component1 &cn.acebrand.AcePokemonCleaner.Commands  
component2 &cn.acebrand.AcePokemonCleaner.Commands  
configManager &cn.acebrand.AcePokemonCleaner.Commands  
dateFormat &cn.acebrand.AcePokemonCleaner.Commands  	emptyList &cn.acebrand.AcePokemonCleaner.Commands  filter &cn.acebrand.AcePokemonCleaner.Commands  forEachIndexed &cn.acebrand.AcePokemonCleaner.Commands  handleClean &cn.acebrand.AcePokemonCleaner.Commands  handleDebug &cn.acebrand.AcePokemonCleaner.Commands  handleItemsGUI &cn.acebrand.AcePokemonCleaner.Commands  handleReload &cn.acebrand.AcePokemonCleaner.Commands  handleStatus &cn.acebrand.AcePokemonCleaner.Commands  handleWorlds &cn.acebrand.AcePokemonCleaner.Commands  ifEmpty &cn.acebrand.AcePokemonCleaner.Commands  isEmpty &cn.acebrand.AcePokemonCleaner.Commands  
isNotEmpty &cn.acebrand.AcePokemonCleaner.Commands  itemExclusionGUI &cn.acebrand.AcePokemonCleaner.Commands  	javaClass &cn.acebrand.AcePokemonCleaner.Commands  joinToString &cn.acebrand.AcePokemonCleaner.Commands  listOf &cn.acebrand.AcePokemonCleaner.Commands  	lowercase &cn.acebrand.AcePokemonCleaner.Commands  org &cn.acebrand.AcePokemonCleaner.Commands  plugin &cn.acebrand.AcePokemonCleaner.Commands  pokemonCleaner &cn.acebrand.AcePokemonCleaner.Commands  sendHelp &cn.acebrand.AcePokemonCleaner.Commands  split &cn.acebrand.AcePokemonCleaner.Commands  
startsWith &cn.acebrand.AcePokemonCleaner.Commands  take &cn.acebrand.AcePokemonCleaner.Commands  BarColor +cn.acebrand.AcePokemonCleaner.ConfigManager  BarStyle +cn.acebrand.AcePokemonCleaner.ConfigManager  
BossBarConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  ItemExclusionManager +cn.acebrand.AcePokemonCleaner.ConfigManager  Level +cn.acebrand.AcePokemonCleaner.ConfigManager  announcementMessage +cn.acebrand.AcePokemonCleaner.ConfigManager  any +cn.acebrand.AcePokemonCleaner.ConfigManager  bossBarFlag +cn.acebrand.AcePokemonCleaner.ConfigManager  bossBarMessageForCount +cn.acebrand.AcePokemonCleaner.ConfigManager  
cleanBattling +cn.acebrand.AcePokemonCleaner.ConfigManager  	cleanBusy +cn.acebrand.AcePokemonCleaner.ConfigManager  cleanEnchantedItems +cn.acebrand.AcePokemonCleaner.ConfigManager  
cleanInterval +cn.acebrand.AcePokemonCleaner.ConfigManager  cleanNamedItems +cn.acebrand.AcePokemonCleaner.ConfigManager  
cleanOnlyWild +cn.acebrand.AcePokemonCleaner.ConfigManager  
cleanOwned +cn.acebrand.AcePokemonCleaner.ConfigManager  cleanRiding +cn.acebrand.AcePokemonCleaner.ConfigManager  cleanUncatchable +cn.acebrand.AcePokemonCleaner.ConfigManager  config +cn.acebrand.AcePokemonCleaner.ConfigManager  contains +cn.acebrand.AcePokemonCleaner.ConfigManager  countdownInterval +cn.acebrand.AcePokemonCleaner.ConfigManager  countdownMessage +cn.acebrand.AcePokemonCleaner.ConfigManager  countdownTimes +cn.acebrand.AcePokemonCleaner.ConfigManager  	debugMode +cn.acebrand.AcePokemonCleaner.ConfigManager  	emptyList +cn.acebrand.AcePokemonCleaner.ConfigManager  emptyMap +cn.acebrand.AcePokemonCleaner.ConfigManager  enableAnnouncement +cn.acebrand.AcePokemonCleaner.ConfigManager  enableCountdown +cn.acebrand.AcePokemonCleaner.ConfigManager  enableItemCleaning +cn.acebrand.AcePokemonCleaner.ConfigManager  
enableLogging +cn.acebrand.AcePokemonCleaner.ConfigManager  enableVanillaMobCleaning +cn.acebrand.AcePokemonCleaner.ConfigManager  
enabledWorlds +cn.acebrand.AcePokemonCleaner.ConfigManager  equals +cn.acebrand.AcePokemonCleaner.ConfigManager  
excludedItems +cn.acebrand.AcePokemonCleaner.ConfigManager  excludedPokemon +cn.acebrand.AcePokemonCleaner.ConfigManager  excludedVanillaMobs +cn.acebrand.AcePokemonCleaner.ConfigManager  
extractBaseId +cn.acebrand.AcePokemonCleaner.ConfigManager  getDefaultBossBarConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  
isInitialized +cn.acebrand.AcePokemonCleaner.ConfigManager  isItemExcluded +cn.acebrand.AcePokemonCleaner.ConfigManager  isItemMatch +cn.acebrand.AcePokemonCleaner.ConfigManager  
isNotEmpty +cn.acebrand.AcePokemonCleaner.ConfigManager  isPokemonExcluded +cn.acebrand.AcePokemonCleaner.ConfigManager  isVanillaMobExcluded +cn.acebrand.AcePokemonCleaner.ConfigManager  itemExclusionManager +cn.acebrand.AcePokemonCleaner.ConfigManager  itemMaxCleanPerRun +cn.acebrand.AcePokemonCleaner.ConfigManager  itemMaxDistance +cn.acebrand.AcePokemonCleaner.ConfigManager  
itemMinAge +cn.acebrand.AcePokemonCleaner.ConfigManager  itemRarityProtectionEnabled +cn.acebrand.AcePokemonCleaner.ConfigManager  
licenseKey +cn.acebrand.AcePokemonCleaner.ConfigManager  listOf +cn.acebrand.AcePokemonCleaner.ConfigManager  loadBossBarConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  
loadConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  mapOf +cn.acebrand.AcePokemonCleaner.ConfigManager  maxCleanPerRun +cn.acebrand.AcePokemonCleaner.ConfigManager  maxDistance +cn.acebrand.AcePokemonCleaner.ConfigManager  minAge +cn.acebrand.AcePokemonCleaner.ConfigManager  minAnnounceCount +cn.acebrand.AcePokemonCleaner.ConfigManager  mutableMapOf +cn.acebrand.AcePokemonCleaner.ConfigManager  
parseBarColor +cn.acebrand.AcePokemonCleaner.ConfigManager  
parseBarStyle +cn.acebrand.AcePokemonCleaner.ConfigManager  plugin +cn.acebrand.AcePokemonCleaner.ConfigManager  protectNamedVanillaMobs +cn.acebrand.AcePokemonCleaner.ConfigManager  protectedRarities +cn.acebrand.AcePokemonCleaner.ConfigManager  set +cn.acebrand.AcePokemonCleaner.ConfigManager  split +cn.acebrand.AcePokemonCleaner.ConfigManager  takeIf +cn.acebrand.AcePokemonCleaner.ConfigManager  to +cn.acebrand.AcePokemonCleaner.ConfigManager  toIntOrNull +cn.acebrand.AcePokemonCleaner.ConfigManager  	uppercase +cn.acebrand.AcePokemonCleaner.ConfigManager  useDefaultConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  validateConfig +cn.acebrand.AcePokemonCleaner.ConfigManager  vanillaMobMaxCleanPerRun +cn.acebrand.AcePokemonCleaner.ConfigManager  vanillaMobMinAge +cn.acebrand.AcePokemonCleaner.ConfigManager  Bukkit .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	ItemStack .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  Material .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  addControlButtons .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  checkAndJumpToNewItemPage .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  
component1 .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  
component2 .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  contains .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  createButton .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	createGUI .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	emptyList .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  exclusionManager .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	getBaseId .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  getItemDisplayName .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	getItemId .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  getItemName .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  getSimpleDisplayName .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleAddItemToSlot .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleItemSlotClick .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleNextPage .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handlePreviousPage .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleReloadConfig .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleRemoveItemFromSlot .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleReplaceItemInSlot .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleResetChanges .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleSaveConfig .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  handleShiftClickAdd .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  isLowerCase .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  
isNotEmpty .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  joinToString .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  let .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  listOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	lowercase .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  maxOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  minOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  minus .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  
mutableListOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  mutableMapOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  mutableSetOf .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  openGUI .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  openGUIs .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  parseItemDetails .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  playerPages .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  playerTempDisplayNames .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  playerTempExcludedItems .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  
refreshGUI .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  replace .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  replaceFirstChar .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  set .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  split .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  	titlecase .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  toList .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  toMutableSet .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  until .cn.acebrand.AcePokemonCleaner.ItemExclusionGUI  File 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  	ItemStack 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  Level 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  Material 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  YamlConfiguration 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  any 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  coerceIn 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
component1 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
component2 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  config 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
configFile 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  contains 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  createItemStack 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  equals 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  forEach 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  getGuiExcludedItems 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  getItemDisplayName 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  	getItemId 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
getMessage 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  getSimpleNBTInfo 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
guiEnabled 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  guiExcludedItems 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  guiSize 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  guiTitle 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
initialize 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
isBaseIdMatch 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  isDetailedMatch 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  isItemExcludedByGui 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  isItemMatch 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
isNotEmpty 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
isNullOrEmpty 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  itemDisplayNames 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  itemsPerPage 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  iterator 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  joinToString 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
loadConfig 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  	lowercase 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  map 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  messages 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
mutableListOf 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  mutableMapOf 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  mutableSetOf 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  parseItemDetails 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  plugin 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  removeExcludedItemById 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  replace 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  
saveConfig 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  set 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  setupConfigFile 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  split 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  toList 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  toSet 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  until 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  updateExcludedItems 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  	uppercase 2cn.acebrand.AcePokemonCleaner.ItemExclusionManager  Bukkit ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  Class ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  CleanResult ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  ConcurrentHashMap ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  Level ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  System ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  WorldCleanResult ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  any ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  checkEntityBattling ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  checkEntityBusy ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  checkEntityUncatchable ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  checkPokemonLegendary ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  checkPokemonOwnership ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  cleanWorldEntities ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  cleanup ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
configManager ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  contains ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  equals ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  extractSpeciesFromPokemon ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  filter ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  find ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getDetailedItemInfo ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getDetailedPokemonInfo ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getItemEntityInfo ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getNBTString ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getPokemonEntityInfo ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getPokemonEntityInfoPublic ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getPokemonSpecies ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getWorldByName ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  getWorldByNamePublic ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
hasCustomName ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isEnchantedItem ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isItemEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isMythicalPokemonByName ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isNearPlayer ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isNearPlayerForItem ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
isNotEmpty ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
isNullOrEmpty ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonBattling ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
isPokemonBusy ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonEntityPublic ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonExcluded ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonExcludedPublic ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonLegendary ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonPlayerOwned ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonShiny ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isPokemonUncatchable ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isProtectedByRarity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  isVanillaMobEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  java ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  	javaClass ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  joinToString ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  listOf ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  	lowercase ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  map ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
mutableListOf ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  mutableMapOf ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  org ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  performClean ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  plugin ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  
plusAssign ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  pokemonSpawnTimes ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  replace ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  sendCleanupAnnouncement ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  set ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  shouldCleanItemEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  shouldCleanPokemonEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  shouldCleanVanillaMobEntity ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  simplifyNBT ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  take ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  toList ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  toRegex ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  	uppercase ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  vanillaMobSpawnTimes ,cn.acebrand.AcePokemonCleaner.PokemonCleaner  	itemCount .cn.acebrand.AcePokemonCleaner.WorldCleanResult  pokemonCount .cn.acebrand.AcePokemonCleaner.WorldCleanResult  vanillaMobCount .cn.acebrand.AcePokemonCleaner.WorldCleanResult  API_BASE_URL %cn.acebrand.AcePokemonCleaner.license  Base64 %cn.acebrand.AcePokemonCleaner.license  Boolean %cn.acebrand.AcePokemonCleaner.license  BufferedReader %cn.acebrand.AcePokemonCleaner.license  	ByteArray %cn.acebrand.AcePokemonCleaner.license  	Character %cn.acebrand.AcePokemonCleaner.license  ERROR_MESSAGES %cn.acebrand.AcePokemonCleaner.license  	Exception %cn.acebrand.AcePokemonCleaner.license  	Executors %cn.acebrand.AcePokemonCleaner.license  GSON %cn.acebrand.AcePokemonCleaner.license  GsonBuilder %cn.acebrand.AcePokemonCleaner.license  HEARTBEAT_ENDPOINT %cn.acebrand.AcePokemonCleaner.license  HttpURLConnection %cn.acebrand.AcePokemonCleaner.license  IOException %cn.acebrand.AcePokemonCleaner.license  InetAddress %cn.acebrand.AcePokemonCleaner.license  InputStream %cn.acebrand.AcePokemonCleaner.license  InputStreamReader %cn.acebrand.AcePokemonCleaner.license  Int %cn.acebrand.AcePokemonCleaner.license  InterruptedException %cn.acebrand.AcePokemonCleaner.license  
JsonObject %cn.acebrand.AcePokemonCleaner.license  
KeyFactory %cn.acebrand.AcePokemonCleaner.license  LicenseInfo %cn.acebrand.AcePokemonCleaner.license  LicenseManager %cn.acebrand.AcePokemonCleaner.license  
PRODUCT_ID %cn.acebrand.AcePokemonCleaner.license  Plugin %cn.acebrand.AcePokemonCleaner.license  RSA_PUBLIC_KEY %cn.acebrand.AcePokemonCleaner.license  ScheduledExecutorService %cn.acebrand.AcePokemonCleaner.license  SecureRandom %cn.acebrand.AcePokemonCleaner.license  Set %cn.acebrand.AcePokemonCleaner.license  	Signature %cn.acebrand.AcePokemonCleaner.license  StandardCharsets %cn.acebrand.AcePokemonCleaner.license  String %cn.acebrand.AcePokemonCleaner.license  
StringBuilder %cn.acebrand.AcePokemonCleaner.license  System %cn.acebrand.AcePokemonCleaner.license  TEAM_ID %cn.acebrand.AcePokemonCleaner.license  TIMEOUT_MILLIS %cn.acebrand.AcePokemonCleaner.license  Thread %cn.acebrand.AcePokemonCleaner.license  TimeUnit %cn.acebrand.AcePokemonCleaner.license  URI %cn.acebrand.AcePokemonCleaner.license  UUID %cn.acebrand.AcePokemonCleaner.license  VERIFY_ENDPOINT %cn.acebrand.AcePokemonCleaner.license  VERSION %cn.acebrand.AcePokemonCleaner.license  X509EncodedKeySpec %cn.acebrand.AcePokemonCleaner.license  format %cn.acebrand.AcePokemonCleaner.license  invoke %cn.acebrand.AcePokemonCleaner.license  isBlank %cn.acebrand.AcePokemonCleaner.license  java %cn.acebrand.AcePokemonCleaner.license  let %cn.acebrand.AcePokemonCleaner.license  mapOf %cn.acebrand.AcePokemonCleaner.license  replace %cn.acebrand.AcePokemonCleaner.license  setOf %cn.acebrand.AcePokemonCleaner.license  step %cn.acebrand.AcePokemonCleaner.license  to %cn.acebrand.AcePokemonCleaner.license  toByteArray %cn.acebrand.AcePokemonCleaner.license  toRegex %cn.acebrand.AcePokemonCleaner.license  
trimIndent %cn.acebrand.AcePokemonCleaner.license  until %cn.acebrand.AcePokemonCleaner.license  use %cn.acebrand.AcePokemonCleaner.license  API_BASE_URL 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Base64 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Boolean 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  BufferedReader 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	ByteArray 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	Character 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  ERROR_MESSAGES 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	Exception 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	Executors 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  GSON 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  GsonBuilder 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  HEARTBEAT_ENDPOINT 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  HttpURLConnection 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  IOException 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  InetAddress 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  InputStream 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  InputStreamReader 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Int 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  InterruptedException 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
JsonObject 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
KeyFactory 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  LicenseInfo 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
PRODUCT_ID 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Plugin 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  RSA_PUBLIC_KEY 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  ScheduledExecutorService 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  SecureRandom 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Set 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	Signature 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  StandardCharsets 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  String 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
StringBuilder 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  System 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  TEAM_ID 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  TIMEOUT_MILLIS 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  Thread 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  TimeUnit 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  URI 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  UUID 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  VERIFY_ENDPOINT 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  VERSION 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  X509EncodedKeySpec 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  buildUserAgent 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
bytesToHex 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  cleanup 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  deviceIdentifier 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  fetchAndHandleResponse 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  format 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  generateHardwareIdentifier 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  generateRandomChallenge 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  handleJsonResponse 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  hexStringToByteArray 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  invoke 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  isBlank 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  isLicenseValid 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  isValid 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  java 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  let 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  licenseInfo 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  logger 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  mapOf 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  plugin 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  replace 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  	scheduler 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
sendHeartbeat 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  setOf 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  setupHeartbeatScheduler 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  shutdown 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  step 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  to 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  toByteArray 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  toRegex 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  
trimIndent 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  until 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  use 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  validateChallenge 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  validateLicenseKey 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  validateResponse 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  verifySignature 4cn.acebrand.AcePokemonCleaner.license.LicenseManager  API_BASE_URL >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  Base64 >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  BufferedReader >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  	ByteArray >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  	Character >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  ERROR_MESSAGES >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  	Executors >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  GSON >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  GsonBuilder >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  HEARTBEAT_ENDPOINT >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  HttpURLConnection >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  IOException >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  InetAddress >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  InputStreamReader >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  
JsonObject >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  
KeyFactory >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  LicenseInfo >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  
PRODUCT_ID >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  RSA_PUBLIC_KEY >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  SecureRandom >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  	Signature >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  StandardCharsets >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  String >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  
StringBuilder >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  System >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  TEAM_ID >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  TIMEOUT_MILLIS >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  Thread >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  TimeUnit >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  URI >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  UUID >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  VERIFY_ENDPOINT >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  VERSION >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  X509EncodedKeySpec >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  format >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  invoke >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  isBlank >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  java >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  let >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  mapOf >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  replace >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  setOf >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  step >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  to >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  toByteArray >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  toRegex >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  
trimIndent >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  until >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  use >cn.acebrand.AcePokemonCleaner.license.LicenseManager.Companion  features @cn.acebrand.AcePokemonCleaner.license.LicenseManager.LicenseInfo  bukkit !cn.acebrand.AcePokemonCleaner.org  entity (cn.acebrand.AcePokemonCleaner.org.bukkit  	inventory (cn.acebrand.AcePokemonCleaner.org.bukkit  Item /cn.acebrand.AcePokemonCleaner.org.bukkit.entity  	ItemStack 2cn.acebrand.AcePokemonCleaner.org.bukkit.inventory  Gson com.google.gson  GsonBuilder com.google.gson  
JsonObject com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  disableHtmlEscaping com.google.gson.GsonBuilder  setPrettyPrinting com.google.gson.GsonBuilder  	asBoolean com.google.gson.JsonElement  asString com.google.gson.JsonElement  getAsJsonObject com.google.gson.JsonElement  get com.google.gson.JsonObject  getAsJsonObject com.google.gson.JsonObject  has com.google.gson.JsonObject  BufferedReader java.io  File java.io  IOException java.io  InputStream java.io  InputStreamReader java.io  use java.io.BufferedReader  
createNewFile java.io.File  exists java.io.File  mkdirs java.io.File  
parentFile java.io.File  use java.io.InputStream  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  InterruptedException 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  append java.lang.AbstractStringBuilder  digit java.lang.Character  declaredFields java.lang.Class  forName java.lang.Class  	getMethod java.lang.Class  methods java.lang.Class  name java.lang.Class  
simpleName java.lang.Class  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  getProperty java.lang.System  
currentThread java.lang.Thread  	interrupt java.lang.Thread  Field java.lang.reflect  isAccessible "java.lang.reflect.AccessibleObject  get java.lang.reflect.Field  isAccessible java.lang.reflect.Field  name java.lang.reflect.Field  type java.lang.reflect.Field  invoke java.lang.reflect.Method  name java.lang.reflect.Method  
BigDecimal 	java.math  
BigInteger 	java.math  HttpURLConnection java.net  InetAddress java.net  URI java.net  HTTP_OK java.net.HttpURLConnection  connectTimeout java.net.HttpURLConnection  
disconnect java.net.HttpURLConnection  doOutput java.net.HttpURLConnection  errorStream java.net.HttpURLConnection  inputStream java.net.HttpURLConnection  outputStream java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  
requestMethod java.net.HttpURLConnection  responseCode java.net.HttpURLConnection  setRequestProperty java.net.HttpURLConnection  getLocalHost java.net.InetAddress  hostName java.net.InetAddress  create java.net.URI  toURL java.net.URI  openConnection java.net.URL  connectTimeout java.net.URLConnection  doOutput java.net.URLConnection  inputStream java.net.URLConnection  outputStream java.net.URLConnection  readTimeout java.net.URLConnection  setRequestProperty java.net.URLConnection  StandardCharsets java.nio.charset  UTF_8 !java.nio.charset.StandardCharsets  
KeyFactory 
java.security  	PublicKey 
java.security  SecureRandom 
java.security  	Signature 
java.security  generatePublic java.security.KeyFactory  getInstance java.security.KeyFactory  	nextBytes java.security.SecureRandom  getInstance java.security.Signature  
initVerify java.security.Signature  update java.security.Signature  verify java.security.Signature  X509EncodedKeySpec java.security.spec  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  API_BASE_URL 	java.util  AcePokemonCleaner 	java.util  Array 	java.util  Base64 	java.util  Boolean 	java.util  BufferedReader 	java.util  	ByteArray 	java.util  	Character 	java.util  	ChatColor 	java.util  
CleanStats 	java.util  Command 	java.util  CommandExecutor 	java.util  
CommandSender 	java.util  
ConfigManager 	java.util  Date 	java.util  ERROR_MESSAGES 	java.util  	Exception 	java.util  	Executors 	java.util  GSON 	java.util  GsonBuilder 	java.util  HEARTBEAT_ENDPOINT 	java.util  HttpURLConnection 	java.util  IOException 	java.util  InetAddress 	java.util  InputStream 	java.util  InputStreamReader 	java.util  Int 	java.util  InterruptedException 	java.util  ItemExclusionGUI 	java.util  
JsonObject 	java.util  
KeyFactory 	java.util  LicenseInfo 	java.util  List 	java.util  Long 	java.util  
PRODUCT_ID 	java.util  Player 	java.util  Plugin 	java.util  PokemonCleaner 	java.util  RSA_PUBLIC_KEY 	java.util  ScheduledExecutorService 	java.util  SecureRandom 	java.util  Set 	java.util  	Signature 	java.util  SimpleDateFormat 	java.util  StandardCharsets 	java.util  String 	java.util  
StringBuilder 	java.util  System 	java.util  TEAM_ID 	java.util  TIMEOUT_MILLIS 	java.util  TabCompleter 	java.util  Thread 	java.util  TimeUnit 	java.util  URI 	java.util  UUID 	java.util  VERIFY_ENDPOINT 	java.util  VERSION 	java.util  X509EncodedKeySpec 	java.util  
component1 	java.util  
component2 	java.util  	emptyList 	java.util  filter 	java.util  forEach 	java.util  forEachIndexed 	java.util  format 	java.util  ifEmpty 	java.util  invoke 	java.util  isBlank 	java.util  isEmpty 	java.util  
isNotEmpty 	java.util  java 	java.util  	javaClass 	java.util  joinToString 	java.util  let 	java.util  listOf 	java.util  	lowercase 	java.util  mapOf 	java.util  org 	java.util  replace 	java.util  setOf 	java.util  split 	java.util  
startsWith 	java.util  step 	java.util  take 	java.util  to 	java.util  toByteArray 	java.util  toRegex 	java.util  
trimIndent 	java.util  until 	java.util  use 	java.util  
getDecoder java.util.Base64  decode java.util.Base64.Decoder  nameUUIDFromBytes java.util.UUID  
randomUUID java.util.UUID  toString java.util.UUID  ConcurrentHashMap java.util.concurrent  	Executors java.util.concurrent  ScheduledExecutorService java.util.concurrent  ScheduledFuture java.util.concurrent  TimeUnit java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  awaitTermination $java.util.concurrent.ExecutorService  
isShutdown $java.util.concurrent.ExecutorService  shutdown $java.util.concurrent.ExecutorService  shutdownNow $java.util.concurrent.ExecutorService   newSingleThreadScheduledExecutor java.util.concurrent.Executors  awaitTermination -java.util.concurrent.ScheduledExecutorService  
isShutdown -java.util.concurrent.ScheduledExecutorService  let -java.util.concurrent.ScheduledExecutorService  scheduleAtFixedRate -java.util.concurrent.ScheduledExecutorService  shutdown -java.util.concurrent.ScheduledExecutorService  shutdownNow -java.util.concurrent.ScheduledExecutorService  MINUTES java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  Level java.util.logging  Logger java.util.logging  SEVERE java.util.logging.Level  WARNING java.util.logging.Level  fine java.util.logging.Logger  info java.util.logging.Logger  log java.util.logging.Logger  severe java.util.logging.Logger  warning java.util.logging.Logger  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  
isInitialized kotlin  let kotlin  map kotlin  minus kotlin  takeIf kotlin  to kotlin  toList kotlin  use kotlin  equals 
kotlin.Any  	javaClass 
kotlin.Any  toString 
kotlin.Any  forEach kotlin.Array  get kotlin.Array  isEmpty kotlin.Array  
isNotEmpty kotlin.Array  size kotlin.Array  not kotlin.Boolean  iterator kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  isLowerCase kotlin.Char  	titlecase kotlin.Char  toString kotlin.Char  coerceIn 
kotlin.Double  	compareTo 
kotlin.Double  div 
kotlin.Double  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  invoke 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  shl 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toDouble 
kotlin.Int  toString 
kotlin.Int  until 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  toInt kotlin.Long  toString kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  equals 
kotlin.String  format 
kotlin.String  get 
kotlin.String  ifEmpty 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  replaceFirstChar 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toIntOrNull 
kotlin.String  toRegex 
kotlin.String  
trimIndent 
kotlin.String  	uppercase 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  ByteIterator kotlin.collections  CharIterator kotlin.collections  
Collection kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  ifEmpty kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  minus kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  setOf kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toMutableSet kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.ByteIterator  next kotlin.collections.ByteIterator  
isNotEmpty kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  contains kotlin.collections.List  filter kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  iterator kotlin.collections.Map  size kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  iterator $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  size kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  putAll kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  add kotlin.collections.MutableSet  addAll kotlin.collections.MutableSet  any kotlin.collections.MutableSet  clear kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  isEmpty kotlin.collections.MutableSet  minus kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  size kotlin.collections.MutableSet  toList kotlin.collections.MutableSet  toSet kotlin.collections.MutableSet  contains kotlin.collections.Set  minus kotlin.collections.Set  size kotlin.collections.Set  toMutableSet kotlin.collections.Set  maxOf kotlin.comparisons  minOf kotlin.comparisons  iterator 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  	javaClass 
kotlin.jvm  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  step 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  step kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  ifEmpty kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  minus kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  toMutableSet kotlin.sequences  toSet kotlin.sequences  Regex kotlin.text  String kotlin.text  any kotlin.text  contains kotlin.text  equals kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  ifEmpty kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  isLowerCase kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  iterator kotlin.text  	lowercase kotlin.text  map kotlin.text  maxOf kotlin.text  minOf kotlin.text  replace kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  take kotlin.text  	titlecase kotlin.text  toByteArray kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toRegex kotlin.text  toSet kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  Bukkit 
org.bukkit  	ChatColor 
org.bukkit  Location 
org.bukkit  Material 
org.bukkit  
NamespacedKey 
org.bukkit  Server 
org.bukkit  World 
org.bukkit  
createBossBar org.bukkit.Bukkit  createInventory org.bukkit.Bukkit  getOnlinePlayers org.bukkit.Bukkit  getScheduler org.bukkit.Bukkit  getWorld org.bukkit.Bukkit  	getWorlds org.bukkit.Bukkit  AQUA org.bukkit.ChatColor  GOLD org.bukkit.ChatColor  GRAY org.bukkit.ChatColor  GREEN org.bukkit.ChatColor  RED org.bukkit.ChatColor  WHITE org.bukkit.ChatColor  YELLOW org.bukkit.ChatColor  key org.bukkit.Keyed  distance org.bukkit.Location  world org.bukkit.Location  AIR org.bukkit.Material  ARROW org.bukkit.Material  BARRIER org.bukkit.Material  BOOK org.bukkit.Material  GREEN_STAINED_GLASS_PANE org.bukkit.Material  LIME_STAINED_GLASS_PANE org.bukkit.Material  ORANGE_STAINED_GLASS_PANE org.bukkit.Material  YELLOW_STAINED_GLASS_PANE org.bukkit.Material  getMaterial org.bukkit.Material  key org.bukkit.Material  name org.bukkit.Material  
customName org.bukkit.Nameable  key org.bukkit.NamespacedKey  	namespace org.bukkit.NamespacedKey  
pluginManager org.bukkit.Server  Environment org.bukkit.World  entities org.bukkit.World  name org.bukkit.World  players org.bukkit.World  BarColor org.bukkit.boss  BarStyle org.bukkit.boss  BossBar org.bukkit.boss  KeyedBossBar org.bukkit.boss  BLUE org.bukkit.boss.BarColor  GREEN org.bukkit.boss.BarColor  PINK org.bukkit.boss.BarColor  PURPLE org.bukkit.boss.BarColor  RED org.bukkit.boss.BarColor  WHITE org.bukkit.boss.BarColor  YELLOW org.bukkit.boss.BarColor  SEGMENTED_10 org.bukkit.boss.BarStyle  SEGMENTED_12 org.bukkit.boss.BarStyle  SEGMENTED_20 org.bukkit.boss.BarStyle  SEGMENTED_6 org.bukkit.boss.BarStyle  SOLID org.bukkit.boss.BarStyle  	addPlayer org.bukkit.boss.BossBar  progress org.bukkit.boss.BossBar  	removeAll org.bukkit.boss.BossBar  Command org.bukkit.command  CommandExecutor org.bukkit.command  
CommandSender org.bukkit.command  
PluginCommand org.bukkit.command  TabCompleter org.bukkit.command  
hasPermission  org.bukkit.command.CommandSender  name  org.bukkit.command.CommandSender  sendMessage  org.bukkit.command.CommandSender  setExecutor  org.bukkit.command.PluginCommand  ConfigurationSection org.bukkit.configuration  
getBoolean -org.bukkit.configuration.ConfigurationSection  getInt -org.bukkit.configuration.ConfigurationSection  getKeys -org.bukkit.configuration.ConfigurationSection  	getString -org.bukkit.configuration.ConfigurationSection  set -org.bukkit.configuration.ConfigurationSection  
createSection ,org.bukkit.configuration.MemoryConfiguration  
getBoolean ,org.bukkit.configuration.MemoryConfiguration  getConfigurationSection ,org.bukkit.configuration.MemoryConfiguration  	getDouble ,org.bukkit.configuration.MemoryConfiguration  getInt ,org.bukkit.configuration.MemoryConfiguration  getIntegerList ,org.bukkit.configuration.MemoryConfiguration  	getString ,org.bukkit.configuration.MemoryConfiguration  
getStringList ,org.bukkit.configuration.MemoryConfiguration  set ,org.bukkit.configuration.MemoryConfiguration  FileConfiguration org.bukkit.configuration.file  YamlConfiguration org.bukkit.configuration.file  
createSection /org.bukkit.configuration.file.FileConfiguration  
getBoolean /org.bukkit.configuration.file.FileConfiguration  getConfigurationSection /org.bukkit.configuration.file.FileConfiguration  	getDouble /org.bukkit.configuration.file.FileConfiguration  getInt /org.bukkit.configuration.file.FileConfiguration  getIntegerList /org.bukkit.configuration.file.FileConfiguration  	getString /org.bukkit.configuration.file.FileConfiguration  
getStringList /org.bukkit.configuration.file.FileConfiguration  save /org.bukkit.configuration.file.FileConfiguration  set /org.bukkit.configuration.file.FileConfiguration  loadConfiguration /org.bukkit.configuration.file.YamlConfiguration  Entity org.bukkit.entity  
EntityType org.bukkit.entity  HumanEntity org.bukkit.entity  Item org.bukkit.entity  Player org.bukkit.entity  
customName org.bukkit.entity.Entity  getNearbyEntities org.bukkit.entity.Entity  isDead org.bukkit.entity.Entity  	javaClass org.bukkit.entity.Entity  location org.bukkit.entity.Entity  name org.bukkit.entity.Entity  remove org.bukkit.entity.Entity  
ticksLived org.bukkit.entity.Entity  type org.bukkit.entity.Entity  uniqueId org.bukkit.entity.Entity  isAlive org.bukkit.entity.EntityType  key org.bukkit.entity.EntityType  name org.bukkit.entity.EntityType  closeInventory org.bukkit.entity.HumanEntity  	inventory org.bukkit.entity.HumanEntity  
openInventory org.bukkit.entity.HumanEntity  	itemStack org.bukkit.entity.Item  closeInventory org.bukkit.entity.Player  getNearbyEntities org.bukkit.entity.Player  
hasPermission org.bukkit.entity.Player  	inventory org.bukkit.entity.Player  isOp org.bukkit.entity.Player  
openInventory org.bukkit.entity.Player  sendMessage org.bukkit.entity.Player  EventHandler org.bukkit.event  Listener org.bukkit.event  	ClickType org.bukkit.event.inventory  InventoryClickEvent org.bukkit.event.inventory  InventoryCloseEvent org.bukkit.event.inventory  isLeftClick $org.bukkit.event.inventory.ClickType  isShiftClick $org.bukkit.event.inventory.ClickType  name $org.bukkit.event.inventory.ClickType  click .org.bukkit.event.inventory.InventoryClickEvent  clickedInventory .org.bukkit.event.inventory.InventoryClickEvent  currentItem .org.bukkit.event.inventory.InventoryClickEvent  cursor .org.bukkit.event.inventory.InventoryClickEvent  	inventory .org.bukkit.event.inventory.InventoryClickEvent  isCancelled .org.bukkit.event.inventory.InventoryClickEvent  isRightClick .org.bukkit.event.inventory.InventoryClickEvent  slot .org.bukkit.event.inventory.InventoryClickEvent  
whoClicked .org.bukkit.event.inventory.InventoryClickEvent  	inventory .org.bukkit.event.inventory.InventoryCloseEvent  player .org.bukkit.event.inventory.InventoryCloseEvent  	inventory )org.bukkit.event.inventory.InventoryEvent  isCancelled 1org.bukkit.event.inventory.InventoryInteractEvent  
whoClicked 1org.bukkit.event.inventory.InventoryInteractEvent  environment org.bukkit.generator.WorldInfo  name org.bukkit.generator.WorldInfo  	Inventory org.bukkit.inventory  
InventoryView org.bukkit.inventory  	ItemStack org.bukkit.inventory  PlayerInventory org.bukkit.inventory  setItem org.bukkit.inventory.Inventory  size org.bukkit.inventory.Inventory  amount org.bukkit.inventory.ItemStack  hasItemMeta org.bukkit.inventory.ItemStack  itemMeta org.bukkit.inventory.ItemStack  type org.bukkit.inventory.ItemStack  ItemMeta org.bukkit.inventory.meta  customModelData "org.bukkit.inventory.meta.ItemMeta  displayName "org.bukkit.inventory.meta.ItemMeta  enchants "org.bukkit.inventory.meta.ItemMeta  hasAttributeModifiers "org.bukkit.inventory.meta.ItemMeta  hasCustomModelData "org.bukkit.inventory.meta.ItemMeta  hasDisplayName "org.bukkit.inventory.meta.ItemMeta  hasEnchants "org.bukkit.inventory.meta.ItemMeta  hasLore "org.bukkit.inventory.meta.ItemMeta  
isUnbreakable "org.bukkit.inventory.meta.ItemMeta  lore "org.bukkit.inventory.meta.ItemMeta  setDisplayName "org.bukkit.inventory.meta.ItemMeta  
hasPermission "org.bukkit.permissions.Permissible  isOp %org.bukkit.permissions.ServerOperator  Plugin org.bukkit.plugin  PluginDescriptionFile org.bukkit.plugin  
PluginManager org.bukkit.plugin  config org.bukkit.plugin.Plugin  
dataFolder org.bukkit.plugin.Plugin  logger org.bukkit.plugin.Plugin  reloadConfig org.bukkit.plugin.Plugin  saveDefaultConfig org.bukkit.plugin.Plugin  saveResource org.bukkit.plugin.Plugin  Bukkit org.bukkit.plugin.PluginBase  BukkitRunnable org.bukkit.plugin.PluginBase  Commands org.bukkit.plugin.PluginBase  
ConfigManager org.bukkit.plugin.PluginBase  	Exception org.bukkit.plugin.PluginBase  ItemExclusionGUI org.bukkit.plugin.PluginBase  Level org.bukkit.plugin.PluginBase  LicenseManager org.bukkit.plugin.PluginBase  PokemonCleaner org.bukkit.plugin.PluginBase  Runnable org.bukkit.plugin.PluginBase  System org.bukkit.plugin.PluginBase  coerceIn org.bukkit.plugin.PluginBase  
configManager org.bukkit.plugin.PluginBase  isBlank org.bukkit.plugin.PluginBase  
isInitialized org.bukkit.plugin.PluginBase  logger org.bukkit.plugin.PluginBase  
nextCleanTime org.bukkit.plugin.PluginBase  pokemonCleaner org.bukkit.plugin.PluginBase  replace org.bukkit.plugin.PluginBase  sendCountdownAnnouncement org.bukkit.plugin.PluginBase  
updateBossBar org.bukkit.plugin.PluginBase  version 'org.bukkit.plugin.PluginDescriptionFile  
disablePlugin org.bukkit.plugin.PluginManager  registerEvents org.bukkit.plugin.PluginManager  
JavaPlugin org.bukkit.plugin.java  Bukkit !org.bukkit.plugin.java.JavaPlugin  BukkitRunnable !org.bukkit.plugin.java.JavaPlugin  Commands !org.bukkit.plugin.java.JavaPlugin  
ConfigManager !org.bukkit.plugin.java.JavaPlugin  	Exception !org.bukkit.plugin.java.JavaPlugin  ItemExclusionGUI !org.bukkit.plugin.java.JavaPlugin  Level !org.bukkit.plugin.java.JavaPlugin  LicenseManager !org.bukkit.plugin.java.JavaPlugin  PokemonCleaner !org.bukkit.plugin.java.JavaPlugin  Runnable !org.bukkit.plugin.java.JavaPlugin  System !org.bukkit.plugin.java.JavaPlugin  coerceIn !org.bukkit.plugin.java.JavaPlugin  
configManager !org.bukkit.plugin.java.JavaPlugin  description !org.bukkit.plugin.java.JavaPlugin  
getCommand !org.bukkit.plugin.java.JavaPlugin  isBlank !org.bukkit.plugin.java.JavaPlugin  
isInitialized !org.bukkit.plugin.java.JavaPlugin  logger !org.bukkit.plugin.java.JavaPlugin  
nextCleanTime !org.bukkit.plugin.java.JavaPlugin  pokemonCleaner !org.bukkit.plugin.java.JavaPlugin  replace !org.bukkit.plugin.java.JavaPlugin  sendCountdownAnnouncement !org.bukkit.plugin.java.JavaPlugin  server !org.bukkit.plugin.java.JavaPlugin  
updateBossBar !org.bukkit.plugin.java.JavaPlugin  BukkitRunnable org.bukkit.scheduler  BukkitScheduler org.bukkit.scheduler  
BukkitTask org.bukkit.scheduler  	Exception #org.bukkit.scheduler.BukkitRunnable  Level #org.bukkit.scheduler.BukkitRunnable  System #org.bukkit.scheduler.BukkitRunnable  cancel #org.bukkit.scheduler.BukkitRunnable  
configManager #org.bukkit.scheduler.BukkitRunnable  logger #org.bukkit.scheduler.BukkitRunnable  
nextCleanTime #org.bukkit.scheduler.BukkitRunnable  pokemonCleaner #org.bukkit.scheduler.BukkitRunnable  runTaskTimer #org.bukkit.scheduler.BukkitRunnable  sendCountdownAnnouncement #org.bukkit.scheduler.BukkitRunnable  
updateBossBar #org.bukkit.scheduler.BukkitRunnable  runTaskLater $org.bukkit.scheduler.BukkitScheduler                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              