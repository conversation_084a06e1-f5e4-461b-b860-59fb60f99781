package cn.acebrand.AcePokemonCleaner

import org.bukkit.configuration.file.FileConfiguration
import org.bukkit.plugin.Plugin
import org.bukkit.boss.BarColor
import org.bukkit.boss.BarStyle
import java.util.logging.Level

/**
 * BossBar 配置数据类
 */
data class BossBarConfig(
    val message: String,
    val style: BarStyle,
    val color: BarColor
)

/**
 * 配置管理器
 * 负责加载和管理插件配置
 */
class ConfigManager(private val plugin: Plugin) {

    private lateinit var config: FileConfiguration

    // 配置项
    var cleanInterval: Int = 300 // 清理间隔（秒）
    var enabledWorlds: List<String> = listOf("world") // 启用清理的世界
    var excludedPokemon: List<String> = emptyList() // 排除清理的精灵列表
    var maxDistance: Double = 100.0 // 玩家附近多少格内的精灵不被清理
    var minAge: Int = 60 // 精灵存在多少秒后才能被清理
    var enableLogging: Boolean = true // 是否启用详细日志
    var debugMode: Boolean = false // 调试模式
    var licenseKey: String = "" // 许可证密钥
    var cleanOnlyWild: Boolean = true // 是否只清理野生精灵（不清理玩家的精灵）
    var maxCleanPerRun: Int = 50 // 每次清理最多清理多少只精灵
    var enableAnnouncement: Boolean = true // 是否启用清理公告
    var announcementMessage: String = "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，用时 §e{duration}ms" // 公告消息模板
    var minAnnounceCount: Int = 1 // 最少清理多少只精灵才发送公告
    var enableCountdown: Boolean = true // 是否启用倒计时公告
    var countdownInterval: Int = 60 // 倒计时公告间隔（秒）

    // 原版生物清理配置
    var enableVanillaMobCleaning: Boolean = false // 是否启用原版生物清理
    var excludedVanillaMobs: List<String> = emptyList() // 排除清理的原版生物列表
    var vanillaMobMaxCleanPerRun: Int = 30 // 每次清理最多清理多少只原版生物
    var vanillaMobMinAge: Int = 120 // 原版生物存在多少秒后才能被清理
    var protectNamedVanillaMobs: Boolean = true // 是否保护被命名牌命名的原版生物
    var countdownMessage: String = "§6[精灵清理] §f距离下次清理还有 §e{time} §f秒" // 倒计时消息模板
    var countdownTimes: List<Int> = listOf(300, 180, 60, 30, 10) // 在剩余多少秒时发送倒计时公告
    var bossBarFlag: Boolean = false // 是否启用 BossBar 倒计时
    var bossBarMessageForCount: Map<Int, BossBarConfig> = emptyMap() // BossBar 倒计时配置

    // 掉落物清理配置
    var enableItemCleaning: Boolean = false // 是否启用掉落物清理
    var excludedItems: List<String> = emptyList() // 排除清理的掉落物列表
    var itemMaxCleanPerRun: Int = 100 // 每次清理最多清理多少个掉落物
    var itemMinAge: Int = 300 // 掉落物存在多少秒后才能被清理
    var itemMaxDistance: Double = 50.0 // 掉落物清理的最大距离
    var cleanNamedItems: Boolean = false // 是否清理有自定义名称的掉落物
    var cleanEnchantedItems: Boolean = false // 是否清理附魔物品
    var itemRarityProtectionEnabled: Boolean = true // 是否启用稀有度保护
    var protectedRarities: List<String> = listOf("RARE", "EPIC", "LEGENDARY") // 保护的稀有度等级

    // 掉落物排除管理器
    lateinit var itemExclusionManager: ItemExclusionManager

    // 高级设置
    var cleanOwned: Boolean = false // 是否清理被玩家拥有的精灵
    var cleanBattling: Boolean = false // 是否清理正在战斗的精灵
    var cleanBusy: Boolean = false // 是否清理繁忙状态的精灵
    var cleanRiding: Boolean = false // 是否清理被骑乘的精灵
    var cleanUncatchable: Boolean = false // 是否清理不可捕获的精灵

    /**
     * 加载配置文件
     */
    fun loadConfig() {
        // 保存默认配置文件
        plugin.saveDefaultConfig()
        plugin.reloadConfig()
        config = plugin.config

        // 初始化掉落物排除管理器
        if (!::itemExclusionManager.isInitialized) {
            itemExclusionManager = ItemExclusionManager(plugin)
            itemExclusionManager.initialize()
        } else {
            itemExclusionManager.loadConfig()
        }

        try {
            // 读取配置项
            cleanInterval = config.getInt("clean-interval", 300)
            enabledWorlds = config.getStringList("enabled-worlds").takeIf { it.isNotEmpty() }
                ?: listOf("world")
            excludedPokemon = config.getStringList("excluded-pokemon")
            maxDistance = config.getDouble("max-distance", 100.0)
            minAge = config.getInt("min-age", 60)
            enableLogging = config.getBoolean("enable-logging", true)
            debugMode = config.getBoolean("debug-mode", false)
            licenseKey = config.getString("license-key", "") ?: ""
            cleanOnlyWild = config.getBoolean("clean-only-wild", true)
            maxCleanPerRun = config.getInt("max-clean-per-run", 50)
            enableAnnouncement = config.getBoolean("enable-announcement", true)
            announcementMessage = config.getString("announcement-message", "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，用时 §e{duration}ms") ?: "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，用时 §e{duration}ms"

            // 原版生物清理配置
            enableVanillaMobCleaning = config.getBoolean("enable-vanilla-mob-cleaning", false)
            excludedVanillaMobs = config.getStringList("excluded-vanilla-mobs")
            vanillaMobMaxCleanPerRun = config.getInt("vanilla-mob-max-clean-per-run", 30)
            vanillaMobMinAge = config.getInt("vanilla-mob-min-age", 120)
            protectNamedVanillaMobs = config.getBoolean("protect-named-vanilla-mobs", true)
            minAnnounceCount = config.getInt("min-announce-count", 1)
            enableCountdown = config.getBoolean("enable-countdown", true)
            countdownInterval = config.getInt("countdown-interval", 60)
            countdownMessage = config.getString("countdown-message", "§6[精灵清理] §f距离下次清理还有 §e{time} §f秒") ?: "§6[精灵清理] §f距离下次清理还有 §e{time} §f秒"
            countdownTimes = config.getIntegerList("countdown-times").takeIf { it.isNotEmpty() }
                ?: listOf(300, 180, 60, 30, 10)
            bossBarFlag = config.getBoolean("bossbar-flag", false)
            bossBarMessageForCount = loadBossBarConfig(config)

            // 读取掉落物清理配置
            enableItemCleaning = config.getBoolean("enable-item-cleaning", false)
            excludedItems = config.getStringList("excluded-items").takeIf { it.isNotEmpty() }
                ?: listOf("diamond", "emerald", "netherite_ingot", "elytra", "totem_of_undying")
            itemMaxCleanPerRun = config.getInt("item-max-clean-per-run", 100)
            itemMinAge = config.getInt("item-min-age", 300)
            itemMaxDistance = config.getDouble("item-max-distance", 50.0)
            cleanNamedItems = config.getBoolean("clean-named-items", false)
            cleanEnchantedItems = config.getBoolean("clean-enchanted-items", false)
            itemRarityProtectionEnabled = config.getBoolean("item-rarity-protection.enabled", true)
            protectedRarities = config.getStringList("item-rarity-protection.protected-rarities").takeIf { it.isNotEmpty() }
                ?: listOf("RARE", "EPIC", "LEGENDARY")

            // 读取高级设置
            cleanOwned = config.getBoolean("advanced.clean-owned", false)
            cleanBattling = config.getBoolean("advanced.clean-battling", false)
            cleanBusy = config.getBoolean("advanced.clean-busy", false)
            cleanRiding = config.getBoolean("advanced.clean-riding", false)
            cleanUncatchable = config.getBoolean("advanced.clean-uncatchable", false)

            // 验证配置
            validateConfig()

            if (enableLogging) {
                plugin.logger.info("配置加载完成:")
                plugin.logger.info("  清理间隔: ${cleanInterval} 秒")
                plugin.logger.info("  启用世界: $enabledWorlds")
                plugin.logger.info("  排除精灵: $excludedPokemon")
                plugin.logger.info("  最大距离: $maxDistance 格")
                plugin.logger.info("  最小年龄: $minAge 秒")
                plugin.logger.info("  只清理野生: $cleanOnlyWild")
                plugin.logger.info("  每次最大清理数: $maxCleanPerRun")
                plugin.logger.info("  启用公告: $enableAnnouncement")
                plugin.logger.info("  最少公告数量: $minAnnounceCount")
                plugin.logger.info("  启用倒计时: $enableCountdown")
                plugin.logger.info("  倒计时时间点: $countdownTimes")
                plugin.logger.info("  启用 BossBar: $bossBarFlag")
                plugin.logger.info("  BossBar 配置数量: ${bossBarMessageForCount.size}")
                plugin.logger.info("  启用掉落物清理: $enableItemCleaning")
                if (enableItemCleaning) {
                    plugin.logger.info("    排除掉落物: $excludedItems")
                    plugin.logger.info("    掉落物最大清理数: $itemMaxCleanPerRun")
                    plugin.logger.info("    掉落物最小年龄: $itemMinAge 秒")
                    plugin.logger.info("    掉落物最大距离: $itemMaxDistance 格")
                    plugin.logger.info("    清理命名物品: $cleanNamedItems")
                    plugin.logger.info("    清理附魔物品: $cleanEnchantedItems")
                    plugin.logger.info("    稀有度保护: $itemRarityProtectionEnabled")
                    if (itemRarityProtectionEnabled) {
                        plugin.logger.info("    保护稀有度: $protectedRarities")
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "加载配置时发生错误，使用默认配置", e)
            useDefaultConfig()
        }
    }

    /**
     * 验证配置的有效性
     */
    private fun validateConfig() {
        if (cleanInterval < 0) {
            plugin.logger.warning("清理间隔不能为负数，已重置为 300 秒")
            cleanInterval = 300
        }

        if (maxDistance < 0) {
            plugin.logger.warning("最大距离不能为负数，已重置为 100.0")
            maxDistance = 100.0
        }

        if (minAge < 0) {
            plugin.logger.warning("最小年龄不能为负数，已重置为 60 秒")
            minAge = 60
        }

        if (maxCleanPerRun <= 0) {
            plugin.logger.warning("每次最大清理数必须大于 0，已重置为 50")
            maxCleanPerRun = 50
        }

        if (enabledWorlds.isEmpty()) {
            plugin.logger.warning("启用世界列表为空，已添加默认世界 'world'")
            enabledWorlds = listOf("world")
        }
    }

    /**
     * 使用默认配置
     */
    private fun useDefaultConfig() {
        cleanInterval = 300
        enabledWorlds = listOf("world")
        excludedPokemon = emptyList()
        maxDistance = 100.0
        minAge = 60
        enableLogging = true
        cleanOnlyWild = true
        maxCleanPerRun = 50
        enableAnnouncement = true
        announcementMessage = "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，用时 §e{duration}ms"
        minAnnounceCount = 1
        enableCountdown = true
        countdownInterval = 60
        countdownMessage = "§6[精灵清理] §f距离下次清理还有 §e{time} §f秒"
        countdownTimes = listOf(300, 180, 60, 30, 10)
        bossBarFlag = false
        bossBarMessageForCount = getDefaultBossBarConfig()

        // 原版生物清理默认配置
        enableVanillaMobCleaning = false
        excludedVanillaMobs = emptyList()
        vanillaMobMaxCleanPerRun = 30
        vanillaMobMinAge = 120
        protectNamedVanillaMobs = true

        // 掉落物清理默认配置
        enableItemCleaning = false
        excludedItems = listOf("diamond", "emerald", "netherite_ingot", "elytra", "totem_of_undying")
        itemMaxCleanPerRun = 100
        itemMinAge = 300
        itemMaxDistance = 50.0
        cleanNamedItems = false
        cleanEnchantedItems = false
        itemRarityProtectionEnabled = true
        protectedRarities = listOf("RARE", "EPIC", "LEGENDARY")

        // 高级设置默认配置
        cleanOwned = false
        cleanBattling = false
        cleanBusy = false
        cleanRiding = false
        cleanUncatchable = false
    }

    /**
     * 检查指定的精灵是否在排除列表中
     */
    fun isPokemonExcluded(pokemonName: String): Boolean {
        return excludedPokemon.any { excluded ->
            excluded.equals(pokemonName, ignoreCase = true) ||
            pokemonName.contains(excluded, ignoreCase = true)
        }
    }

    /**
     * 检查指定的原版生物是否在排除列表中
     */
    fun isVanillaMobExcluded(mobName: String): Boolean {
        return excludedVanillaMobs.any { excluded ->
            excluded.equals(mobName, ignoreCase = true) ||
            mobName.contains(excluded, ignoreCase = true)
        }
    }

    /**
     * 检查指定的掉落物是否在排除列表中
     * 同时检查配置文件和GUI管理的排除列表
     */
    fun isItemExcluded(itemName: String): Boolean {
        // 检查配置文件中的排除列表
        val configExcluded = excludedItems.any { excluded ->
            isItemMatch(excluded, itemName)
        }

        // 检查GUI管理的排除列表
        val guiExcluded = if (::itemExclusionManager.isInitialized) {
            itemExclusionManager.isItemExcludedByGui(itemName)
        } else {
            false
        }

        if (enableLogging && (configExcluded || guiExcluded)) {
            plugin.logger.info("物品 $itemName 被排除: 配置文件=$configExcluded, GUI=$guiExcluded")
        }

        return configExcluded || guiExcluded
    }

    /**
     * 检查两个物品名称是否匹配
     * 支持不同格式的物品ID匹配，包括基础匹配
     */
    private fun isItemMatch(excluded: String, itemName: String): Boolean {
        // 完全匹配
        if (excluded.equals(itemName, ignoreCase = true)) {
            return true
        }

        // 提取基础ID进行匹配（对于配置文件中的简单ID）
        val itemBaseId = extractBaseId(itemName)
        val excludedBaseId = extractBaseId(excluded)

        // 基础ID匹配
        if (excludedBaseId.equals(itemBaseId, ignoreCase = true)) {
            return true
        }

        // 包含匹配
        if (itemName.contains(excluded, ignoreCase = true) || excluded.contains(itemName, ignoreCase = true)) {
            return true
        }

        if (itemBaseId.contains(excludedBaseId, ignoreCase = true) || excludedBaseId.contains(itemBaseId, ignoreCase = true)) {
            return true
        }

        // 处理命名空间匹配
        val excludedParts = excludedBaseId.split(":")
        val itemParts = itemBaseId.split(":")

        // 如果排除项没有命名空间，但物品有命名空间，比较物品名部分
        if (excludedParts.size == 1 && itemParts.size == 2) {
            return excludedParts[0].equals(itemParts[1], ignoreCase = true)
        }

        // 如果物品没有命名空间，但排除项有命名空间，比较物品名部分
        if (itemParts.size == 1 && excludedParts.size == 2) {
            return itemParts[0].equals(excludedParts[1], ignoreCase = true)
        }

        return false
    }

    /**
     * 提取基础物品ID（去除详细信息）
     */
    private fun extractBaseId(itemInfo: String): String {
        return itemInfo.split(";")[0]
    }

    /**
     * 检查指定的世界是否启用清理
     */
    fun isWorldEnabled(worldName: String): Boolean {
        return enabledWorlds.any { it.equals(worldName, ignoreCase = true) }
    }

    /**
     * 加载 BossBar 配置
     */
    private fun loadBossBarConfig(config: FileConfiguration): Map<Int, BossBarConfig> {
        val bossBarMap = mutableMapOf<Int, BossBarConfig>()

        try {
            val bossBarList = config.getStringList("bossbar-message-for-count")

            for (entry in bossBarList) {
                val parts = entry.split(";")
                if (parts.size >= 4) {
                    val seconds = parts[0].toIntOrNull() ?: continue
                    val message = parts[1]
                    val style = parseBarStyle(parts[2])
                    val color = parseBarColor(parts[3])

                    bossBarMap[seconds] = BossBarConfig(message, style, color)
                }
            }
        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "加载 BossBar 配置时发生错误，使用默认配置", e)
            return getDefaultBossBarConfig()
        }

        return bossBarMap
    }

    /**
     * 解析 BarStyle
     */
    private fun parseBarStyle(styleStr: String): BarStyle {
        return try {
            when (styleStr.uppercase()) {
                "SOLID" -> BarStyle.SOLID
                "SEGMENTED_6" -> BarStyle.SEGMENTED_6
                "SEGMENTED_10" -> BarStyle.SEGMENTED_10
                "SEGMENTED_12" -> BarStyle.SEGMENTED_12
                "SEGMENTED_20" -> BarStyle.SEGMENTED_20
                else -> BarStyle.SOLID
            }
        } catch (e: Exception) {
            BarStyle.SOLID
        }
    }

    /**
     * 解析 BarColor
     */
    private fun parseBarColor(colorStr: String): BarColor {
        return try {
            when (colorStr.uppercase()) {
                "PINK" -> BarColor.PINK
                "BLUE" -> BarColor.BLUE
                "RED" -> BarColor.RED
                "GREEN" -> BarColor.GREEN
                "YELLOW" -> BarColor.YELLOW
                "PURPLE" -> BarColor.PURPLE
                "WHITE" -> BarColor.WHITE
                else -> BarColor.WHITE
            }
        } catch (e: Exception) {
            BarColor.WHITE
        }
    }

    /**
     * 获取默认 BossBar 配置
     */
    private fun getDefaultBossBarConfig(): Map<Int, BossBarConfig> {
        return mapOf(
            300 to BossBarConfig("§b§l【实体清理】距离下次清理还有 5 分钟", BarStyle.SOLID, BarColor.BLUE),
            180 to BossBarConfig("§6§l【实体清理】距离下次清理还有 3 分钟", BarStyle.SOLID, BarColor.YELLOW),
            120 to BossBarConfig("§6§l【实体清理】距离下次清理还有 2 分钟", BarStyle.SOLID, BarColor.YELLOW),
            60 to BossBarConfig("§e§l【实体清理】距离下次清理还有 1 分钟", BarStyle.SOLID, BarColor.YELLOW),
            45 to BossBarConfig("§c§l【实体清理】距离下次清理还有 45 秒", BarStyle.SEGMENTED_6, BarColor.RED),
            30 to BossBarConfig("§c§l【实体清理】距离下次清理还有 30 秒", BarStyle.SEGMENTED_6, BarColor.RED),
            25 to BossBarConfig("§c§l【实体清理】距离下次清理还有 25 秒", BarStyle.SEGMENTED_10, BarColor.RED),
            20 to BossBarConfig("§c§l【实体清理】距离下次清理还有 20 秒", BarStyle.SEGMENTED_10, BarColor.RED),
            15 to BossBarConfig("§c§l【实体清理】距离下次清理还有 15 秒", BarStyle.SEGMENTED_10, BarColor.RED),
            10 to BossBarConfig("§4§l【实体清理】距离下次清理还有 10 秒", BarStyle.SEGMENTED_12, BarColor.RED),
            9 to BossBarConfig("§4§l【实体清理】距离下次清理还有 9 秒", BarStyle.SEGMENTED_12, BarColor.RED),
            8 to BossBarConfig("§4§l【实体清理】距离下次清理还有 8 秒", BarStyle.SEGMENTED_12, BarColor.RED),
            7 to BossBarConfig("§4§l【实体清理】距离下次清理还有 7 秒", BarStyle.SEGMENTED_12, BarColor.RED),
            6 to BossBarConfig("§4§l【实体清理】距离下次清理还有 6 秒", BarStyle.SEGMENTED_12, BarColor.RED),
            5 to BossBarConfig("§4§l【实体清理】距离下次清理还有 5 秒", BarStyle.SEGMENTED_20, BarColor.RED),
            4 to BossBarConfig("§4§l【实体清理】距离下次清理还有 4 秒", BarStyle.SEGMENTED_20, BarColor.RED),
            3 to BossBarConfig("§4§l【实体清理】距离下次清理还有 3 秒", BarStyle.SEGMENTED_20, BarColor.RED),
            2 to BossBarConfig("§4§l【实体清理】距离下次清理还有 2 秒", BarStyle.SEGMENTED_20, BarColor.RED),
            1 to BossBarConfig("§4§l【实体清理】距离下次清理还有 1 秒", BarStyle.SEGMENTED_20, BarColor.RED),
            0 to BossBarConfig("§a§l【清理完成】精灵: {pokemonCount} 只 | 原版生物: {vanillaCount} 只 | 总计: {count} 个", BarStyle.SOLID, BarColor.GREEN)
        )
    }
}
