{"md5": "2ae3924fcf4bcc24e86caa7384b6d490", "sha2": "bad4e2c689e72b2415c3b2503a30321b70224f1a", "sha256": "386544541a807ade0f13fd56de34a3343f6511016f8e2c67a172e3c1f7b299f0", "contents": {"classes": {"classes/sun/tools/jconsole/BorderedComponent$ArrowIcon.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/BorderedComponent$ArrowIcon", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ILjavax/swing/JLabel;)V"}, {"nme": "paintIcon", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;Ljava/awt/Graphics;II)V"}, {"nme": "getIconWidth", "acc": 1, "dsc": "()I"}, {"nme": "getIconHeight", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "direction", "dsc": "I"}, {"acc": 2, "nme": "textLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanInfo$ReadOnlyDefaultTableModel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanInfo$ReadOnlyDefaultTableModel", "super": "javax/swing/table/DefaultTableModel", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "setValueAt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;II)V"}], "flds": []}, "classes/sun/tools/jconsole/BorderedComponent$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/BorderedComponent$1", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/BorderedComponent;Ljava/awt/LayoutManager;Ljavax/swing/JLabel;)V"}, {"nme": "getBaseline", "acc": 1, "dsc": "(II)I"}], "flds": [{"acc": 4112, "nme": "val$textLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/BorderedComponent;"}]}, "classes/sun/tools/jconsole/ProxyClient$SnapshotInvocationHandler$NameValueMap.class": {"ver": 65, "acc": 48, "nme": "sun/tools/jconsole/ProxyClient$SnapshotInvocationHandler$NameValueMap", "super": "java/util/HashMap", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": []}, "classes/sun/tools/jconsole/ThreadTab$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}]}, "classes/sun/tools/jconsole/AboutDialog$TPanel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$TPanel", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(II)V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XViewedArrayData.class": {"ver": 65, "acc": 1536, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XViewedArrayData", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/sun/tools/jconsole/Version.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/Version", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "print", "acc": 9, "dsc": "(Ljava/io/PrintStream;)V"}, {"nme": "printFullVersion", "acc": 9, "dsc": "(Ljava/io/PrintStream;)V"}, {"nme": "getVersion", "acc": 8, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "jconsole_version", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/ThreadTab$ThreadJList.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$ThreadJList", "super": "javax/swing/JList", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab;Ljavax/swing/DefaultListModel;Ljavax/swing/JTextArea;)V", "sig": "(Ljavax/swing/DefaultListModel<Ljava/lang/Long;>;Ljavax/swing/JTextArea;)V"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}], "flds": [{"acc": 2, "nme": "textArea", "dsc": "Ljavax/swing/JTextArea;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}]}, "classes/sun/tools/jconsole/JConsole$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$3", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Z)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$hostName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$port", "dsc": "I"}, {"acc": 4112, "nme": "val$userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$tile", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/Worker.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/Worker", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "stopWorker", "acc": 1, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "queueFull", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 0, "nme": "jobs", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/Runnable;>;"}, {"acc": 66, "nme": "stopped", "dsc": "Z"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$Navigatable.class": {"ver": 65, "acc": 1536, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$Navigatable", "super": "java/lang/Object", "mthds": [{"nme": "incrElement", "acc": 1025, "dsc": "()V"}, {"nme": "decrElement", "acc": 1025, "dsc": "()V"}, {"nme": "canDecrement", "acc": 1025, "dsc": "()Z"}, {"nme": "canIncrement", "acc": 1025, "dsc": "()Z"}, {"nme": "getElementCount", "acc": 1025, "dsc": "()I"}, {"nme": "getSelectedElementIndex", "acc": 1025, "dsc": "()I"}], "flds": []}, "classes/sun/tools/jconsole/ClassTab.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ClassTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "getOverviewPanels", "acc": 0, "dsc": "()[Lsun/tools/jconsole/OverviewPanel;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "loadedClassesMeter", "dsc": "Lsun/tools/jconsole/PlotterPanel;"}, {"acc": 0, "nme": "timeComboBox", "dsc": "Lsun/tools/jconsole/TimeComboBox;"}, {"acc": 2, "nme": "verboseCheckBox", "dsc": "Ljavax/swing/J<PERSON>heckBox;"}, {"acc": 2, "nme": "details", "dsc": "Lsun/tools/jconsole/HTMLPane;"}, {"acc": 2, "nme": "overviewPanel", "dsc": "Lsun/tools/jconsole/ClassTab$ClassOverviewPanel;"}, {"acc": 2, "nme": "plotterListening", "dsc": "Z"}, {"acc": 26, "nme": "loadedPlotterKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "loaded"}, {"acc": 26, "nme": "totalLoadedPlotterKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "totalLoaded"}, {"acc": 26, "nme": "loadedPlotterColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "totalLoadedPlotterColor", "dsc": "Ljava/awt/Color;"}]}, "classes/sun/tools/jconsole/SummaryTab$Result.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SummaryTab$Result", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "upTime", "dsc": "J"}, {"acc": 0, "nme": "processCpuTime", "dsc": "J"}, {"acc": 0, "nme": "timeStamp", "dsc": "J"}, {"acc": 0, "nme": "nCPUs", "dsc": "I"}, {"acc": 0, "nme": "summary", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/MemoryTab$PoolChart.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$PoolChart", "super": "sun/tools/jconsole/BorderedComponent", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MemoryTab;)V"}, {"nme": "setValue", "acc": 1, "dsc": "(ILsun/tools/jconsole/MemoryTab$PoolPlotter;JJJ)V"}, {"nme": "paintPoolBar", "acc": 2, "dsc": "(Ljava/awt/Graphics;Lsun/tools/jconsole/MemoryTab$PoolPlotter;)V"}, {"nme": "paintComponent", "acc": 1, "dsc": "(Ljava/awt/Graphics;)V"}, {"nme": "getBarRect", "acc": 2, "dsc": "(Lsun/tools/jconsole/MemoryTab$PoolPlotter;)Ljava/awt/Rectangle;"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}, {"nme": "mouseClicked", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "getToolTipText", "acc": 1, "dsc": "(Lja<PERSON>/awt/event/MouseEvent;)Ljava/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljava/awt/event/MouseEvent;)Lsun/tools/jconsole/Plotter;"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseReleased", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseEntered", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseExited", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "getAccessibleContext", "acc": 1, "dsc": "()Ljavax/accessibility/AccessibleContext;"}], "flds": [{"acc": 16, "nme": "height", "dsc": "I", "val": 150}, {"acc": 16, "nme": "leftMargin", "dsc": "I", "val": 50}, {"acc": 16, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I", "val": 23}, {"acc": 16, "nme": "bottom<PERSON>argin", "dsc": "I", "val": 35}, {"acc": 16, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I", "val": 22}, {"acc": 16, "nme": "barGap", "dsc": "I", "val": 3}, {"acc": 16, "nme": "groupGap", "dsc": "I", "val": 8}, {"acc": 16, "nme": "barHeight", "dsc": "I", "val": 100}, {"acc": 16, "nme": "greenBar", "dsc": "Ljava/awt/Color;"}, {"acc": 16, "nme": "greenBarBackground", "dsc": "Ljava/awt/Color;"}, {"acc": 16, "nme": "redBarBackground", "dsc": "Ljava/awt/Color;"}, {"acc": 0, "nme": "smallFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 0, "nme": "poolPlotters", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/MemoryTab$PoolPlotter;>;"}, {"acc": 0, "nme": "nHeapPools", "dsc": "I"}, {"acc": 0, "nme": "nNonHeapPools", "dsc": "I"}, {"acc": 0, "nme": "heapRect", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 0, "nme": "nonHeapRect", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MemoryTab;"}]}, "classes/sun/tools/jconsole/inspector/Utils$CopyKeyAdapter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/Utils$CopyKeyAdapter", "super": "java/awt/event/KeyAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "keyPressed", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}, {"nme": "keyTyped", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultEditorKitCopyActionName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "copy-to-clipboard"}, {"acc": 26, "nme": "transferHandlerCopyActionName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/MemoryPoolStat.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/MemoryPoolStat", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;JLjava/lang/management/MemoryUsage;JJJJLjava/lang/management/MemoryUsage;Ljava/lang/management/MemoryUsage;)V"}, {"nme": "getPoolName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUsage", "acc": 1, "dsc": "()Ljava/lang/management/MemoryUsage;"}, {"nme": "getUsageThreshold", "acc": 1, "dsc": "()J"}, {"nme": "getCollectionUsageThreshold", "acc": 1, "dsc": "()J"}, {"nme": "getLastGcId", "acc": 1, "dsc": "()J"}, {"nme": "getLastGcStartTime", "acc": 1, "dsc": "()J"}, {"nme": "getLastGcEndTime", "acc": 1, "dsc": "()J"}, {"nme": "getBeforeGcUsage", "acc": 1, "dsc": "()Ljava/lang/management/MemoryUsage;"}, {"nme": "getAfterGcUsage", "acc": 1, "dsc": "()Ljava/lang/management/MemoryUsage;"}], "flds": [{"acc": 2, "nme": "poolName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "usageThreshold", "dsc": "J"}, {"acc": 2, "nme": "usage", "dsc": "Ljava/lang/management/MemoryUsage;"}, {"acc": 2, "nme": "lastGcId", "dsc": "J"}, {"acc": 2, "nme": "lastGcStartTime", "dsc": "J"}, {"acc": 2, "nme": "lastGcEndTime", "dsc": "J"}, {"acc": 2, "nme": "collectThreshold", "dsc": "J"}, {"acc": 2, "nme": "beforeGcUsage", "dsc": "Ljava/lang/management/MemoryUsage;"}, {"acc": 2, "nme": "afterGcUsage", "dsc": "Ljava/lang/management/MemoryUsage;"}]}, "classes/sun/tools/jconsole/MemoryTab$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$3", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MemoryTab;Ljava/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MemoryTab;"}]}, "classes/sun/tools/jconsole/inspector/OperationEntry.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/OperationEntry", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/MBeanOperationInfo;ZLjavax/swing/JButton;Lsun/tools/jconsole/inspector/XOperations;)V"}, {"nme": "setPanel", "acc": 2, "dsc": "(ZLjavax/swing/JButton;Lsun/tools/jconsole/inspector/XOperations;)V"}, {"nme": "getSignature", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParameters", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getReturnType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "operation", "dsc": "Ljavax/management/MBeanOperationInfo;"}, {"acc": 2, "nme": "inputs", "dsc": "[Lsun/tools/jconsole/inspector/XTextField;"}]}, "classes/sun/tools/jconsole/ThreadTab$ThreadJList$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$ThreadJList$1", "super": "javax/swing/DefaultList<PERSON>ell<PERSON><PERSON><PERSON>", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$ThreadJList;Lsun/tools/jconsole/ThreadTab;)V"}, {"nme": "getListCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JList;<PERSON><PERSON><PERSON>/lang/Object;IZZ)Ljava/awt/Component;", "sig": "(Ljavax/swing/JList<*>;Ljava/lang/Object;IZZ)Ljava/awt/Component;"}], "flds": [{"acc": 4112, "nme": "val$this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ThreadTab$ThreadJList;"}]}, "classes/sun/tools/jconsole/ThreadTab$4$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$4$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$4;[[<PERSON><PERSON><PERSON>/lang/Long;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$deadlockedThreads", "dsc": "[[<PERSON><PERSON><PERSON>/lang/Long;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ThreadTab$4;"}]}, "classes/sun/tools/jconsole/OverviewTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OverviewTab$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/OverviewTab;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "done", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/OverviewTab;"}]}, "classes/sun/tools/jconsole/SheetDialog$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/SheetDialog$SheetOptionPane;)V", "sig": "()V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}], "flds": [{"acc": 4112, "nme": "val$optionPane", "dsc": "Lsun/tools/jconsole/SheetDialog$SheetOptionPane;"}]}, "classes/sun/tools/jconsole/inspector/XTree$MBeanInfoNodesSwingWorker.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XTree$MBeanInfoNodesSwingWorker", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultTreeModel;Ljavax/swing/tree/DefaultMutableTreeNode;Lsun/tools/jconsole/inspector/XMBean;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "addMBeanInfoNodes", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultTreeModel;Ljavax/swing/tree/DefaultMutableTreeNode;Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;Ljava/lang/<PERSON>;)V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "model", "dsc": "Ljavax/swing/tree/DefaultTreeModel;"}, {"acc": 18, "nme": "node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 18, "nme": "mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}]}, "classes/sun/tools/jconsole/VMPanel$5.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/ConnectDialog$LocalTabJTable.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$LocalTabJTable", "super": "javax/swing/JTable", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/ConnectDialog;Lsun/tools/jconsole/ConnectDialog$ManagedVmTableModel;)V"}, {"nme": "getToolTipText", "acc": 1, "dsc": "(Lja<PERSON>/awt/event/MouseEvent;)Ljava/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(II)Ljavax/swing/table/TableCellRenderer;"}], "flds": [{"acc": 0, "nme": "vmModel", "dsc": "Lsun/tools/jconsole/ConnectDialog$ManagedVmTableModel;"}, {"acc": 0, "nme": "rendererBorder", "dsc": "Ljavax/swing/border/Border;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}]}, "classes/sun/tools/jconsole/inspector/XTree.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XTree", "super": "javax/swing/JTree", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/tree/TreeNode;Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "removeChildNode", "acc": 34, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "addChildNode", "acc": 34, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;Ljavax/swing/tree/DefaultMutableTreeNode;I)V"}, {"nme": "addChildNode", "acc": 34, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "removeAll", "acc": 33, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 33, "dsc": "(Ljavax/management/ObjectName;)V"}, {"nme": "hasNonMetadataNodes", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "hasMetadataNodes", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "isMetadataNode", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "removeMetadataNodes", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "remove<PERSON><PERSON>nt<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Dn;ILjavax/swing/tree/DefaultMutableTreeNode;)Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"nme": "addMBeansToView", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljavax/management/ObjectName;>;)V"}, {"nme": "addMBeanToView", "acc": 33, "dsc": "(Ljavax/management/ObjectName;)V"}, {"nme": "addMBeanToView", "acc": 34, "dsc": "(Ljavax/management/ObjectName;Lsun/tools/jconsole/inspector/XMBean;Lsun/tools/jconsole/inspector/XTree$Dn;)V"}, {"nme": "changeNodeValue", "acc": 34, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;Lsun/tools/jconsole/inspector/XNodeInfo;)V"}, {"nme": "createDomainNode", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Dn;Lsun/tools/jconsole/inspector/XTree$Token;)Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"nme": "createDnNode", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Dn;Lsun/tools/jconsole/inspector/XTree$Token;Lsun/tools/jconsole/inspector/XMBean;)Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"nme": "createSubDnNode", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Dn;Lsun/tools/jconsole/inspector/XTree$Token;)Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"nme": "createNodeValue", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Lsun/tools/jconsole/inspector/XTree$Token;)Ljava/lang/Object;"}, {"nme": "extractKeyValuePairs", "acc": 10, "dsc": "(Ljava/lang/String;Ljavax/management/ObjectName;)Ljava/util/Map;", "sig": "(Ljava/lang/String;Ljavax/management/ObjectName;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getKeyPropertyListString", "acc": 10, "dsc": "(Ljavax/management/ObjectName;)Ljava/lang/String;"}, {"nme": "addMetadataNodes", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "isTreeView", "acc": 10, "dsc": "()Z"}, {"nme": "getTreeViewValue", "acc": 10, "dsc": "()Z"}, {"nme": "isKeyValueView", "acc": 2, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "orderedKeyPropertyList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "mbeansTab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}, {"acc": 2, "nme": "nodes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljavax/swing/tree/DefaultMutableTreeNode;>;"}, {"acc": 10, "nme": "tree<PERSON>iew", "dsc": "Z"}, {"acc": 10, "nme": "treeViewInit", "dsc": "Z"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}]}, "classes/sun/tools/jconsole/MaximizableInternalFrame$FixedMenuBarLayout.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MaximizableInternalFrame$FixedMenuBarLayout", "super": "javax/swing/BoxLayout", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;I)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)V"}], "flds": []}, "classes/sun/tools/jconsole/AboutDialog$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/AboutDialog;)V"}, {"nme": "hyperlinkUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/HyperlinkEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/AboutDialog;"}]}, "classes/sun/tools/jconsole/ConnectDialog$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$1", "super": "javax/swing/AbstractAction", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog;Ljava/lang/String;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}]}, "classes/sun/tools/jconsole/JConsole$3$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$3$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole$3;Ljava/io/IOException;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$ex", "dsc": "Ljava/io/IOException;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/JConsole$3;"}]}, "classes/sun/tools/jconsole/ProxyClient.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/ProxyClient", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;)V", "exs": ["java/io/IOException"]}, {"nme": "setParameters", "acc": 2, "dsc": "(Ljavax/management/remote/JMXServiceURL;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "checkStub", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Lja<PERSON>/lang/Class;)V", "sig": "(Lja<PERSON>/rmi/Remote;Ljava/lang/Class<+Ljava/rmi/Remote;>;)V"}, {"nme": "checkSslConfig", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isSslRmiRegistry", "acc": 1, "dsc": "()Z"}, {"nme": "isSslRmiStub", "acc": 1, "dsc": "()Z"}, {"nme": "isVmConnector", "acc": 1, "dsc": "()Z"}, {"nme": "setConnectionState", "acc": 2, "dsc": "(Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;)V"}, {"nme": "getConnectionState", "acc": 1, "dsc": "()Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"nme": "flush", "acc": 0, "dsc": "()V"}, {"nme": "connect", "acc": 0, "dsc": "(Z)V"}, {"nme": "tryConnect", "acc": 2, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "getProxyClient", "acc": 9, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;)Lsun/tools/jconsole/ProxyClient;", "exs": ["java/io/IOException"]}, {"nme": "getConnectionName", "acc": 9, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;)Ljava/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;)Ljava/lang/String;"}, {"nme": "getProxyClient", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)Lsun/tools/jconsole/ProxyClient;", "exs": ["java/io/IOException"]}, {"nme": "getConnectionName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "getProxyClient", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;ILjava/lang/String;Ljava/lang/String;)Lsun/tools/jconsole/ProxyClient;", "exs": ["java/io/IOException"]}, {"nme": "getConnectionName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)<PERSON>java/lang/String;"}, {"nme": "connectionName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDisplayName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMBeanServerConnection", "acc": 1, "dsc": "()Ljavax/management/MBeanServerConnection;"}, {"nme": "getSnapshotMBeanServerConnection", "acc": 1, "dsc": "()Lsun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getHostName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "getVmid", "acc": 1, "dsc": "()I"}, {"nme": "getUserName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPassword", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "disconnect", "acc": 1, "dsc": "()V"}, {"nme": "getDomains", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "getMBeans", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Map;", "sig": "(Ljava/lang/String;)Ljava/util/Map<Ljavax/management/ObjectName;Ljavax/management/MBeanInfo;>;", "exs": ["java/io/IOException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;)Ljavax/management/AttributeList;", "exs": ["java/io/IOException"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljavax/management/Attribute;)V", "exs": ["javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "java/io/IOException"]}, {"nme": "invoke", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;[Ljava/lang/Object;[Ljava/lang/String;)Ljava/lang/Object;", "exs": ["java/io/IOException", "javax/management/MBeanException"]}, {"nme": "getClassLoadingMXBean", "acc": 33, "dsc": "()Ljava/lang/management/ClassLoadingMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getCompilationMXBean", "acc": 33, "dsc": "()Ljava/lang/management/CompilationMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getMemoryPoolProxies", "acc": 1, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lsun/tools/jconsole/MemoryPoolProxy;>;", "exs": ["java/io/IOException"]}, {"nme": "getGarbageCollectorMXBeans", "acc": 33, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/management/GarbageCollectorMXBean;>;", "exs": ["java/io/IOException"]}, {"nme": "get<PERSON><PERSON>oryMX<PERSON>ean", "acc": 33, "dsc": "()Ljava/lang/management/MemoryMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getRuntimeMXBean", "acc": 33, "dsc": "()Ljava/lang/management/RuntimeMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getThreadMXBean", "acc": 33, "dsc": "()Ljava/lang/management/ThreadMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getOperatingSystemMXBean", "acc": 33, "dsc": "()Ljava/lang/management/OperatingSystemMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getSunOperatingSystemMXBean", "acc": 33, "dsc": "()Lcom/sun/management/OperatingSystemMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getHotSpotDiagnosticMXBean", "acc": 33, "dsc": "()Lcom/sun/management/HotSpotDiagnosticMXBean;", "exs": ["java/io/IOException"]}, {"nme": "getMXBean", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/Class;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljavax/management/ObjectName;Ljava/lang/Class<TT;>;)TT;", "exs": ["java/io/IOException"]}, {"nme": "findDeadlockedThreads", "acc": 1, "dsc": "()[J", "exs": ["java/io/IOException"]}, {"nme": "mark<PERSON><PERSON><PERSON>", "acc": 33, "dsc": "()V"}, {"nme": "isDead", "acc": 1, "dsc": "()Z"}, {"nme": "isConnected", "acc": 0, "dsc": "()Z"}, {"nme": "hasPlatformMXBeans", "acc": 0, "dsc": "()Z"}, {"nme": "hasHotSpotDiagnosticMXBean", "acc": 0, "dsc": "()Z"}, {"nme": "isLockUsageSupported", "acc": 0, "dsc": "()Z"}, {"nme": "isRegistered", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)Z", "exs": ["java/io/IOException"]}, {"nme": "addPropertyChangeListener", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}, {"nme": "addWeakPropertyChangeListener", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}, {"nme": "removePropertyChangeListener", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "connectionState", "dsc": "Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"acc": 2, "nme": "propertyChangeSupport", "dsc": "Ljavax/swing/event/SwingPropertyChangeSupport;"}, {"acc": 10, "nme": "cache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Lsun/tools/jconsole/ProxyClient;>;"}, {"acc": 66, "nme": "isDead", "dsc": "Z"}, {"acc": 2, "nme": "hostName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 2, "nme": "userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "hasPlatformMXBeans", "dsc": "Z"}, {"acc": 2, "nme": "hasHotSpotDiagnosticMXBean", "dsc": "Z"}, {"acc": 2, "nme": "hasCompilationMXBean", "dsc": "Z"}, {"acc": 2, "nme": "supportsLockUsage", "dsc": "Z"}, {"acc": 2, "nme": "lvm", "dsc": "Lsun/tools/jconsole/LocalVirtualMachine;"}, {"acc": 2, "nme": "advancedUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "jmxUrl", "dsc": "Ljavax/management/remote/JMXServiceURL;"}, {"acc": 2, "nme": "mbsc", "dsc": "Ljavax/management/MBeanServerConnection;"}, {"acc": 2, "nme": "server", "dsc": "Lsun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection;"}, {"acc": 2, "nme": "jmxc", "dsc": "Ljavax/management/remote/JMXConnector;"}, {"acc": 2, "nme": "stub", "dsc": "Ljavax/management/remote/rmi/RMIServer;"}, {"acc": 26, "nme": "sslRMIClientSocketFactory", "dsc": "Ljavax/rmi/ssl/SslRMIClientSocketFactory;"}, {"acc": 2, "nme": "registryHostName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "registryPort", "dsc": "I"}, {"acc": 2, "nme": "vmConnector", "dsc": "Z"}, {"acc": 2, "nme": "sslRegistry", "dsc": "Z"}, {"acc": 2, "nme": "sslStub", "dsc": "Z"}, {"acc": 18, "nme": "connectionName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "displayName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "classLoadingMBean", "dsc": "Ljava/lang/management/ClassLoadingMXBean;"}, {"acc": 2, "nme": "compilationMBean", "dsc": "Ljava/lang/management/CompilationMXBean;"}, {"acc": 2, "nme": "memoryMBean", "dsc": "Ljava/lang/management/MemoryMXBean;"}, {"acc": 2, "nme": "operatingSystemMBean", "dsc": "Ljava/lang/management/OperatingSystemMXBean;"}, {"acc": 2, "nme": "runtimeMBean", "dsc": "Ljava/lang/management/RuntimeMXBean;"}, {"acc": 2, "nme": "threadMBean", "dsc": "Ljava/lang/management/ThreadMXBean;"}, {"acc": 2, "nme": "sunOperatingSystemMXBean", "dsc": "Lcom/sun/management/OperatingSystemMXBean;"}, {"acc": 2, "nme": "hotspotDiagnosticMXBean", "dsc": "Lcom/sun/management/HotSpotDiagnosticMXBean;"}, {"acc": 2, "nme": "memoryPoolProxies", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/tools/jconsole/MemoryPoolProxy;>;"}, {"acc": 2, "nme": "garbageCollectorMBeans", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/management/GarbageCollectorMXBean;>;"}, {"acc": 26, "nme": "HOTSPOT_DIAGNOSTIC_MXBEAN_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management:type=HotSpotDiagnostic"}, {"acc": 26, "nme": "rmiServerImplStubClassName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "javax.management.remote.rmi.RMIServerImpl_Stub"}, {"acc": 26, "nme": "rmiServerImplStubClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Ljava/rmi/Remote;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/tools/jconsole/ConnectDialog$ManagedVmTableModel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$ManagedVmTableModel", "super": "javax/swing/table/AbstractTableModel", "mthds": [{"nme": "getColumnCount", "acc": 1, "dsc": "()I"}, {"nme": "getColumnName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getRowCount", "acc": 33, "dsc": "()I"}, {"nme": "getValueAt", "acc": 33, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getColumnClass", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Class;", "sig": "(I)<PERSON><PERSON><PERSON>/lang/Class<*>;"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "vmAt", "acc": 33, "dsc": "(I)Lsun/tools/jconsole/LocalVirtualMachine;"}, {"nme": "refresh", "acc": 33, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "columnNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "vmList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/tools/jconsole/LocalVirtualMachine;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/tools/jconsole/SummaryTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SummaryTab$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/SummaryTab;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()Lsun/tools/jconsole/SummaryTab$Result;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/SummaryTab;"}]}, "classes/sun/tools/jconsole/ThreadTab$2$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$2$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$2;Ljava/lang/String;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$text", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ThreadTab$2;"}]}, "classes/sun/tools/jconsole/Plotter$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Plotter$1", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/Plotter;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/Plotter;"}]}, "classes/sun/tools/jconsole/MemoryTab$PoolPlotter.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$PoolPlotter", "super": "sun/tools/jconsole/Plotter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 0, "nme": "objectName", "dsc": "Ljavax/management/ObjectName;"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "isHeap", "dsc": "Z"}, {"acc": 0, "nme": "value", "dsc": "J"}, {"acc": 0, "nme": "threshold", "dsc": "J"}, {"acc": 0, "nme": "max", "dsc": "J"}, {"acc": 0, "nme": "barX", "dsc": "I"}]}, "classes/sun/tools/jconsole/OverviewTab.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OverviewTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}], "flds": [{"acc": 0, "nme": "gridPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 0, "nme": "timeComboBox", "dsc": "Lsun/tools/jconsole/TimeComboBox;"}]}, "classes/sun/tools/jconsole/Plotter$Unit.class": {"ver": 65, "acc": 16433, "nme": "sun/tools/jconsole/Plotter$Unit", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lsun/tools/jconsole/Plotter$Unit;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lsun/tools/jconsole/Plotter$Unit;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lsun/tools/jconsole/Plotter$Unit;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Lsun/tools/jconsole/Plotter$Unit;"}, {"acc": 16409, "nme": "BYTES", "dsc": "Lsun/tools/jconsole/Plotter$Unit;"}, {"acc": 16409, "nme": "PERCENT", "dsc": "Lsun/tools/jconsole/Plotter$Unit;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lsun/tools/jconsole/Plotter$Unit;"}]}, "classes/sun/tools/jconsole/VariableGridLayout.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/VariableGridLayout", "super": "java/awt/GridLayout", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(IIIIZZ)V"}, {"nme": "setFillRow", "acc": 1, "dsc": "(Ljavax/swing/JComponent;Z)V"}, {"nme": "setFillColumn", "acc": 1, "dsc": "(Ljavax/swing/JComponent;Z)V"}, {"nme": "getFillRow", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)Z"}, {"nme": "getFillColumn", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)V"}, {"nme": "preferredLayoutSize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)<PERSON><PERSON><PERSON>/awt/Dimension;"}], "flds": [{"acc": 2, "nme": "fillRows", "dsc": "Z"}, {"acc": 2, "nme": "fillColumns", "dsc": "Z"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XTabularData.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XTabularData", "super": "sun/tools/jconsole/inspector/XOpenTypeViewer$XCompositeData", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;Ljavax/management/openmbean/TabularData;)V"}, {"nme": "accessFirstElement", "acc": 10, "dsc": "(Ljavax/management/openmbean/TabularData;)Ljavax/management/openmbean/CompositeData;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "getElementCount", "acc": 1, "dsc": "()I"}, {"nme": "getSelectedElementIndex", "acc": 1, "dsc": "()I"}, {"nme": "incrElement", "acc": 1, "dsc": "()V"}, {"nme": "decrElement", "acc": 1, "dsc": "()V"}, {"nme": "canDecrement", "acc": 1, "dsc": "()Z"}, {"nme": "canIncrement", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "tabular", "dsc": "Ljavax/management/openmbean/TabularData;"}, {"acc": 16, "nme": "type", "dsc": "Ljavax/management/openmbean/TabularType;"}, {"acc": 0, "nme": "currentIndex", "dsc": "I"}, {"acc": 16, "nme": "elements", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 16, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "normalFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "italicFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}]}, "classes/sun/tools/jconsole/ConnectDialog$LocalTabJTable$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$LocalTabJTable$1", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog$LocalTabJTable;Lsun/tools/jconsole/ConnectDialog;)V"}, {"nme": "mouseClicked", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "val$this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ConnectDialog$LocalTabJTable;"}]}, "classes/sun/tools/jconsole/ProxyClient$WeakPCL.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ProxyClient$WeakPCL", "super": "java/lang/ref/WeakReference", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ProxyClient;Ljava/beans/PropertyChangeListener;)V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}, {"nme": "dispose", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ProxyClient;"}]}, "classes/sun/tools/jconsole/inspector/XPlottingViewer$2$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XPlottingViewer$2$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XPlottingViewer$2;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/inspector/XPlottingViewer$2;"}]}, "classes/sun/tools/jconsole/AboutDialog$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/AboutDialog;)V"}, {"nme": "focusGained", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/AboutDialog;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;Ljava/awt/Component;)V"}, {"nme": "tableChanged", "acc": 1, "dsc": "(Ljavax/swing/event/TableModelEvent;)V"}, {"nme": "setAttribute", "acc": 2, "dsc": "(Ljavax/management/Attribute;Ljava/lang/String;)V"}, {"nme": "popupAndLog", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "component", "dsc": "L<PERSON><PERSON>/awt/Component;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/BorderedComponent$FocusBorder.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/BorderedComponent$FocusBorder", "super": "javax/swing/border/AbstractBorder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "paintBorder", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/awt/Component;Ljava/awt/Graphics;IIII)V"}, {"nme": "getBorderInsets", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;<PERSON><PERSON><PERSON>/awt/Insets;)<PERSON><PERSON><PERSON>/awt/Insets;"}, {"nme": "focusGained", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}], "flds": [{"acc": 2, "nme": "comp", "dsc": "L<PERSON><PERSON>/awt/Component;"}, {"acc": 2, "nme": "focusColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "focusLostTemporarily", "dsc": "Z"}]}, "classes/sun/tools/jconsole/VMPanel$10.class": {"ver": 65, "acc": 4128, "nme": "sun/tools/jconsole/VMPanel$10", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$sun$tools$jconsole$JConsoleContext$ConnectionState", "dsc": "[I"}]}, "classes/sun/tools/jconsole/OutputViewer$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OutputViewer$1", "super": "java/awt/event/WindowAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "windowClosing", "acc": 1, "dsc": "(Ljava/awt/event/WindowEvent;)V"}], "flds": []}, "classes/sun/tools/jconsole/LabeledComponent.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/LabeledComponent", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljavax/swing/JComponent;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;ILjavax/swing/JComponent;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setValueLabel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "updateLabel", "acc": 2, "dsc": "()V"}, {"nme": "layout", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)V"}], "flds": [{"acc": 0, "nme": "rightPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 0, "nme": "labelStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "valueLabelStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "compoundStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "label", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "comp", "dsc": "Ljavax/swing/JComponent;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanInfo$TableRowDivider.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanInfo$TableRowDivider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "tableRowDividerText", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/SummaryTab.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SummaryTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "formatSummary", "acc": 32, "dsc": "()Lsun/tools/jconsole/SummaryTab$Result;"}, {"nme": "tryToGet", "acc": 130, "dsc": "([Lja<PERSON>/util/function/LongSupplier;)J"}, {"nme": "append", "acc": 34, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "append", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "append", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getOverviewPanels", "acc": 0, "dsc": "()[Lsun/tools/jconsole/OverviewPanel;"}], "flds": [{"acc": 26, "nme": "cpuUsageKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "cpu"}, {"acc": 26, "nme": "newDivider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<tr><td colspan=4><font size =-1><hr>"}, {"acc": 26, "nme": "newTable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<tr><td colspan=4 align=left><table cellpadding=1>"}, {"acc": 26, "nme": "newLeftTable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<tr><td colspan=2 align=left><table cellpadding=1>"}, {"acc": 26, "nme": "newRightTable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "<td colspan=2 align=left><table cellpadding=1>"}, {"acc": 26, "nme": "endTable", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "</table>"}, {"acc": 26, "nme": "CPU_DECIMALS", "dsc": "I", "val": 1}, {"acc": 2, "nme": "overviewPanel", "dsc": "Lsun/tools/jconsole/SummaryTab$CPUOverviewPanel;"}, {"acc": 2, "nme": "headerDateTimeFormat", "dsc": "Ljava/text/DateFormat;"}, {"acc": 2, "nme": "pathSeparator", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "info", "dsc": "Lsun/tools/jconsole/HTMLPane;"}, {"acc": 0, "nme": "buf", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$MaximizedCellRenderer.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$MaximizedCellRenderer", "super": "javax/swing/table/DefaultTableCellRenderer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "getTableCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZZII)Ljava/awt/Component;"}, {"nme": "getComponent", "acc": 1, "dsc": "()L<PERSON>va/awt/Component;"}], "flds": [{"acc": 0, "nme": "comp", "dsc": "L<PERSON><PERSON>/awt/Component;"}]}, "classes/sun/tools/jconsole/ConnectDialog$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$2", "super": "javax/swing/AbstractAction", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog;Ljava/lang/String;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}]}, "classes/sun/tools/jconsole/CreateMBeanDialog$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/CreateMBeanDialog$2", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/CreateMBeanDialog;Ljava/lang/String;Ljava/awt/event/ActionEvent;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$ev", "dsc": "Ljava/awt/event/ActionEvent;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/CreateMBeanDialog;"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$TabularDataComparator.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$TabularDataComparator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/openmbean/TabularType;)V"}, {"nme": "compare", "acc": 1, "dsc": "(Ljavax/management/openmbean/CompositeData;Ljavax/management/openmbean/CompositeData;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 18, "nme": "indexNames", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XArrayData.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XArrayData", "super": "sun/tools/jconsole/inspector/XOpenTypeViewer$XCompositeData", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;Ljavax/management/openmbean/ArrayType;Ljava/lang/Object;)V", "sig": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;Ljavax/management/openmbean/ArrayType<*>;Ljava/lang/Object;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;ILjavax/management/openmbean/OpenType;Ljava/lang/Object;)V", "sig": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;ILjavax/management/openmbean/OpenType<*>;Ljava/lang/Object;)V"}, {"nme": "viewed", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer;)V", "exs": ["java/lang/Exception"]}, {"nme": "getElementCount", "acc": 1, "dsc": "()I"}, {"nme": "getSelectedElementIndex", "acc": 1, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "incrElement", "acc": 1, "dsc": "()V"}, {"nme": "decrElement", "acc": 1, "dsc": "()V"}, {"nme": "canDecrement", "acc": 1, "dsc": "()Z"}, {"nme": "canIncrement", "acc": 1, "dsc": "()Z"}, {"nme": "loadArray", "acc": 2, "dsc": "()V"}, {"nme": "load", "acc": 2, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "dimension", "dsc": "I"}, {"acc": 2, "nme": "size", "dsc": "I"}, {"acc": 2, "nme": "elemType", "dsc": "Ljavax/management/openmbean/OpenType;", "sig": "Ljavax/management/openmbean/OpenType<*>;"}, {"acc": 2, "nme": "val", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "isCompositeType", "dsc": "Z"}, {"acc": 2, "nme": "isTabularType", "dsc": "Z"}, {"acc": 2, "nme": "currentIndex", "dsc": "I"}, {"acc": 2, "nme": "elements", "dsc": "[Ljavax/management/openmbean/CompositeData;"}, {"acc": 18, "nme": "arrayColumns", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "normalFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "boldFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}]}, "classes/sun/tools/jconsole/PlotterPanel$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/PlotterPanel$1", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/PlotterPanel;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/PlotterPanel;"}]}, "classes/sun/tools/jconsole/AboutDialog$HighlightPainter.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$HighlightPainter", "super": "javax/swing/text/DefaultHighlighter$DefaultHighlightPainter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "paintLayer", "acc": 1, "dsc": "(Ljava/awt/Graphics;IILjava/awt/Shape;Ljavax/swing/text/JTextComponent;Ljavax/swing/text/View;)Ljava/awt/Shape;"}], "flds": []}, "classes/sun/tools/jconsole/SheetDialog$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane;Lsun/tools/jconsole/SheetDialog$SheetOptionPane;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$safGlassPane", "dsc": "Lsun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane;"}, {"acc": 4112, "nme": "val$optionPane", "dsc": "Lsun/tools/jconsole/SheetDialog$SheetOptionPane;"}]}, "classes/sun/tools/jconsole/JConsole$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$2", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$tile", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/inspector/IconManager.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/IconManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getImage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljavax/swing/ImageIcon;"}, {"nme": "getSmallIcon", "acc": 10, "dsc": "(Ljavax/swing/ImageIcon;)Ljavax/swing/ImageIcon;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 9, "nme": "MBEAN", "dsc": "Ljavax/swing/Icon;"}, {"acc": 9, "nme": "MBEANSERVERDELEGATE", "dsc": "Ljavax/swing/Icon;"}, {"acc": 9, "nme": "DEFAULT_XOBJECT", "dsc": "Ljavax/swing/Icon;"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "isViewableValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "loadOpenType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/awt/Component;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "setOpenType", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "setupDisplay", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "prev", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "incr", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "decr", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "tabularPrev", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "tabularNext", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "compositeLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "tabularLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "container", "dsc": "Ljavax/swing/JScrollPane;"}, {"acc": 0, "nme": "current", "dsc": "Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;"}, {"acc": 0, "nme": "listener", "dsc": "Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeDataListener;"}, {"acc": 26, "nme": "compositeNavigationSingle", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "tabularNavigationSingle", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "editor", "dsc": "Ljavax/swing/table/TableCellEditor;"}]}, "classes/sun/tools/jconsole/ConnectDialog.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/ConnectDialog", "super": "sun/tools/jconsole/InternalDialog", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/JConsole;)V"}, {"nme": "revalidate", "acc": 1, "dsc": "()V"}, {"nme": "createActions", "acc": 2, "dsc": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "setConnectionParameters", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "itemStateChanged", "acc": 1, "dsc": "(Ljava/awt/event/ItemEvent;)V"}, {"nme": "updateButtonStates", "acc": 2, "dsc": "()V"}, {"nme": "insertUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "removeUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "changedUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "focusGained", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}, {"nme": "keyTyped", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}, {"nme": "setVisible", "acc": 1, "dsc": "(Z)V"}, {"nme": "keyPressed", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}, {"nme": "keyReleased", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}, {"nme": "valueChanged", "acc": 1, "dsc": "(Ljavax/swing/event/ListSelectionEvent;)V"}, {"nme": "refresh", "acc": 1, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "COL_NAME", "dsc": "I", "val": 0}, {"acc": 26, "nme": "COL_PID", "dsc": "I", "val": 1}, {"acc": 0, "nme": "jConsole", "dsc": "Lsun/tools/jconsole/JConsole;"}, {"acc": 0, "nme": "userNameTF", "dsc": "Ljavax/swing/JTextField;"}, {"acc": 0, "nme": "passwordTF", "dsc": "Ljavax/swing/JTextField;"}, {"acc": 0, "nme": "localRadioButton", "dsc": "Ljavax/swing/JRadio<PERSON>;"}, {"acc": 0, "nme": "remoteRadioButton", "dsc": "Ljavax/swing/JRadio<PERSON>;"}, {"acc": 0, "nme": "localMessageLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "remoteMessageLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "remoteTF", "dsc": "Ljavax/swing/JTextField;"}, {"acc": 0, "nme": "connectButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "cancelButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "radioButtonPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 2, "nme": "mastheadIcon", "dsc": "Ljavax/swing/Icon;"}, {"acc": 2, "nme": "hintTextColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "disabledTableCellColor", "dsc": "Ljava/awt/Color;"}, {"acc": 0, "nme": "vmTable", "dsc": "Ljavax/swing/JTable;"}, {"acc": 0, "nme": "vmModel", "dsc": "Lsun/tools/jconsole/ConnectDialog$ManagedVmTableModel;"}, {"acc": 0, "nme": "localTableScrollPane", "dsc": "Ljavax/swing/JScrollPane;"}, {"acc": 2, "nme": "connectAction", "dsc": "Ljavax/swing/Action;"}, {"acc": 2, "nme": "cancelAction", "dsc": "Ljavax/swing/Action;"}, {"acc": 10, "nme": "tmpLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}]}, "classes/sun/tools/jconsole/MBeansTab.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/MBeansTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "getDataViewer", "acc": 1, "dsc": "()Lsun/tools/jconsole/inspector/XDataViewer;"}, {"nme": "getTree", "acc": 1, "dsc": "()Lsun/tools/jconsole/inspector/XTree;"}, {"nme": "getSheet", "acc": 1, "dsc": "()Lsun/tools/jconsole/inspector/XSheet;"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "getUpdateInterval", "acc": 1, "dsc": "()I"}, {"nme": "buildMBeanServerView", "acc": 2, "dsc": "()V"}, {"nme": "getMBeanServerConnection", "acc": 1, "dsc": "()Ljavax/management/MBeanServerConnection;"}, {"nme": "getSnapshotMBeanServerConnection", "acc": 1, "dsc": "()Lsun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection;"}, {"nme": "update", "acc": 1, "dsc": "()V"}, {"nme": "setupTab", "acc": 2, "dsc": "()V"}, {"nme": "handleNotification", "acc": 1, "dsc": "(Ljavax/management/Notification;Ljava/lang/Object;)V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}, {"nme": "valueChanged", "acc": 1, "dsc": "(Ljavax/swing/event/TreeSelectionEvent;)V"}, {"nme": "treeWillExpand", "acc": 1, "dsc": "(Ljavax/swing/event/TreeExpansionEvent;)V", "exs": ["javax/swing/tree/ExpandVetoException"]}, {"nme": "treeWillCollapse", "acc": 1, "dsc": "(Ljavax/swing/event/TreeExpansionEvent;)V", "exs": ["javax/swing/tree/ExpandVetoException"]}], "flds": [{"acc": 2, "nme": "tree", "dsc": "Lsun/tools/jconsole/inspector/XTree;"}, {"acc": 2, "nme": "sheet", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}, {"acc": 2, "nme": "viewer", "dsc": "Lsun/tools/jconsole/inspector/XDataViewer;"}, {"acc": 2, "nme": "ml", "dsc": "Ljava/awt/event/MouseListener;"}]}, "classes/sun/tools/jconsole/Tab.class": {"ver": 65, "acc": 1057, "nme": "sun/tools/jconsole/Tab", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;Ljava/lang/String;)V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "update", "acc": 1, "dsc": "()V"}, {"nme": "dispose", "acc": 33, "dsc": "()V"}, {"nme": "getVMPanel", "acc": 4, "dsc": "()Lsun/tools/jconsole/VMPanel;"}, {"nme": "getOverviewPanels", "acc": 0, "dsc": "()[Lsun/tools/jconsole/OverviewPanel;"}, {"nme": "workerAdd", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}], "flds": [{"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "worker", "dsc": "Lsun/tools/jconsole/Worker;"}, {"acc": 4, "nme": "vmPanel", "dsc": "Lsun/tools/jconsole/VMPanel;"}, {"acc": 2, "nme": "prevSW", "dsc": "Ljavax/swing/SwingWorker;", "sig": "Ljavax/swing/SwingW<PERSON>ker<**>;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener;Ljavax/management/Notification;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$n", "dsc": "Ljavax/management/Notification;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener;"}]}, "classes/sun/tools/jconsole/BorderedComponent$LabeledBorder.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/BorderedComponent$LabeledBorder", "super": "javax/swing/border/TitledBorder", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/border/Border;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/border/Border;Ljavax/swing/JComponent;)V"}, {"nme": "paintBorder", "acc": 1, "dsc": "(<PERSON>ja<PERSON>/awt/Component;Ljava/awt/Graphics;IIII)V"}, {"nme": "getBorderInsets", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;<PERSON><PERSON><PERSON>/awt/Insets;)<PERSON><PERSON><PERSON>/awt/Insets;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljavax/swing/JComponent;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)V"}, {"nme": "getMinimumSize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)Ljava/awt/Dimension;"}, {"nme": "computeIntersection", "acc": 10, "dsc": "(Ljava/awt/Rectangle;IIII)Z"}], "flds": [{"acc": 4, "nme": "label", "dsc": "Ljavax/swing/JComponent;"}, {"acc": 2, "nme": "compLoc", "dsc": "Ljava/awt/Point;"}]}, "classes/sun/tools/jconsole/MBeansTab$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MBeansTab$3", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/VMPanel$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$4", "super": "java/util/TimerTask", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanInfo$MBeanInfoTableCellRenderer.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanInfo$MBeanInfoTableCellRenderer", "super": "javax/swing/table/DefaultTableCellRenderer", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getTableCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZZII)Ljava/awt/Component;"}], "flds": []}, "classes/sun/tools/jconsole/inspector/TableSorter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/TableSorter", "super": "javax/swing/table/DefaultTableModel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "newDataAvailable", "acc": 1, "dsc": "(Ljavax/swing/event/TableModelEvent;)V"}, {"nme": "addTableModelListener", "acc": 1, "dsc": "(Ljavax/swing/event/TableModelListener;)V"}, {"nme": "removeTableModelListener", "acc": 1, "dsc": "(Ljavax/swing/event/TableModelListener;)V"}, {"nme": "removeListeners", "acc": 2, "dsc": "()V"}, {"nme": "restoreListeners", "acc": 2, "dsc": "()V"}, {"nme": "compare", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "sort", "acc": 2, "dsc": "(IZ)V"}, {"nme": "compareS", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;Z)Z"}, {"nme": "compareG", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;Z)Z"}, {"nme": "quickSort", "acc": 2, "dsc": "(IIIZ)V"}, {"nme": "getRow", "acc": 2, "dsc": "(I)<PERSON><PERSON><PERSON>/util/Vector;", "sig": "(I)<PERSON><PERSON><PERSON>/util/Vector<*>;"}, {"nme": "setRow", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Vector;I)V", "sig": "(<PERSON><PERSON><PERSON>/util/Vector<*>;I)V"}, {"nme": "swap", "acc": 2, "dsc": "(III)V"}, {"nme": "sortByColumn", "acc": 1, "dsc": "(I)V"}, {"nme": "sortByColumn", "acc": 1, "dsc": "(IZ)V"}, {"nme": "getIndexOfRow", "acc": 1, "dsc": "(I)I"}, {"nme": "addMouseListenerToHeaderInTable", "acc": 1, "dsc": "(Ljavax/swing/JTable;)V"}, {"nme": "mouseClicked", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseEntered", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseExited", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "mouseReleased", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 2, "nme": "ascending", "dsc": "Z"}, {"acc": 2, "nme": "columnModel", "dsc": "Ljavax/swing/table/TableColumnModel;"}, {"acc": 2, "nme": "tableView", "dsc": "Ljavax/swing/JTable;"}, {"acc": 2, "nme": "evtListenerList", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Ljavax/swing/event/TableModelListener;>;"}, {"acc": 2, "nme": "sortColumn", "dsc": "I"}, {"acc": 2, "nme": "invertedIndex", "dsc": "[I"}]}, "classes/sun/tools/jconsole/inspector/XMBeanInfo$MBeanInfoTableCellEditor.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanInfo$MBeanInfoTableCellEditor", "super": "sun/tools/jconsole/inspector/Utils$ReadOnlyTableCellEditor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/JTextField;)V"}, {"nme": "getTableCellEditorComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZII)Ljava/awt/Component;"}], "flds": []}, "classes/sun/tools/jconsole/Resources.class": {"ver": 65, "acc": 49, "nme": "sun/tools/jconsole/Resources", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "format", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "getMnemonicInt", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "initializeMessages", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/String;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;)V"}, {"nme": "isWritableField", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Z"}, {"nme": "getMessage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/ResourceBundle;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "setFieldValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;Ljava/lang/String;)V"}, {"nme": "replaceWithPlatformLineFeed", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "removeMnemonicAmpersand", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "findMnemonicInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "lookupMnemonicInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "MNEMONIC_LOOKUP", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}]}, "classes/sun/tools/jconsole/inspector/XOperations.class": {"ver": 65, "acc": 1057, "nme": "sun/tools/jconsole/inspector/XOperations", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "removeOperations", "acc": 1, "dsc": "()V"}, {"nme": "loadOperations", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;)V"}, {"nme": "isCallable", "acc": 2, "dsc": "([Ljavax/management/MBeanParameterInfo;)Z"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "performInvokeRequest", "acc": 0, "dsc": "(Ljavax/swing/JButton;)V"}, {"nme": "addOperationsListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V"}, {"nme": "removeOperationsListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V"}, {"nme": "fireChangedNotification", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "updateOperations", "acc": 1028, "dsc": "([Ljavax/management/MBeanOperationInfo;)[Ljavax/management/MBeanOperationInfo;"}], "flds": [{"acc": 25, "nme": "OPERATION_INVOCATION_EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jam.xoperations.invoke.result"}, {"acc": 2, "nme": "notificationListenersList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljavax/management/NotificationListener;>;"}, {"acc": 2, "nme": "operationEntryTable", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Ljava/util/Hashtable<Ljavax/swing/JButton;Lsun/tools/jconsole/inspector/OperationEntry;>;"}, {"acc": 2, "nme": "mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 2, "nme": "mbeanInfo", "dsc": "Ljavax/management/MBeanInfo;"}, {"acc": 2, "nme": "mbeansTab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/SheetDialog.class": {"ver": 65, "acc": 49, "nme": "sun/tools/jconsole/SheetDialog", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "showOptionDialog", "acc": 8, "dsc": "(Lsun/tools/jconsole/VMPanel;Ljava/lang/Object;IILjavax/swing/Icon;[Ljava/lang/Object;Ljava/lang/Object;)Ljavax/swing/JOptionPane;"}, {"nme": "fixWrapping", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "iconR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 10, "nme": "textR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 10, "nme": "viewR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 10, "nme": "viewInsets", "dsc": "<PERSON><PERSON><PERSON>/awt/Insets;"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XCompositeData.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XCompositeData", "super": "sun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "viewed", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer;)V", "exs": ["java/lang/Exception"]}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatKey", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "load", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "loadCompositeData", "acc": 4, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}], "flds": [{"acc": 20, "nme": "columnNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "composite", "dsc": "Ljavax/management/openmbean/CompositeData;"}]}, "classes/sun/tools/jconsole/VMPanel$6.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/InternalDialog$MastheadIcon.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/InternalDialog$MastheadIcon", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/InternalDialog;Ljava/lang/String;)V"}, {"nme": "paintIcon", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;Ljava/awt/Graphics;II)V"}, {"nme": "getIconWidth", "acc": 1, "dsc": "()I"}, {"nme": "getIconHeight", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 2, "nme": "leftIcon", "dsc": "Ljavax/swing/ImageIcon;"}, {"acc": 2, "nme": "rightIcon", "dsc": "Ljavax/swing/ImageIcon;"}, {"acc": 2, "nme": "font", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "gap", "dsc": "I"}, {"acc": 2, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/InternalDialog;"}]}, "classes/sun/tools/jconsole/inspector/ThreadDialog.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/ThreadDialog", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "parentComponent", "dsc": "L<PERSON><PERSON>/awt/Component;"}, {"acc": 0, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "messageType", "dsc": "I"}]}, "classes/sun/tools/jconsole/inspector/XSheet$7.class": {"ver": 65, "acc": 4128, "nme": "sun/tools/jconsole/inspector/XSheet$7", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$sun$tools$jconsole$inspector$XNodeInfo$Type", "dsc": "[I"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$AttributesMouseListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$AttributesMouseListener", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/OutputViewer$PipeListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OutputViewer$PipeListener", "super": "java/lang/Thread", "mthds": [{"nme": "create", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/io/PrintStream;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 1, "nme": "ps", "dsc": "Ljava/io/PrintStream;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "inPipe", "dsc": "Ljava/io/PipedInputStream;"}, {"acc": 2, "nme": "br", "dsc": "<PERSON><PERSON><PERSON>/io/BufferedReader;"}]}, "classes/sun/tools/jconsole/ProxyClient$SnapshotInvocationHandler.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ProxyClient$SnapshotInvocationHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/management/MBeanServerConnection;)V"}, {"nme": "flush", "acc": 32, "dsc": "()V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "getAttribute", "acc": 2, "dsc": "(Ljavax/management/ObjectName;Ljava/lang/String;)Ljava/lang/Object;", "exs": ["javax/management/MBeanException", "javax/management/InstanceNotFoundException", "javax/management/AttributeNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 2, "dsc": "(Ljavax/management/ObjectName;[Ljava/lang/String;)Ljavax/management/AttributeList;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getCachedAttributes", "acc": 34, "dsc": "(Ljavax/management/ObjectName;Ljava/util/Set;)Lsun/tools/jconsole/ProxyClient$SnapshotInvocationHandler$NameValueMap;", "sig": "(Ljavax/management/ObjectName;Ljava/util/Set<Ljava/lang/String;>;)Lsun/tools/jconsole/ProxyClient$SnapshotInvocationHandler$NameValueMap;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "newMap", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "<K:Ljava/lang/Object;V:Ljava/lang/Object;>()Ljava/util/Map<TK;TV;>;"}], "flds": [{"acc": 18, "nme": "conn", "dsc": "Ljavax/management/MBeanServerConnection;"}, {"acc": 2, "nme": "cachedValues", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljavax/management/ObjectName;Lsun/tools/jconsole/ProxyClient$SnapshotInvocationHandler$NameValueMap;>;"}, {"acc": 2, "nme": "cachedNames", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljavax/management/ObjectName;Ljava/util/Set<Ljava/lang/String;>;>;"}]}, "classes/sun/tools/jconsole/AboutDialog$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$4", "super": "javax/swing/AbstractAction", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/AboutDialog;Ljava/lang/String;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/AboutDialog;"}]}, "classes/sun/tools/jconsole/InternalDialog$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/InternalDialog$1", "super": "javax/swing/AbstractAction", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/InternalDialog;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/InternalDialog;"}]}, "classes/sun/tools/jconsole/MaximizableInternalFrame.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/MaximizableInternalFrame", "super": "javax/swing/JInternalFrame", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;ZZZZ)V"}, {"nme": "init", "acc": 2, "dsc": "()V"}, {"nme": "updateFrame", "acc": 2, "dsc": "()V"}, {"nme": "updateUI", "acc": 1, "dsc": "()V"}, {"nme": "getMainFrame", "acc": 2, "dsc": "()Ljavax/swing/J<PERSON><PERSON>e;"}, {"nme": "getMainMenuBar", "acc": 2, "dsc": "()Ljavax/swing/JMenuBar;"}, {"nme": "setTitle", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "updateButtonStates", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "isXP", "dsc": "Z"}, {"acc": 2, "nme": "mainFrame", "dsc": "Ljavax/swing/J<PERSON>rame;"}, {"acc": 2, "nme": "mainMenuBar", "dsc": "Ljavax/swing/JMenuBar;"}, {"acc": 2, "nme": "mainTitle", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "titlePane", "dsc": "Ljavax/swing/JComponent;"}, {"acc": 2, "nme": "normalBorder", "dsc": "Ljavax/swing/border/Border;"}, {"acc": 2, "nme": "pcl", "dsc": "Ljava/beans/PropertyChangeListener;"}, {"acc": 10, "nme": "WP_MINBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "WP_RESTOREBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "WP_CLOSEBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "WP_MDIMINBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "WP_MDIRESTOREBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "WP_MDICLOSEBUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "setButtonIcons", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}, {"acc": 2, "nme": "enableActions", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "classes/sun/tools/jconsole/ThreadTab$ThreadOverviewPanel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$ThreadOverviewPanel", "super": "sun/tools/jconsole/OverviewPanel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "updateThreadsInfo", "acc": 2, "dsc": "(JJJJ)V"}], "flds": []}, "classes/sun/tools/jconsole/Formatter.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Formatter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "formatTime", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatNanoTime", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatClockTime", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatDate", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatDateTime", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDateTimeFormat", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/text/DateFormat;"}, {"nme": "toExcelTime", "acc": 8, "dsc": "(J)D"}, {"nme": "formatKByteStrings", "acc": 136, "dsc": "([J)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatKBytes", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatBytes", "acc": 8, "dsc": "(JZ)Ljava/lang/String;"}, {"nme": "formatBytes", "acc": 8, "dsc": "(JJ)Ljava/lang/String;"}, {"nme": "formatBytes", "acc": 8, "dsc": "(JJZ)Ljava/lang/String;"}, {"nme": "trimDouble", "acc": 10, "dsc": "(D)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatLong", "acc": 8, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "formatLongs", "acc": 136, "dsc": "([J)[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "justify", "acc": 8, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "justify", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "newRow", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "newRow", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "newRow", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;L<PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "SECOND", "dsc": "J", "val": 1000}, {"acc": 24, "nme": "MINUTE", "dsc": "J", "val": 60000}, {"acc": 24, "nme": "HOUR", "dsc": "J", "val": 3600000}, {"acc": 24, "nme": "DAY", "dsc": "J", "val": 86400000}, {"acc": 24, "nme": "cr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "timeDF", "dsc": "Ljava/text/DateFormat;"}, {"acc": 26, "nme": "timeWithSecondsDF", "dsc": "Ljava/text/DateFormat;"}, {"acc": 26, "nme": "dateDF", "dsc": "Ljava/text/DateFormat;"}, {"acc": 26, "nme": "decimalZero", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/JConsole$FixedJRootPane.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$FixedJRootPane", "super": "javax/swing/JRootPane", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "updateUI", "acc": 1, "dsc": "()V"}, {"nme": "revalidate", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XTable.class": {"ver": 65, "acc": 1057, "nme": "sun/tools/jconsole/inspector/XTable", "super": "javax/swing/JTable", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getDefaultColor", "acc": 0, "dsc": "()Ljava/awt/Color;"}, {"nme": "getEditableColor", "acc": 0, "dsc": "()Ljava/awt/Color;"}, {"nme": "sortRequested", "acc": 0, "dsc": "(I)V"}, {"nme": "getSelectedIndex", "acc": 1, "dsc": "()I"}, {"nme": "convertRowToIndex", "acc": 1, "dsc": "(I)I"}, {"nme": "emptyTable", "acc": 1, "dsc": "()V"}, {"nme": "isTableEditable", "acc": 1025, "dsc": "()Z"}, {"nme": "isColumnEditable", "acc": 1025, "dsc": "(I)Z"}, {"nme": "isReadable", "acc": 1025, "dsc": "(I)Z"}, {"nme": "isWritable", "acc": 1025, "dsc": "(I)Z"}, {"nme": "isCellError", "acc": 1025, "dsc": "(II)Z"}, {"nme": "isAttributeViewable", "acc": 1025, "dsc": "(II)Z"}, {"nme": "setTableValue", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "getValue", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getClassName", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValueName", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isReadWrite", "acc": 1, "dsc": "(I)Z"}, {"nme": "isCellEditable", "acc": 1, "dsc": "(II)Z"}, {"nme": "isCellDroppable", "acc": 1, "dsc": "(II)Z"}, {"nme": "getToolTip", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(II)Ljavax/swing/table/TableCellRenderer;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/swing/table/TableCellRenderer;II)Ljava/awt/Component;"}], "flds": [{"acc": 24, "nme": "NAME_COLUMN", "dsc": "I", "val": 0}, {"acc": 24, "nme": "VALUE_COLUMN", "dsc": "I", "val": 1}, {"acc": 2, "nme": "defaultColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "editableColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "errorColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "normalFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "boldFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}]}, "classes/sun/tools/jconsole/VMPanel$3$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$3$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel$3;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/VMPanel$3;"}]}, "classes/sun/tools/jconsole/VMPanel$9.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$9", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;I)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$n", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/MemoryTab$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$4", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MemoryTab;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 2, "nme": "used", "dsc": "[J"}, {"acc": 2, "nme": "committed", "dsc": "[J"}, {"acc": 2, "nme": "max", "dsc": "[J"}, {"acc": 2, "nme": "threshold", "dsc": "[J"}, {"acc": 2, "nme": "timeStamp", "dsc": "J"}, {"acc": 2, "nme": "detailsStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "initialRun", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MemoryTab;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCellRenderer.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCellRenderer", "super": "javax/swing/table/DefaultTableCellRenderer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "getTableCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZZII)Ljava/awt/Component;"}, {"nme": "getComponent", "acc": 1, "dsc": "()L<PERSON>va/awt/Component;"}], "flds": [{"acc": 0, "nme": "comp", "dsc": "L<PERSON><PERSON>/awt/Component;"}]}, "classes/sun/tools/jconsole/CreateMBeanDialog$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/CreateMBeanDialog$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/CreateMBeanDialog;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/CreateMBeanDialog;"}]}, "classes/sun/tools/jconsole/Messages.class": {"ver": 65, "acc": 49, "nme": "sun/tools/jconsole/Messages", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "BUNDLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.tools.jconsole.resources.messages"}, {"acc": 9, "nme": "ONE_DAY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ONE_HOUR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ONE_MIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ONE_MONTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ONE_YEAR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TWO_HOURS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREE_HOURS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREE_MONTHS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FIVE_MIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIX_HOURS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIX_MONTHS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SEVEN_DAYS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TEN_MIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TWELVE_HOURS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THIRTY_MIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "LESS_THAN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "A_LOT_LESS_THAN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "GREATER_THAN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ACTION_CAPITALIZED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ACTION_INFO_CAPITALIZED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ALL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ARCHITECTURE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ATTRIBUTE_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ATTRIBUTE_VALUES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ATTRIBUTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "BLANK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "BLOCKED_COUNT_WAITED_COUNT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "BOOT_CLASS_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "BORDERED_COMPONENT_MORE_OR_LESS_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CPU_USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CPU_USAGE_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CANCEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CASCADE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CHART_COLON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLASS_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLASS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLASS_TAB_INFO_LABEL_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLASS_TAB_LOADED_CLASSES_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLASSES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CLOSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "COLUMN_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "COLUMN_PID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "COMMITTED_MEMORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "COMMITTED_VIRTUAL_MEMORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "COMMITTED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_CONNECT_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_ACCESSIBLE_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_MASTHEAD_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_MASTHEAD_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_STATUS_BAR_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECT_DIALOG_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTED_PUNCTUATION_CLICK_TO_DISCONNECT_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_NAME__DISCONNECTED_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONSTRUCTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CURRENT_CLASSES_LOADED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CURRENT_HEAP_SIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CURRENT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CREATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DAEMON_THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DISCONNECTED_PUNCTUATION_CLICK_TO_CONNECT_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DOUBLE_CLICK_TO_EXPAND_FORWARD_SLASH_COLLAPSE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DOUBLE_CLICK_TO_VISUALIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DESCRIPTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DETAILS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DETECT_DEADLOCK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DETECT_DEADLOCK_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DIMENSION_IS_NOT_SUPPORTED_COLON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DISCARD_CHART", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DURATION_DAYS_HOURS_MINUTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DURATION_HOURS_MINUTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DURATION_MINUTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DURATION_SECONDS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "EMPTY_ARRAY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ERROR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ERROR_COLON_MBEANS_ALREADY_EXIST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ERROR_COLON_MBEANS_DO_NOT_EXIST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "EXIT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FAIL_TO_LOAD_PLUGIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_FILE_EXISTS_CANCEL_OPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_FILE_EXISTS_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_FILE_EXISTS_OK_OPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_FILE_EXISTS_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_SAVED_FILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_SAVE_FAILED_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FILE_CHOOSER_SAVE_FAILED_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FREE_PHYSICAL_MEMORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "FREE_SWAP_SPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "GARBAGE_COLLECTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "GC_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "GC_TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "GC_TIME_DETAILS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HEAP_MEMORY_USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HEAP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_ACCESSIBLE_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_JCONSOLE_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_JAVA_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_MASTHEAD_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_MASTHEAD_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_ABOUT_DIALOG_USER_GUIDE_LINK_URL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_MENU_ABOUT_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_MENU_USER_GUIDE_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HELP_MENU_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HOTSPOT_MBEANS_ELLIPSIS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "HOTSPOT_MBEANS_DIALOG_ACCESSIBLE_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "IMPACT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "INFO_CAPITALIZED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "INSECURE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "INVALID_PLUGIN_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "INVALID_URL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "IS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "JAVA_MONITORING___MANAGEMENT_CONSOLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "JCONSOLE_COLON_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "JCONSOLE_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "JCONSOLE_ACCESSIBLE_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "JIT_COMPILER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "LIBRARY_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "LIVE_THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "LOADED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "LOCAL_PROCESS_COLON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MASTHEAD_FONT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MANAGEMENT_NOT_ENABLED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MANAGEMENT_WILL_BE_ENABLED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEAN_ATTRIBUTE_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEAN_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEAN_NOTIFICATION_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEAN_OPERATION_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_CLEAR_NOTIFICATIONS_BUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_CLEAR_NOTIFICATIONS_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_COMPOSITE_NAVIGATION_MULTIPLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_COMPOSITE_NAVIGATION_SINGLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_REFRESH_ATTRIBUTES_BUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_REFRESH_ATTRIBUTES_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_SUBSCRIBE_NOTIFICATIONS_BUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_SUBSCRIBE_NOTIFICATIONS_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_TABULAR_NAVIGATION_MULTIPLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_TABULAR_NAVIGATION_SINGLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_UNSUBSCRIBE_NOTIFICATIONS_BUTTON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MBEANS_TAB_UNSUBSCRIBE_NOTIFICATIONS_BUTTON_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MANAGE_HOTSPOT_MBEANS_IN_COLON_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MAX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MAXIMUM_HEAP_SIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_POOL_LABEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_HEAP_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_INFO_LABEL_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_NON_HEAP_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_POOL_CHART_ABOVE_THRESHOLD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_POOL_CHART_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_POOL_CHART_BELOW_THRESHOLD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MEMORY_TAB_POOL_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "METHOD_SUCCESSFULLY_INVOKED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MINIMIZE_ALL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "MONITOR_LOCKED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NAME_STATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NAME_STATE_LOCK_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NAME_STATE_LOCK_NAME_LOCK_OWNER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NAME_AND_BUILD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NEW_CONNECTION_ELLIPSIS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NO_DEADLOCK_DETECTED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NON_HEAP_MEMORY_USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NON_HEAP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NOTIFICATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NOTIFICATION_BUFFER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NOTIFICATIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NOTIF_TYPES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NUMBER_OF_THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NUMBER_OF_LOADED_CLASSES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "NUMBER_OF_PROCESSORS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OBJECT_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OPERATING_SYSTEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OPERATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OPERATION_INVOCATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OPERATION_RETURN_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OPERATIONS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OVERVIEW", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "OVERVIEW_PANEL_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PARAMETER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PASSWORD_COLON_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PASSWORD_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PEAK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PERFORM_GC", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PERFORM_GC_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOTTER_ACCESSIBLE_NAME_KEY_AND_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOTTER_ACCESSIBLE_NAME_NO_DATA", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOTTER_SAVE_AS_MENU_ITEM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOTTER_TIME_RANGE_MENU", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLUGIN_EXCEPTION_DIALOG_BUTTON_EXIT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLUGIN_EXCEPTION_DIALOG_BUTTON_IGNORE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLUGIN_EXCEPTION_DIALOG_BUTTON_OK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLUGIN_EXCEPTION_DIALOG_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLUGIN_EXCEPTION_DIALOG_TITLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROBLEM_ADDING_LISTENER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROBLEM_DISPLAYING_MBEAN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROBLEM_INVOKING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROBLEM_REMOVING_LISTENER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROBLEM_SETTING_ATTRIBUTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PROCESS_CPU_TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "READABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "RECONNECT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "REMOTE_PROCESS_COLON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "REMOTE_PROCESS_TEXT_FIELD_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "RESTORE_ALL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "RETURN_TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SEQ_NUM", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIZE_BYTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIZE_GB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIZE_KB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SIZE_MB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "STACK_TRACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SUMMARY_TAB_HEADER_DATE_TIME_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SUMMARY_TAB_PENDING_FINALIZATION_LABEL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SUMMARY_TAB_PENDING_FINALIZATION_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SUMMARY_TAB_TAB_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "SUMMARY_TAB_VM_VERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREADS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREAD_TAB_INFO_LABEL_FORMAT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREAD_TAB_THREAD_INFO_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREAD_TAB_THREAD_PLOTTER_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THREAD_TAB_INITIAL_STACK_TRACE_MESSAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "THRESHOLD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TIME_RANGE_COLON", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TIME_STAMP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_LOADED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_CLASSES_LOADED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_CLASSES_UNLOADED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_COMPILE_TIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_PHYSICAL_MEMORY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_THREADS_STARTED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TOTAL_SWAP_SPACE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "TYPE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "UNAVAILABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "UNKNOWN_CAPITALIZED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "UNKNOWN_HOST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "UNREGISTER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "UPTIME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "USAGE_THRESHOLD", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "REMOTE_TF_USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "USED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "USERNAME_COLON_", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "USERNAME_ACCESSIBLE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "USER_DATA", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VIRTUAL_MACHINE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VM_ARGUMENTS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VMINTERNAL_FRAME_ACCESSIBLE_DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VENDOR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VERBOSE_OUTPUT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VERBOSE_OUTPUT_TOOLTIP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VIEW", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "WINDOW", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "WINDOWS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "WRITABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_FAILED1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_FAILED2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_FAILED_SSL1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_FAILED_SSL2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTION_LOST1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTING_TO1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "CONNECTING_TO2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DEADLOCK_TAB", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "DEADLOCK_TAB_N", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "EXPAND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "KBYTES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "PLOT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "VISUALIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 9, "nme": "ZZ_USAGE_TEXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/InternalDialog.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/InternalDialog", "super": "javax/swing/JInternalFrame", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/JConsole;Ljava/lang/String;Z)V"}, {"nme": "setLocationRelativeTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;)V"}], "flds": [{"acc": 4, "nme": "statusBar", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}]}, "classes/sun/tools/jconsole/MemoryPoolProxy.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/MemoryPoolProxy", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/ProxyClient;Ljavax/management/ObjectName;)V", "exs": ["java/io/IOException"]}, {"nme": "isCollectedMemoryPool", "acc": 1, "dsc": "()Z"}, {"nme": "getStat", "acc": 1, "dsc": "()Lsun/tools/jconsole/MemoryPoolStat;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "poolName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "client", "dsc": "Lsun/tools/jconsole/ProxyClient;"}, {"acc": 2, "nme": "pool", "dsc": "Ljava/lang/management/MemoryPoolMXBean;"}, {"acc": 2, "nme": "gcMBeans", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljavax/management/ObjectName;Ljava/lang/Long;>;"}, {"acc": 2, "nme": "lastGcInfo", "dsc": "Lcom/sun/management/GcInfo;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/tools/jconsole/MemoryTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$1", "super": "sun/tools/jconsole/Plotter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MemoryTab;Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MemoryTab;"}]}, "classes/sun/tools/jconsole/MaximizableInternalFrame$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MaximizableInternalFrame$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MaximizableInternalFrame;)V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MaximizableInternalFrame;"}]}, "classes/sun/tools/jconsole/ThreadTab$PromptingTextField.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$PromptingTextField", "super": "javax/swing/JTextField", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "revalidate", "acc": 1, "dsc": "()V"}, {"nme": "updateForeground", "acc": 2, "dsc": "()V"}, {"nme": "getText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "focusGained", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}], "flds": [{"acc": 2, "nme": "prompt", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "promptRemoved", "dsc": "Z"}, {"acc": 0, "nme": "fg", "dsc": "Ljava/awt/Color;"}]}, "classes/sun/tools/jconsole/ThreadTab$4$1$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$4$1$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$4$1;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$2", "dsc": "Lsun/tools/jconsole/ThreadTab$4$1;"}]}, "classes/sun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane$1", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane;)V"}], "flds": []}, "classes/sun/tools/jconsole/Plotter$2.class": {"ver": 65, "acc": 4128, "nme": "sun/tools/jconsole/Plotter$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$com$sun$tools$jconsole$JConsoleContext$ConnectionState", "dsc": "[I"}]}, "classes/sun/tools/jconsole/VMPanel$8.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$8", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;I)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/SummaryTab$CPUOverviewPanel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SummaryTab$CPUOverviewPanel", "super": "sun/tools/jconsole/OverviewPanel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "updateCPUInfo", "acc": 1, "dsc": "(Lsun/tools/jconsole/SummaryTab$Result;)V"}], "flds": [{"acc": 2, "nme": "prevUpTime", "dsc": "J"}, {"acc": 2, "nme": "prevProcessCpuTime", "dsc": "J"}]}, "classes/sun/tools/jconsole/MemoryTab$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$2", "super": "sun/tools/jconsole/Plotter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MemoryTab;Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MemoryTab;"}]}, "classes/sun/tools/jconsole/inspector/XTree$1.class": {"ver": 65, "acc": 4128, "nme": "sun/tools/jconsole/inspector/XTree$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$sun$tools$jconsole$inspector$XNodeInfo$Type", "dsc": "[I"}]}, "classes/sun/tools/jconsole/MemoryTab$PoolChart$AccessiblePoolChart.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/MemoryTab$PoolChart$AccessiblePoolChart", "super": "javax/swing/JPanel$AccessibleJPanel", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lsun/tools/jconsole/MemoryTab$PoolChart;)V"}, {"nme": "getAccessibleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/MemoryTab$PoolChart;"}]}, "classes/sun/tools/jconsole/inspector/XTree$Dn.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XTree$Dn", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/ObjectName;)V"}, {"nme": "getObjectName", "acc": 1, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "getDomain", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getKeyPropertyList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getToken", "acc": 1, "dsc": "(I)Lsun/tools/jconsole/inspector/XTree$Token;"}, {"nme": "getTokenCount", "acc": 1, "dsc": "()I"}, {"nme": "getHashDn", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getHash<PERSON>ey", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Token;)Ljava/lang/String;"}, {"nme": "computeHashDn", "acc": 2, "dsc": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XTree$Dn;)I"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 2, "nme": "mbean", "dsc": "Ljavax/management/ObjectName;"}, {"acc": 2, "nme": "domain", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "keyPropertyList", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "hashDn", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tokens", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/tools/jconsole/inspector/XTree$Token;>;"}]}, "classes/sun/tools/jconsole/inspector/XTextFieldEditor.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XTextFieldEditor", "super": "sun/tools/jconsole/inspector/XTextField", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "dropSuccess", "acc": 4, "dsc": "()V"}, {"nme": "addCellEditorListener", "acc": 1, "dsc": "(Ljavax/swing/event/CellEditorListener;)V"}, {"nme": "removeCellEditorListener", "acc": 1, "dsc": "(Ljavax/swing/event/CellEditorListener;)V"}, {"nme": "fireEditingStopped", "acc": 4, "dsc": "()V"}, {"nme": "fireEditingCanceled", "acc": 4, "dsc": "()V"}, {"nme": "cancelCellEditing", "acc": 1, "dsc": "()V"}, {"nme": "stopCellEditing", "acc": 1, "dsc": "()Z"}, {"nme": "isCellEditable", "acc": 1, "dsc": "(Ljava/util/EventObject;)Z"}, {"nme": "shouldSelectCell", "acc": 1, "dsc": "(Ljava/util/EventObject;)Z"}, {"nme": "getCellEditorValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getTableCellEditorComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZII)Ljava/awt/Component;"}], "flds": [{"acc": 4, "nme": "evtListenerList", "dsc": "Ljavax/swing/event/EventListenerList;"}, {"acc": 4, "nme": "changeEvent", "dsc": "Ljavax/swing/event/ChangeEvent;"}, {"acc": 2, "nme": "editorFocus<PERSON><PERSON><PERSON>", "dsc": "Ljava/awt/event/FocusListener;"}]}, "classes/sun/tools/jconsole/AboutDialog.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/AboutDialog", "super": "sun/tools/jconsole/InternalDialog", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/JConsole;)V"}, {"nme": "highlight", "acc": 1, "dsc": "()V"}, {"nme": "removeHighlights", "acc": 1, "dsc": "()V"}, {"nme": "showDialog", "acc": 1, "dsc": "()V"}, {"nme": "getAboutDialog", "acc": 10, "dsc": "(Lsun/tools/jconsole/JConsole;)Lsun/tools/jconsole/AboutDialog;"}, {"nme": "showAboutDialog", "acc": 8, "dsc": "(Lsun/tools/jconsole/JConsole;)V"}, {"nme": "browseUserGuide", "acc": 8, "dsc": "(Lsun/tools/jconsole/JConsole;)V"}, {"nme": "isBrowseSupported", "acc": 8, "dsc": "()Z"}, {"nme": "browse", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "createActions", "acc": 2, "dsc": "()V"}, {"nme": "getOnlineDocUrl", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "textColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "bgColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "borderColor", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "mastheadIcon", "dsc": "Ljavax/swing/Icon;"}, {"acc": 10, "nme": "aboutDialog", "dsc": "Lsun/tools/jconsole/AboutDialog;"}, {"acc": 2, "nme": "statusBar", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 2, "nme": "closeAction", "dsc": "Ljavax/swing/Action;"}, {"acc": 2, "nme": "helpLink", "dsc": "Ljavax/swing/JEditorPane;"}, {"acc": 18, "nme": "urlStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/Utilities.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/Utilities", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "updateTransparency", "acc": 9, "dsc": "(Ljavax/swing/JComponent;)V"}, {"nme": "setTabbedPaneTransparency", "acc": 10, "dsc": "(Ljavax/swing/JComponent;Z)V"}, {"nme": "setTransparency", "acc": 10, "dsc": "(Ljavax/swing/JComponent;Z)V"}, {"nme": "newTableScrollPane", "acc": 9, "dsc": "(Ljavax/swing/JComponent;)Ljavax/swing/JScrollPane;"}, {"nme": "setAccessibleName", "acc": 9, "dsc": "(Ljavax/accessibility/Accessible;Ljava/lang/String;)V"}, {"nme": "setAccessibleDescription", "acc": 9, "dsc": "(Ljavax/accessibility/Accessible;Ljava/lang/String;)V"}, {"nme": "ensureContrast", "acc": 9, "dsc": "(<PERSON>ja<PERSON>/awt/Color;Ljava/awt/Color;)Ljava/awt/Color;"}, {"nme": "getColorBrightness", "acc": 9, "dsc": "(<PERSON>java/awt/Color;)D"}, {"nme": "setColorBrightness", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/awt/Color;D)Ljava/awt/Color;"}], "flds": [{"acc": 26, "nme": "windowsLaF", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.java.swing.plaf.windows.WindowsLookAndFeel"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$NotifMouseListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$NotifMouseListener", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanNotifications;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanInfo.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XMBeanInfo", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "emptyInfoTable", "acc": 1, "dsc": "()V"}, {"nme": "emptyDescTable", "acc": 1, "dsc": "()V"}, {"nme": "addDescriptor", "acc": 2, "dsc": "(Ljavax/management/Descriptor;Ljava/lang/String;)V"}, {"nme": "addMBeanInfo", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;)V"}, {"nme": "addMBeanAttributeInfo", "acc": 1, "dsc": "(Ljavax/management/MBeanAttributeInfo;)V"}, {"nme": "addMBeanOperationInfo", "acc": 1, "dsc": "(Ljavax/management/MBeanOperationInfo;)V"}, {"nme": "addMBeanNotificationInfo", "acc": 1, "dsc": "(Ljavax/management/MBeanNotificationInfo;)V"}, {"nme": "addMBeanConstructorInfo", "acc": 2, "dsc": "(Ljavax/management/MBeanConstructorInfo;Ljava/lang/String;)V"}, {"nme": "addMBeanParameterInfo", "acc": 2, "dsc": "(Ljavax/management/MBeanParameterInfo;Ljava/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "light<PERSON>ellow", "dsc": "Ljava/awt/Color;"}, {"acc": 18, "nme": "NAME_COLUMN", "dsc": "I", "val": 0}, {"acc": 18, "nme": "VALUE_COLUMN", "dsc": "I", "val": 1}, {"acc": 18, "nme": "columnNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "infoTable", "dsc": "Ljavax/swing/JTable;"}, {"acc": 2, "nme": "descTable", "dsc": "Ljavax/swing/JTable;"}, {"acc": 2, "nme": "infoBorderPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 2, "nme": "descBorderPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 10, "nme": "renderer", "dsc": "Lsun/tools/jconsole/inspector/XMBeanInfo$MBeanInfoTableCellRenderer;"}, {"acc": 10, "nme": "editor", "dsc": "Ljavax/swing/table/TableCellEditor;"}]}, "classes/com/sun/tools/jconsole/JConsoleContext$ConnectionState.class": {"ver": 65, "acc": 16433, "nme": "com/sun/tools/jconsole/JConsoleContext$ConnectionState", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CONNECTED", "dsc": "Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"acc": 16409, "nme": "DISCONNECTED", "dsc": "Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"acc": 16409, "nme": "CONNECTING", "dsc": "Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}]}, "classes/sun/tools/jconsole/inspector/XTreeRenderer$1.class": {"ver": 65, "acc": 4128, "nme": "sun/tools/jconsole/inspector/XTreeRenderer$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$sun$tools$jconsole$inspector$XNodeInfo$Type", "dsc": "[I"}]}, "classes/sun/tools/jconsole/VMPanel$7.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$7", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;I)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/VMInternalFrame.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/VMInternalFrame", "super": "sun/tools/jconsole/MaximizableInternalFrame", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "getVMPanel", "acc": 1, "dsc": "()Lsun/tools/jconsole/VMPanel;"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}], "flds": [{"acc": 2, "nme": "vmPanel", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/JConsole$6.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(ZLjava/util/List;<PERSON><PERSON><PERSON>/util/List;ZLjava/util/List;<PERSON>java/util/List;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$hotspot", "dsc": "Z"}, {"acc": 4112, "nme": "val$hostNames", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 4112, "nme": "val$ports", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 4112, "nme": "val$noTile", "dsc": "Z"}, {"acc": 4112, "nme": "val$urls", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}, {"acc": 4112, "nme": "val$vmids", "dsc": "<PERSON><PERSON><PERSON>/util/List;"}]}, "classes/sun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$SlideAndFadeGlassPane", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "show", "acc": 1, "dsc": "(Lsun/tools/jconsole/SheetDialog$SheetOptionPane;)V"}, {"nme": "hide", "acc": 1, "dsc": "(Lsun/tools/jconsole/SheetDialog$SheetOptionPane;)V"}, {"nme": "doSlide", "acc": 2, "dsc": "()V"}, {"nme": "setGrayLevel", "acc": 1, "dsc": "(I)V"}, {"nme": "paint", "acc": 1, "dsc": "(Ljava/awt/Graphics;)V"}], "flds": [{"acc": 0, "nme": "optionPane", "dsc": "Lsun/tools/jconsole/SheetDialog$SheetOptionPane;"}, {"acc": 0, "nme": "fade", "dsc": "I"}, {"acc": 0, "nme": "slideIn", "dsc": "Z"}]}, "classes/sun/tools/jconsole/MBeansTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MBeansTab$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljavax/management/ObjectName;>;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/inspector/XArrayDataViewer.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XArrayDataViewer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isViewableValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "loadArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/awt/Component;"}, {"nme": "htmlize", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "classes/sun/tools/jconsole/ConnectDialog$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}]}, "classes/sun/tools/jconsole/Utilities$TableScrollPane.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Utilities$TableScrollPane", "super": "javax/swing/JScrollPane", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)V"}, {"nme": "paintBorder", "acc": 4, "dsc": "(Ljava/awt/Graphics;)V"}], "flds": []}, "classes/sun/tools/jconsole/ThreadTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab;Lsun/tools/jconsole/ProxyClient;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 2, "nme": "tlCount", "dsc": "I"}, {"acc": 2, "nme": "tpCount", "dsc": "I"}, {"acc": 2, "nme": "ttCount", "dsc": "J"}, {"acc": 2, "nme": "threads", "dsc": "[J"}, {"acc": 2, "nme": "timeStamp", "dsc": "J"}, {"acc": 4112, "nme": "val$proxyClient", "dsc": "Lsun/tools/jconsole/ProxyClient;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}]}, "classes/sun/tools/jconsole/inspector/Utils$ReadOnlyTableCellEditor.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/Utils$ReadOnlyTableCellEditor", "super": "javax/swing/DefaultCellEditor", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/JTextField;)V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications", "super": "javax/swing/JTable", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "cancelCellEditing", "acc": 1, "dsc": "()V"}, {"nme": "stopCellEditing", "acc": 1, "dsc": "()V"}, {"nme": "isCellEditable", "acc": 1, "dsc": "(II)Z"}, {"nme": "setValueAt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;II)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 33, "dsc": "(Ljavax/swing/table/TableCellRenderer;II)Ljava/awt/Component;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 33, "dsc": "(II)Ljavax/swing/table/TableCellRenderer;"}, {"nme": "getUserDataCell", "acc": 2, "dsc": "(II)Lsun/tools/jconsole/inspector/XMBeanNotifications$UserDataCell;"}, {"nme": "dispose", "acc": 32, "dsc": "()V"}, {"nme": "getReceivedNotifications", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;)J"}, {"nme": "clearCurrentNotifications", "acc": 33, "dsc": "()Z"}, {"nme": "unregisterListener", "acc": 33, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "registerListener", "acc": 33, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "handleNotification", "acc": 33, "dsc": "(Ljavax/management/Notification;Ljava/lang/Object;)V"}, {"nme": "disableNotifications", "acc": 33, "dsc": "()V"}, {"nme": "unregister", "acc": 34, "dsc": "(Ljavax/management/ObjectName;)Z"}, {"nme": "addNotificationsListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V"}, {"nme": "removeNotificationsListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V"}, {"nme": "fireNotificationReceived", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener;Lsun/tools/jconsole/inspector/XMBean;Ljavax/swing/tree/DefaultMutableTreeNode;[Ljava/lang/Object;J)V"}, {"nme": "updateModel", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<[Ljava/lang/Object;>;)V"}, {"nme": "isListenerRegistered", "acc": 33, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;)Z"}, {"nme": "loadNotifications", "acc": 33, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;)V"}, {"nme": "setColumnEditors", "acc": 2, "dsc": "()V"}, {"nme": "isTableEditable", "acc": 1, "dsc": "()Z"}, {"nme": "emptyTable", "acc": 33, "dsc": "()V"}, {"nme": "updateUserDataCell", "acc": 32, "dsc": "(II)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "columnNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljavax/management/ObjectName;Lsun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener;>;"}, {"acc": 66, "nme": "subscribed", "dsc": "Z"}, {"acc": 2, "nme": "currentListener", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener;"}, {"acc": 25, "nme": "NOTIFICATION_RECEIVED_EVENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jconsole.xnotification.received"}, {"acc": 2, "nme": "notificationListenersList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljavax/management/NotificationListener;>;"}, {"acc": 66, "nme": "enabled", "dsc": "Z"}, {"acc": 2, "nme": "normalFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "boldFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "rowMinHeight", "dsc": "I"}, {"acc": 2, "nme": "userDataEditor", "dsc": "Ljavax/swing/table/TableCellEditor;"}, {"acc": 2, "nme": "mouseListener", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications$NotifMouseListener;"}, {"acc": 2, "nme": "timeFormater", "dsc": "Ljava/text/SimpleDateFormat;"}, {"acc": 10, "nme": "editor", "dsc": "Ljavax/swing/table/TableCellEditor;"}]}, "classes/sun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection.class": {"ver": 65, "acc": 1537, "nme": "sun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection", "super": "java/lang/Object", "mthds": [{"nme": "flush", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$ZoomedCell.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$ZoomedCell", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "isInited", "acc": 0, "dsc": "()Z"}, {"nme": "getValue", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "setValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "init", "acc": 0, "dsc": "(Ljavax/swing/table/TableCellRenderer;Ljava/awt/Component;I)V"}, {"nme": "getType", "acc": 0, "dsc": "()I"}, {"nme": "reset", "acc": 0, "dsc": "()V"}, {"nme": "switchState", "acc": 0, "dsc": "()V"}, {"nme": "isMaximized", "acc": 0, "dsc": "()Z"}, {"nme": "minimize", "acc": 0, "dsc": "()V"}, {"nme": "maximize", "acc": 0, "dsc": "()V"}, {"nme": "getHeight", "acc": 0, "dsc": "()I"}, {"nme": "getMinHeight", "acc": 0, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Ljavax/swing/table/TableCellRenderer;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Ljavax/swing/table/TableCellRenderer;"}], "flds": [{"acc": 0, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/swing/table/TableCellRenderer;"}, {"acc": 0, "nme": "max<PERSON><PERSON><PERSON>", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes$MaximizedCellRenderer;"}, {"acc": 0, "nme": "minHeight", "dsc": "I"}, {"acc": 0, "nme": "minimized", "dsc": "Z"}, {"acc": 0, "nme": "init", "dsc": "Z"}, {"acc": 0, "nme": "type", "dsc": "I"}, {"acc": 0, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/tools/jconsole/PlotterPanel.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/PlotterPanel", "super": "sun/tools/jconsole/BorderedComponent", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Lsun/tools/jconsole/Plotter$Unit;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Lsun/tools/jconsole/Plotter;"}, {"nme": "set<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter;)V"}, {"nme": "init", "acc": 2, "dsc": "()V"}, {"nme": "getComponentPopupMenu", "acc": 1, "dsc": "()Ljavax/swing/JPopupMenu;"}, {"nme": "getAccessibleContext", "acc": 1, "dsc": "()Ljavax/accessibility/AccessibleContext;"}], "flds": [{"acc": 0, "nme": "plotter", "dsc": "Lsun/tools/jconsole/Plotter;"}]}, "classes/sun/tools/jconsole/HTMLPane.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/HTMLPane", "super": "javax/swing/JEditorPane", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setHasSelection", "acc": 33, "dsc": "(Z)V"}, {"nme": "getHasSelection", "acc": 33, "dsc": "()Z"}, {"nme": "setText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 2, "nme": "hasSelection", "dsc": "Z"}]}, "classes/sun/tools/jconsole/TimeComboBox.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/TimeComboBox", "super": "javax/swing/JComboBox", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "([Lsun/tools/jconsole/Plotter;)V"}, {"nme": "addPlotter", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter;)V"}, {"nme": "itemStateChanged", "acc": 1, "dsc": "(Ljava/awt/event/ItemEvent;)V"}, {"nme": "selectValue", "acc": 2, "dsc": "(I)V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}], "flds": [{"acc": 2, "nme": "plotters", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/Plotter;>;"}]}, "classes/sun/tools/jconsole/SheetDialog$SheetOptionPane.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$SheetOptionPane", "super": "javax/swing/JOptionPane", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;IILjavax/swing/Icon;[L<PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "setVisible", "acc": 1, "dsc": "(Z)V"}, {"nme": "paint", "acc": 1, "dsc": "(Ljava/awt/Graphics;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "comp", "dsc": "Ljava/awt/Composite;"}, {"acc": 10, "nme": "bgColor", "dsc": "Ljava/awt/Color;"}]}, "classes/sun/tools/jconsole/ConnectDialog$Padder.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$Padder", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/swing/JRadio<PERSON>on;)V"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}, {"nme": "getTextRectangle", "acc": 10, "dsc": "(<PERSON>java<PERSON>/swing/AbstractButton;)Ljava/awt/Rectangle;"}], "flds": [{"acc": 0, "nme": "radioButton", "dsc": "Ljavax/swing/JRadio<PERSON>;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener;Ljavax/management/Attribute;Ljava/lang/String;)V"}, {"nme": "doInBackground", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/lang/Exception"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$attribute", "dsc": "Ljavax/management/Attribute;"}, {"acc": 4112, "nme": "val$method", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes$AttributesListener;"}]}, "classes/sun/tools/jconsole/HTMLPane$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/HTMLPane$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/HTMLPane;)V"}, {"nme": "caretUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/CaretEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/HTMLPane;"}]}, "classes/sun/tools/jconsole/ThreadTab$4$1$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$4$1$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$4$1;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$2", "dsc": "Lsun/tools/jconsole/ThreadTab$4$1;"}]}, "classes/sun/tools/jconsole/VMPanel$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$2", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;Ljava/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/com/sun/tools/jconsole/JConsolePlugin.class": {"ver": 65, "acc": 1057, "nme": "com/sun/tools/jconsole/JConsolePlugin", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "setContext", "acc": 49, "dsc": "(Lcom/sun/tools/jconsole/JConsoleContext;)V"}, {"nme": "getContext", "acc": 17, "dsc": "()Lcom/sun/tools/jconsole/JConsoleContext;"}, {"nme": "getTabs", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljavax/swing/JPanel;>;"}, {"nme": "newSwingWorker", "acc": 1025, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "addContextPropertyChangeListener", "acc": 17, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}, {"nme": "removeContextPropertyChangeListener", "acc": 17, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}], "flds": [{"acc": 66, "nme": "context", "dsc": "Lcom/sun/tools/jconsole/JConsoleContext;"}, {"acc": 2, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/beans/PropertyChangeListener;>;"}]}, "classes/sun/tools/jconsole/PlotterPanel$AccessiblePlotterPanel.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/PlotterPanel$AccessiblePlotterPanel", "super": "javax/swing/JComponent$AccessibleJComponent", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lsun/tools/jconsole/PlotterPanel;)V"}, {"nme": "getAccessibleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/PlotterPanel;"}]}, "classes/sun/tools/jconsole/BorderedComponent.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/BorderedComponent", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "getImage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/awt/Image;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/String;Ljavax/swing/JComponent;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljavax/swing/JComponent;Z)V"}, {"nme": "setComponent", "acc": 1, "dsc": "(Ljavax/swing/JComponent;)V"}, {"nme": "setValueLabel", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "getMinimumSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}, {"nme": "doLayout", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "moreOrLessButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "valueLabelStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "label", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "comp", "dsc": "Ljavax/swing/JComponent;"}, {"acc": 0, "nme": "collapsed", "dsc": "Z"}, {"acc": 2, "nme": "collapseIcon", "dsc": "Ljavax/swing/Icon;"}, {"acc": 2, "nme": "expandIcon", "dsc": "Ljavax/swing/Icon;"}]}, "classes/sun/tools/jconsole/ClassTab$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ClassTab$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ClassTab;Z)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$b", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ClassTab;"}]}, "classes/sun/tools/jconsole/ThreadTab.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "valueChanged", "acc": 1, "dsc": "(Ljavax/swing/event/ListSelectionEvent;)V"}, {"nme": "doUpdate", "acc": 2, "dsc": "()V"}, {"nme": "detectDeadlock", "acc": 2, "dsc": "()V"}, {"nme": "getDeadlockedThreadIds", "acc": 1, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Long;", "exs": ["java/io/IOException"]}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "insertUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "removeUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "changedUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "getOverviewPanels", "acc": 0, "dsc": "()[Lsun/tools/jconsole/OverviewPanel;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "threadMeter", "dsc": "Lsun/tools/jconsole/PlotterPanel;"}, {"acc": 0, "nme": "timeComboBox", "dsc": "Lsun/tools/jconsole/TimeComboBox;"}, {"acc": 0, "nme": "threadListTabbedPane", "dsc": "Ljavax/swing/JTabbed<PERSON><PERSON>;"}, {"acc": 0, "nme": "listModel", "dsc": "Ljavax/swing/DefaultListModel;", "sig": "Ljavax/swing/DefaultListModel<Ljava/lang/Long;>;"}, {"acc": 0, "nme": "filterTF", "dsc": "Ljavax/swing/JTextField;"}, {"acc": 0, "nme": "messageLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}, {"acc": 0, "nme": "threadsSplitPane", "dsc": "Ljavax/swing/JSplitPane;"}, {"acc": 0, "nme": "name<PERSON>ache", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "<PERSON><PERSON><PERSON>/util/Hash<PERSON>ap<Ljava/lang/Long;Ljava/lang/String;>;"}, {"acc": 2, "nme": "overviewPanel", "dsc": "Lsun/tools/jconsole/ThreadTab$ThreadOverviewPanel;"}, {"acc": 2, "nme": "plotterListening", "dsc": "Z"}, {"acc": 26, "nme": "threadCountKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "threadCount"}, {"acc": 26, "nme": "peakKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "peak"}, {"acc": 26, "nme": "threadCountColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "peakColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "thinEmptyBorder", "dsc": "Ljavax/swing/border/Border;"}, {"acc": 2, "nme": "oldThreads", "dsc": "[J"}, {"acc": 0, "nme": "lastSelected", "dsc": "J"}]}, "classes/sun/tools/jconsole/inspector/Utils$EditFocusAdapter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/Utils$EditFocusAdapter", "super": "java/awt/event/FocusAdapter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/CellEditor;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}], "flds": [{"acc": 2, "nme": "editor", "dsc": "Ljavax/swing/CellEditor;"}]}, "classes/sun/tools/jconsole/inspector/XPlotter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XPlotter", "super": "sun/tools/jconsole/Plotter", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/swing/JTable;Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "addValues", "acc": 129, "dsc": "(J[J)V"}], "flds": [{"acc": 0, "nme": "table", "dsc": "Ljavax/swing/JTable;"}]}, "classes/sun/tools/jconsole/JConsole$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$1", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Ljava/lang/String;Lsun/tools/jconsole/LocalVirtualMachine;Z)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$lvm", "dsc": "Lsun/tools/jconsole/LocalVirtualMachine;"}, {"acc": 4112, "nme": "val$tile", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/inspector/XMBean.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XMBean", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/ObjectName;Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "getMBeanServerConnection", "acc": 0, "dsc": "()Ljavax/management/MBeanServerConnection;"}, {"nme": "getSnapshotMBeanServerConnection", "acc": 0, "dsc": "()Lsun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection;"}, {"nme": "isBroadcaster", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;[Ljava/lang/String;)<PERSON>ja<PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "setAttribute", "acc": 1, "dsc": "(Ljavax/management/Attribute;)V", "exs": ["javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/InvalidAttributeValueException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttribute", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "([Lja<PERSON>/lang/String;)Ljavax/management/AttributeList;", "exs": ["javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getAttributes", "acc": 1, "dsc": "([Ljavax/management/MBeanAttributeInfo;)Ljavax/management/AttributeList;", "exs": ["javax/management/AttributeNotFoundException", "javax/management/InstanceNotFoundException", "javax/management/MBeanException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "getObjectName", "acc": 1, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "getMBeanInfo", "acc": 1, "dsc": "()Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getIcon", "acc": 1, "dsc": "()Ljavax/swing/Icon;"}, {"nme": "setIcon", "acc": 1, "dsc": "(Ljavax/swing/Icon;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "mbeansTab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}, {"acc": 18, "nme": "objectName", "dsc": "Ljavax/management/ObjectName;"}, {"acc": 2, "nme": "icon", "dsc": "Ljavax/swing/Icon;"}, {"acc": 2, "nme": "text", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "broadcaster", "dsc": "<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"acc": 18, "nme": "broadcasterLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "mbeanInfo", "dsc": "Ljavax/management/MBeanInfo;"}, {"acc": 18, "nme": "mbeanInfoLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/tools/jconsole/MemoryTab.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab", "super": "sun/tools/jconsole/Tab", "mthds": [{"nme": "getTabName", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "createPlotters", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "itemStateChanged", "acc": 1, "dsc": "(Ljava/awt/event/ItemEvent;)V"}, {"nme": "gc", "acc": 1, "dsc": "()V"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "formatDetails", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "getOverviewPanels", "acc": 0, "dsc": "()[Lsun/tools/jconsole/OverviewPanel;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "plotterChoice", "dsc": "Ljavax/swing/JComboBox;", "sig": "Ljavax/swing/JComboBox<Lsun/tools/jconsole/Plotter;>;"}, {"acc": 0, "nme": "timeComboBox", "dsc": "Lsun/tools/jconsole/TimeComboBox;"}, {"acc": 0, "nme": "gcButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "plotter<PERSON><PERSON>l", "dsc": "Lsun/tools/jconsole/PlotterPanel;"}, {"acc": 0, "nme": "bottomPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 0, "nme": "details", "dsc": "Lsun/tools/jconsole/HTMLPane;"}, {"acc": 0, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Lsun/tools/jconsole/MemoryTab$PoolChart;"}, {"acc": 0, "nme": "plotterList", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/Plotter;>;"}, {"acc": 0, "nme": "heapPlotter", "dsc": "Lsun/tools/jconsole/Plotter;"}, {"acc": 0, "nme": "nonHeapPlotter", "dsc": "Lsun/tools/jconsole/Plotter;"}, {"acc": 2, "nme": "overviewPanel", "dsc": "Lsun/tools/jconsole/MemoryTab$MemoryOverviewPanel;"}, {"acc": 26, "nme": "usedKey", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "used"}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "committed"}, {"acc": 26, "nme": "max<PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "max"}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "threshold"}, {"acc": 26, "nme": "usedColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "committedColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "maxColor", "dsc": "Ljava/awt/Color;"}, {"acc": 26, "nme": "thresholdColor", "dsc": "Ljava/awt/Color;"}]}, "classes/sun/tools/jconsole/SheetDialog$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/SheetDialog$3", "super": "javax/swing/JLabel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}], "flds": [{"acc": 4112, "nme": "val$maxWidth", "dsc": "I"}]}, "classes/sun/tools/jconsole/MBeansTab$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MBeansTab$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/MBeansTab;Ljavax/management/Notification;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$notification", "dsc": "Ljavax/management/Notification;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/JConsole$WindowMenu.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$WindowMenu", "super": "javax/swing/JMenu", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Ljava/lang/String;)V"}, {"nme": "add", "acc": 2, "dsc": "(Lsun/tools/jconsole/VMInternalFrame;)V"}, {"nme": "remove", "acc": 2, "dsc": "(Lsun/tools/jconsole/VMInternalFrame;)V"}], "flds": [{"acc": 0, "nme": "windowMenuWindows", "dsc": "[Lsun/tools/jconsole/VMInternalFrame;"}, {"acc": 0, "nme": "separatorPosition", "dsc": "I"}, {"acc": 0, "nme": "viewR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 0, "nme": "textR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 0, "nme": "iconR", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCell.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCell", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isInited", "acc": 0, "dsc": "()Z"}, {"nme": "init", "acc": 0, "dsc": "(Ljavax/swing/table/TableCellRenderer;I)V"}, {"nme": "switchState", "acc": 0, "dsc": "()V"}, {"nme": "isMaximized", "acc": 0, "dsc": "()Z"}, {"nme": "minimize", "acc": 0, "dsc": "()V"}, {"nme": "maximize", "acc": 0, "dsc": "()V"}, {"nme": "getHeight", "acc": 0, "dsc": "()I"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Ljavax/swing/table/TableCellRenderer;"}], "flds": [{"acc": 0, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/swing/table/TableCellRenderer;"}, {"acc": 0, "nme": "max<PERSON><PERSON><PERSON>", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications$UserDataCellRenderer;"}, {"acc": 0, "nme": "minHeight", "dsc": "I"}, {"acc": 0, "nme": "minimized", "dsc": "Z"}, {"acc": 0, "nme": "init", "dsc": "Z"}, {"acc": 0, "nme": "userData", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/tools/jconsole/ExceptionSafePlugin.class": {"ver": 65, "acc": 48, "nme": "sun/tools/jconsole/ExceptionSafePlugin", "super": "com/sun/tools/jconsole/JConsolePlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lcom/sun/tools/jconsole/JConsolePlugin;)V"}, {"nme": "getTabs", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljavax/swing/JPanel;>;"}, {"nme": "newSwingWorker", "acc": 1, "dsc": "()Ljavax/swing/SwingWorker;", "sig": "()Ljavax/swing/SwingWorker<**>;"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "executeSwingWorker", "acc": 1, "dsc": "(Ljavax/swing/SwingWorker;)V", "sig": "(Ljavax/swing/SwingWorker<**>;)V"}, {"nme": "handleException", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "showExceptionDialog", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 10, "nme": "ignoreExceptions", "dsc": "Z"}, {"acc": 18, "nme": "plugin", "dsc": "Lcom/sun/tools/jconsole/JConsolePlugin;"}]}, "classes/sun/tools/jconsole/inspector/XSheet$5.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$5", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["javax/management/InstanceNotFoundException", "java/io/IOException"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/OverviewPanel.class": {"ver": 65, "acc": 1056, "nme": "sun/tools/jconsole/OverviewPanel", "super": "sun/tools/jconsole/PlotterPanel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;<PERSON>java/lang/String;Ljava/lang/String;Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "getInfoLabel", "acc": 1, "dsc": "()Ljavax/swing/<PERSON><PERSON><PERSON><PERSON>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PREFERRED_PLOTTER_SIZE", "dsc": "Ljava/awt/Dimension;"}, {"acc": 26, "nme": "MINIMUM_PLOTTER_SIZE", "dsc": "Ljava/awt/Dimension;"}, {"acc": 24, "nme": "VIEW_RANGE", "dsc": "I", "val": -1}, {"acc": 8, "nme": "PLOTTER_COLOR", "dsc": "Ljava/awt/Color;"}, {"acc": 2, "nme": "infoLabel", "dsc": "Ljavax/swing/J<PERSON><PERSON><PERSON>;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanOperations.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XMBeanOperations", "super": "sun/tools/jconsole/inspector/XOperations", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "updateOperations", "acc": 4, "dsc": "([Ljavax/management/MBeanOperationInfo;)[Ljavax/management/MBeanOperationInfo;"}], "flds": []}, "classes/sun/tools/jconsole/Plotter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/Plotter", "super": "javax/swing/JComponent", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter$Unit;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter$Unit;IZ)V"}, {"nme": "setUnit", "acc": 1, "dsc": "(Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "setDecimals", "acc": 1, "dsc": "(I)V"}, {"nme": "createSequence", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/awt/Color;Z)V"}, {"nme": "setUseDashedTransitions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "setIsPlotted", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "addValues", "acc": 161, "dsc": "(J[J)V"}, {"nme": "getSequence", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;)Lsun/tools/jconsole/Plotter$Sequence;"}, {"nme": "getViewRange", "acc": 1, "dsc": "()I"}, {"nme": "setViewRange", "acc": 1, "dsc": "(I)V"}, {"nme": "getComponentPopupMenu", "acc": 1, "dsc": "()Ljavax/swing/JPopupMenu;"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "saveAs", "acc": 2, "dsc": "()V"}, {"nme": "saveDataToFile", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)V"}, {"nme": "paintComponent", "acc": 1, "dsc": "(Ljava/awt/Graphics;)V"}, {"nme": "checkLeftMargin", "acc": 2, "dsc": "(I)Z"}, {"nme": "checkRightMargin", "acc": 2, "dsc": "(I)Z"}, {"nme": "getValueStringSlot", "acc": 2, "dsc": "([IIII)I"}, {"nme": "calculateTickInterval", "acc": 2, "dsc": "(IIJ)J"}, {"nme": "normalizeMin", "acc": 2, "dsc": "(J)J"}, {"nme": "normalizeMax", "acc": 2, "dsc": "(J)J"}, {"nme": "getFormattedValue", "acc": 2, "dsc": "(JZ)Ljava/lang/String;"}, {"nme": "getSizeString", "acc": 2, "dsc": "(JJ)Ljava/lang/String;"}, {"nme": "getDashedStroke", "acc": 42, "dsc": "()<PERSON><PERSON><PERSON>/awt/Stroke;"}, {"nme": "extendArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getValue", "acc": 0, "dsc": "()J"}, {"nme": "getLastTimeStamp", "acc": 0, "dsc": "()J"}, {"nme": "getLastValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)J"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}, {"nme": "getAccessibleContext", "acc": 1, "dsc": "()Ljavax/accessibility/AccessibleContext;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "rangeNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 24, "nme": "rangeValues", "dsc": "[I"}, {"acc": 24, "nme": "SECOND", "dsc": "J", "val": 1000}, {"acc": 24, "nme": "MINUTE", "dsc": "J", "val": 60000}, {"acc": 24, "nme": "HOUR", "dsc": "J", "val": 3600000}, {"acc": 24, "nme": "DAY", "dsc": "J", "val": 86400000}, {"acc": 24, "nme": "bgColor", "dsc": "Ljava/awt/Color;"}, {"acc": 24, "nme": "defaultColor", "dsc": "Ljava/awt/Color;"}, {"acc": 24, "nme": "ARRAY_SIZE_INCREMENT", "dsc": "I", "val": 4000}, {"acc": 10, "nme": "dashedStroke", "dsc": "<PERSON><PERSON><PERSON>/awt/Stroke;"}, {"acc": 2, "nme": "times", "dsc": "Lsun/tools/jconsole/Plotter$TimeStamps;"}, {"acc": 2, "nme": "seqs", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/Plotter$Sequence;>;"}, {"acc": 2, "nme": "popupMenu", "dsc": "Ljavax/swing/JPopupMenu;"}, {"acc": 2, "nme": "timeRangeMenu", "dsc": "Ljavax/swing/JMenu;"}, {"acc": 2, "nme": "menuRBs", "dsc": "[Ljavax/swing/JRadioButtonMenuItem;"}, {"acc": 2, "nme": "saveAsMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "saveFC", "dsc": "Ljavax/swing/J<PERSON><PERSON>hooser;"}, {"acc": 2, "nme": "viewRange", "dsc": "I"}, {"acc": 2, "nme": "unit", "dsc": "Lsun/tools/jconsole/Plotter$Unit;"}, {"acc": 2, "nme": "decimals", "dsc": "I"}, {"acc": 2, "nme": "decimalsMultiplier", "dsc": "D"}, {"acc": 2, "nme": "border", "dsc": "Ljavax/swing/border/Border;"}, {"acc": 2, "nme": "r", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 2, "nme": "smallFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 2, "nme": "bottom<PERSON>argin", "dsc": "I"}, {"acc": 2, "nme": "leftMargin", "dsc": "I"}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "I"}, {"acc": 18, "nme": "displayLegend", "dsc": "Z"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/tools/jconsole/OverviewTab$1$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OverviewTab$1$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/OverviewTab$1;Ljava/util/ArrayList;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$plotters", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;"}, {"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/OverviewTab$1;"}]}, "classes/sun/tools/jconsole/inspector/XTree$Token.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XTree$Token", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getTokenType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTokenValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "buildKeyValue", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "tokenType", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tokenValue", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/ConnectDialog$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$3", "super": "javax/swing/AbstractAction", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog;Ljava/lang/String;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}]}, "classes/sun/tools/jconsole/inspector/XSheet$6.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$6", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes", "super": "sun/tools/jconsole/inspector/XTable", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 33, "dsc": "(Ljavax/swing/table/TableCellRenderer;II)Ljava/awt/Component;"}, {"nme": "updateRowHeight", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 33, "dsc": "(II)Ljavax/swing/table/TableCellRenderer;"}, {"nme": "setColumnEditors", "acc": 2, "dsc": "()V"}, {"nme": "cancelCellEditing", "acc": 1, "dsc": "()V"}, {"nme": "stopCellEditing", "acc": 1, "dsc": "()V"}, {"nme": "editCellAt", "acc": 17, "dsc": "(IILjava/util/EventObject;)Z"}, {"nme": "isCellEditable", "acc": 1, "dsc": "(II)Z"}, {"nme": "setValueAt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;II)V"}, {"nme": "isTableEditable", "acc": 1, "dsc": "()Z"}, {"nme": "setTableValue", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "isColumnEditable", "acc": 1, "dsc": "(I)Z"}, {"nme": "getClassName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValueName", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getToolTip", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isWritable", "acc": 33, "dsc": "(I)Z"}, {"nme": "getRowCount", "acc": 33, "dsc": "()I"}, {"nme": "isReadable", "acc": 33, "dsc": "(I)Z"}, {"nme": "isCellError", "acc": 33, "dsc": "(II)Z"}, {"nme": "isAttributeViewable", "acc": 33, "dsc": "(II)Z"}, {"nme": "loadAttributes", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;)V"}, {"nme": "doLoadAttributes", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;)Ljava/lang/Runnable;", "exs": ["javax/management/JMException", "java/io/IOException"]}, {"nme": "collapse", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "updateZoomedCell", "acc": 0, "dsc": "(II)Lsun/tools/jconsole/inspector/XMBeanAttributes$ZoomedCell;"}, {"nme": "refreshAttributes", "acc": 1, "dsc": "()V"}, {"nme": "refreshAttributes", "acc": 2, "dsc": "(Z)V"}, {"nme": "columnMarginChanged", "acc": 1, "dsc": "(Ljavax/swing/event/ChangeEvent;)V"}, {"nme": "sortRequested", "acc": 0, "dsc": "(I)V"}, {"nme": "emptyTable", "acc": 33, "dsc": "()V"}, {"nme": "emptyTable", "acc": 2, "dsc": "(Ljavax/swing/table/DefaultTableModel;)V"}, {"nme": "isViewable", "acc": 2, "dsc": "(Ljavax/management/Attribute;)Z"}, {"nme": "removeAttributes", "acc": 32, "dsc": "()V"}, {"nme": "getZoomedCell", "acc": 2, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Ljava/lang/Object;)Lsun/tools/jconsole/inspector/XMBeanAttributes$ZoomedCell;"}, {"nme": "addTableData", "acc": 4, "dsc": "(Ljavax/swing/table/DefaultTableModel;Lsun/tools/jconsole/inspector/XMBean;[Ljavax/management/MBeanAttributeInfo;Ljava/util/HashMap;Ljava/util/HashMap;Ljava/util/HashMap;)V", "sig": "(Ljavax/swing/table/DefaultTableModel;Lsun/tools/jconsole/inspector/XMBean;[Ljavax/management/MBeanAttributeInfo;Ljava/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;Ljava/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;Ljava/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;)V"}, {"nme": "updateColumnWidth", "acc": 2, "dsc": "(II)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "LOGGER", "dsc": "Ljava/lang/System$Logger;"}, {"acc": 26, "nme": "columnNames", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 2, "nme": "mbeanInfo", "dsc": "Ljavax/management/MBeanInfo;"}, {"acc": 2, "nme": "attributesInfo", "dsc": "[Ljavax/management/MBeanAttributeInfo;"}, {"acc": 2, "nme": "attributes", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Lja<PERSON>/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "unavailableAttributes", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Lja<PERSON>/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "viewableAttributes", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Lja<PERSON>/util/HashMap<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 2, "nme": "viewersCache", "dsc": "L<PERSON>va/util/WeakHash<PERSON>ap;", "sig": "Ljava/util/WeakHashMap<Lsun/tools/jconsole/inspector/XMBean;Ljava/util/HashMap<Ljava/lang/String;Lsun/tools/jconsole/inspector/XMBeanAttributes$ZoomedCell;>;>;"}, {"acc": 18, "nme": "attributesListener", "dsc": "Ljavax/swing/event/TableModelListener;"}, {"acc": 2, "nme": "mbeansTab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}, {"acc": 2, "nme": "valueCellEditor", "dsc": "Ljavax/swing/table/TableCellEditor;"}, {"acc": 2, "nme": "rowMinHeight", "dsc": "I"}, {"acc": 2, "nme": "mouseListener", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes$AttributesMouseListener;"}, {"acc": 10, "nme": "editor", "dsc": "Ljavax/swing/table/TableCellEditor;"}]}, "classes/sun/tools/jconsole/LocalVirtualMachine.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/LocalVirtualMachine", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON>java/lang/String;Z<PERSON>java/lang/String;)V"}, {"nme": "getDisplayName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "vmid", "acc": 1, "dsc": "()I"}, {"nme": "isManageable", "acc": 1, "dsc": "()Z"}, {"nme": "isAttachable", "acc": 1, "dsc": "()Z"}, {"nme": "startManagementAgent", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "connectorAddress", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "displayName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAllVirtualMachines", "acc": 9, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Integer;Lsun/tools/jconsole/LocalVirtualMachine;>;"}, {"nme": "getMonitoredVMs", "acc": 10, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Integer;Lsun/tools/jconsole/LocalVirtualMachine;>;)V"}, {"nme": "getAttachableVMs", "acc": 10, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/Integer;Lsun/tools/jconsole/LocalVirtualMachine;>;)V"}, {"nme": "getLocalVirtualMachine", "acc": 9, "dsc": "(I)Lsun/tools/jconsole/LocalVirtualMachine;"}, {"nme": "loadManagementAgent", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "address", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "commandLine", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "displayName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "vmid", "dsc": "I"}, {"acc": 2, "nme": "isAttachSupported", "dsc": "Z"}, {"acc": 26, "nme": "LOCAL_CONNECTOR_ADDRESS_PROP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.localConnectorAddress"}]}, "classes/sun/tools/jconsole/OutputViewer.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/OutputViewer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "init", "acc": 9, "dsc": "()V"}, {"nme": "append", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "appendln", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "frame", "dsc": "Ljavax/swing/J<PERSON>rame;"}, {"acc": 10, "nme": "ta", "dsc": "Ljavax/swing/JTextArea;"}]}, "classes/sun/tools/jconsole/inspector/XDataViewer.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XDataViewer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "registerForMouseEvent", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;Ljava/awt/event/MouseListener;)V"}, {"nme": "dispose", "acc": 9, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "isViewableValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getViewerType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "getActionLabel", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "createOperationViewer", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/Object;Lsun/tools/jconsole/inspector/XMBean;)Ljava/awt/Component;"}, {"nme": "createNotificationViewer", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/awt/Component;"}, {"nme": "createAttributeViewer", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/Object;Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Ljavax/swing/JTable;)Ljava/awt/Component;"}], "flds": [{"acc": 25, "nme": "OPEN", "dsc": "I", "val": 1}, {"acc": 25, "nme": "ARRAY", "dsc": "I", "val": 2}, {"acc": 25, "nme": "NUMERIC", "dsc": "I", "val": 3}, {"acc": 25, "nme": "NOT_SUPPORTED", "dsc": "I", "val": 4}, {"acc": 2, "nme": "tab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/VMPanel$TabInfo.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$TabInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Z)V", "sig": "(Ljava/lang/Class<+Lsun/tools/jconsole/Tab;>;Ljava/lang/String;Z)V"}], "flds": [{"acc": 0, "nme": "tabClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Lsun/tools/jconsole/Tab;>;"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "tabVisible", "dsc": "Z"}]}, "classes/sun/tools/jconsole/inspector/XSheet$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$4", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/ClassTab$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ClassTab$2", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ClassTab;Lsun/tools/jconsole/ProxyClient;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "formatDetails", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 2, "nme": "clCount", "dsc": "J"}, {"acc": 2, "nme": "cuCount", "dsc": "J"}, {"acc": 2, "nme": "ctCount", "dsc": "J"}, {"acc": 2, "nme": "isVerbose", "dsc": "Z"}, {"acc": 2, "nme": "detailsStr", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "timeStamp", "dsc": "J"}, {"acc": 4112, "nme": "val$proxyClient", "dsc": "Lsun/tools/jconsole/ProxyClient;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ClassTab;"}]}, "classes/sun/tools/jconsole/OverviewTab$AutoGridLayout.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OverviewTab$AutoGridLayout", "super": "java/awt/GridLayout", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/OverviewTab;II)V"}, {"nme": "preferredLayoutSize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)<PERSON><PERSON><PERSON>/awt/Dimension;"}, {"nme": "minimumLayoutSize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)<PERSON><PERSON><PERSON>/awt/Dimension;"}, {"nme": "updateColumns", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/awt/Container;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/OverviewTab;"}]}, "classes/sun/tools/jconsole/CreateMBeanDialog$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/CreateMBeanDialog$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/CreateMBeanDialog;)V"}, {"nme": "compare", "acc": 1, "dsc": "(Lsun/tools/jconsole/ProxyClient;Lsun/tools/jconsole/ProxyClient;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "classes/sun/tools/jconsole/VMPanel.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/VMPanel", "super": "javax/swing/JTabbedPane", "mthds": [{"nme": "getTabInfos", "acc": 9, "dsc": "()[Lsun/tools/jconsole/VMPanel$TabInfo;"}, {"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ProxyClient;I)V"}, {"nme": "setUI", "acc": 1, "dsc": "(Ljavax/swing/plaf/TabbedPaneUI;)V"}, {"nme": "paintComponent", "acc": 4, "dsc": "(Ljava/awt/Graphics;)V"}, {"nme": "getToolTipText", "acc": 1, "dsc": "(Lja<PERSON>/awt/event/MouseEvent;)Ljava/lang/String;"}, {"nme": "addTab", "acc": 34, "dsc": "(Lsun/tools/jconsole/VMPanel$TabInfo;)V"}, {"nme": "insertTab", "acc": 34, "dsc": "(Lsun/tools/jconsole/VMPanel$TabInfo;I)V"}, {"nme": "removeTabAt", "acc": 33, "dsc": "(I)V"}, {"nme": "instantiate", "acc": 2, "dsc": "(Lsun/tools/jconsole/VMPanel$TabInfo;)Lsun/tools/jconsole/Tab;"}, {"nme": "isConnected", "acc": 0, "dsc": "()Z"}, {"nme": "getUpdateInterval", "acc": 1, "dsc": "()I"}, {"nme": "getProxyClient", "acc": 0, "dsc": "(Z)Lsun/tools/jconsole/ProxyClient;"}, {"nme": "getProxyClient", "acc": 1, "dsc": "()Lsun/tools/jconsole/ProxyClient;"}, {"nme": "cleanUp", "acc": 1, "dsc": "()V"}, {"nme": "connect", "acc": 1, "dsc": "()V"}, {"nme": "disconnect", "acc": 1, "dsc": "()V"}, {"nme": "propertyChange", "acc": 1, "dsc": "(Ljava/beans/PropertyChangeEvent;)V"}, {"nme": "onConnecting", "acc": 2, "dsc": "()V"}, {"nme": "closeOptionPane", "acc": 2, "dsc": "()V"}, {"nme": "updateFrameTitle", "acc": 0, "dsc": "()V"}, {"nme": "getFrame", "acc": 2, "dsc": "()Lsun/tools/jconsole/VMInternalFrame;"}, {"nme": "getTabs", "acc": 32, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lsun/tools/jconsole/Tab;>;"}, {"nme": "startUpdateTimer", "acc": 2, "dsc": "()V"}, {"nme": "vmPanelDied", "acc": 2, "dsc": "()V"}, {"nme": "update", "acc": 2, "dsc": "()V"}, {"nme": "getHostName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "getUserName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPassword", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getConnectionName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDisplayName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "createPluginTabs", "acc": 2, "dsc": "()V"}, {"nme": "fireConnectedChange", "acc": 2, "dsc": "(Z)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "proxyClient", "dsc": "Lsun/tools/jconsole/ProxyClient;"}, {"acc": 2, "nme": "timer", "dsc": "<PERSON><PERSON><PERSON>/util/Timer;"}, {"acc": 2, "nme": "updateInterval", "dsc": "I"}, {"acc": 2, "nme": "hostName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 2, "nme": "userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "vmIF", "dsc": "Lsun/tools/jconsole/VMInternalFrame;"}, {"acc": 10, "nme": "tabInfos", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/VMPanel$TabInfo;>;"}, {"acc": 2, "nme": "wasConnected", "dsc": "Z"}, {"acc": 2, "nme": "userDisconnected", "dsc": "Z"}, {"acc": 2, "nme": "shouldUseSSL", "dsc": "Z"}, {"acc": 2, "nme": "everConnected", "dsc": "Z"}, {"acc": 2, "nme": "initialUpdate", "dsc": "Z"}, {"acc": 2, "nme": "plugins", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/tools/jconsole/ExceptionSafePlugin;Ljavax/swing/SwingWorker<**>;>;"}, {"acc": 2, "nme": "pluginTabsAdded", "dsc": "Z"}, {"acc": 2, "nme": "optionPane", "dsc": "Ljavax/swing/JOptionPane;"}, {"acc": 2, "nme": "progressBar", "dsc": "Ljavax/swing/JProgressBar;"}, {"acc": 2, "nme": "time0", "dsc": "J"}, {"acc": 10, "nme": "connectedIcon16", "dsc": "Ljavax/swing/Icon;"}, {"acc": 10, "nme": "connectedIcon24", "dsc": "Ljavax/swing/Icon;"}, {"acc": 10, "nme": "disconnectedIcon16", "dsc": "Ljavax/swing/Icon;"}, {"acc": 10, "nme": "disconnectedIcon24", "dsc": "Ljavax/swing/Icon;"}, {"acc": 2, "nme": "connectedIconBounds", "dsc": "<PERSON><PERSON><PERSON>/awt/Rectangle;"}, {"acc": 2, "nme": "lockObject", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;)V"}, {"nme": "doInBackground", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Runnable;", "exs": ["java/lang/Exception"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 4112, "nme": "val$mbeanInfo", "dsc": "Ljavax/management/MBeanInfo;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/inspector/XNodeInfo$Type.class": {"ver": 65, "acc": 16433, "nme": "sun/tools/jconsole/inspector/XNodeInfo$Type", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "MBEAN", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "NONMBEAN", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "ATTRIBUTES", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "OPERATIONS", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "NOTIFICATIONS", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "ATTRIBUTE", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "OPERATION", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 16409, "nme": "NOTIFICATION", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}]}, "classes/sun/tools/jconsole/JConsole$4$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$4$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole$4;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/JConsole$4;"}]}, "classes/sun/tools/jconsole/AboutDialog$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/AboutDialog$1", "super": "java/awt/event/KeyAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/AboutDialog;)V"}, {"nme": "keyPressed", "acc": 1, "dsc": "(Ljava/awt/event/KeyEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/AboutDialog;"}]}, "classes/sun/tools/jconsole/ProxyClient$Snapshot.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/ProxyClient$Snapshot", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "newSnapshot", "acc": 9, "dsc": "(Ljavax/management/MBeanServerConnection;)Lsun/tools/jconsole/ProxyClient$SnapshotMBeanServerConnection;"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XNodeInfo.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XNodeInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XNodeInfo$Type;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"nme": "getData", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getToolTipText", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "type", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo$Type;"}, {"acc": 2, "nme": "data", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 2, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "tooltip", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/tools/jconsole/ConnectDialog$LocalTabJTable$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ConnectDialog$LocalTabJTable$2", "super": "javax/swing/table/DefaultTableCellRenderer", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ConnectDialog$LocalTabJTable;)V"}, {"nme": "getTableCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZZII)Ljava/awt/Component;"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ConnectDialog$LocalTabJTable;"}]}, "classes/sun/tools/jconsole/Plotter$AccessiblePlotter.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/Plotter$AccessiblePlotter", "super": "javax/swing/JComponent$AccessibleJComponent", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lsun/tools/jconsole/Plotter;)V"}, {"nme": "getAccessibleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAccessibleRole", "acc": 1, "dsc": "()Ljavax/accessibility/AccessibleRole;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -3847205410473510922}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/Plotter;"}]}, "classes/sun/tools/jconsole/VMPanel$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$3", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;Ljava/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/JConsole.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/JConsole", "super": "javax/swing/JFrame", "mthds": [{"nme": "updateLafValues", "acc": 8, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "getDesktopPane", "acc": 1, "dsc": "()Ljavax/swing/JDesktopPane;"}, {"nme": "getInternalFrames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lsun/tools/jconsole/VMInternalFrame;>;"}, {"nme": "createMDI", "acc": 2, "dsc": "()V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "tileWindows", "acc": 1, "dsc": "()V"}, {"nme": "cascadeWindows", "acc": 1, "dsc": "()V"}, {"nme": "addHost", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}, {"nme": "addVmid", "acc": 0, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;)V"}, {"nme": "addVmid", "acc": 0, "dsc": "(Lsun/tools/jconsole/LocalVirtualMachine;Z)V"}, {"nme": "addUrl", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "addHost", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Z)V"}, {"nme": "addProxyClient", "acc": 0, "dsc": "(Lsun/tools/jconsole/ProxyClient;Z)V"}, {"nme": "failed", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addFrame", "acc": 2, "dsc": "(Lsun/tools/jconsole/VMPanel;)Lsun/tools/jconsole/VMInternalFrame;"}, {"nme": "showConnectDialog", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "showCreateMBeanDialog", "acc": 2, "dsc": "()V"}, {"nme": "removeVMInternalFrame", "acc": 2, "dsc": "(Lsun/tools/jconsole/VMInternalFrame;)V"}, {"nme": "isProxyClientUsed", "acc": 2, "dsc": "(Lsun/tools/jconsole/ProxyClient;)Z"}, {"nme": "isValidRemoteString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "errorMessage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "internalFrameClosing", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameOpened", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameClosed", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameIconified", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameDeiconified", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameActivated", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "internalFrameDeactivated", "acc": 1, "dsc": "(Ljavax/swing/event/InternalFrameEvent;)V"}, {"nme": "usage", "acc": 10, "dsc": "()V"}, {"nme": "mainInit", "acc": 10, "dsc": "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lsun/tools/jconsole/ProxyClient;ZZ)V", "sig": "(Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/String;>;Ljava/util/List<Ljava/lang/Integer;>;Ljava/util/List<Lsun/tools/jconsole/LocalVirtualMachine;>;Lsun/tools/jconsole/ProxyClient;ZZ)V"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isDebug", "acc": 9, "dsc": "()Z"}, {"nme": "dbgStackTrace", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "isLocalAttachAvailable", "acc": 9, "dsc": "()Z"}, {"nme": "getPlugins", "acc": 40, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/sun/tools/jconsole/JConsolePlugin;>;"}, {"nme": "initPluginService", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initEmptyPlugin", "acc": 10, "dsc": "()V"}, {"nme": "pathToURLs", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;)[Ljava/net/URL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "fileToURL", "acc": 10, "dsc": "(Ljava/io/File;)Ljava/net/URL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 8, "nme": "IS_GTK", "dsc": "Z"}, {"acc": 8, "nme": "IS_WIN", "dsc": "Z"}, {"acc": 26, "nme": "title", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 25, "nme": "ROOT_URL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "service:jmx:"}, {"acc": 10, "nme": "updateInterval", "dsc": "I"}, {"acc": 10, "nme": "pluginPath", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "menuBar", "dsc": "Ljavax/swing/JMenuBar;"}, {"acc": 2, "nme": "hotspotMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "connectMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "exitMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "windowMenu", "dsc": "Lsun/tools/jconsole/JConsole$WindowMenu;"}, {"acc": 2, "nme": "tileMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "cascadeMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "minimizeAllMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "restoreAllMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "userGuideMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "aboutMI", "dsc": "Ljavax/swing/JMenuItem;"}, {"acc": 2, "nme": "connectButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "desktop", "dsc": "Ljavax/swing/JDesktopPane;"}, {"acc": 2, "nme": "connectDialog", "dsc": "Lsun/tools/jconsole/ConnectDialog;"}, {"acc": 2, "nme": "createDialog", "dsc": "Lsun/tools/jconsole/CreateMBeanDialog;"}, {"acc": 2, "nme": "windows", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Lsun/tools/jconsole/VMInternalFrame;>;"}, {"acc": 2, "nme": "frameLoc", "dsc": "I"}, {"acc": 8, "nme": "debug", "dsc": "Z"}, {"acc": 10, "nme": "pluginService", "dsc": "<PERSON><PERSON><PERSON>/util/ServiceLoader;", "sig": "Ljava/util/ServiceLoader<Lcom/sun/tools/jconsole/JConsolePlugin;>;"}]}, "classes/sun/tools/jconsole/ThreadTab$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$XMBeanNotificationsListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanNotifications;Lsun/tools/jconsole/inspector/XMBeanNotifications;Lsun/tools/jconsole/inspector/XMBean;Ljavax/swing/tree/DefaultMutableTreeNode;[Ljava/lang/String;)V"}, {"nme": "getData", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<[Ljava/lang/Object;>;"}, {"nme": "clear", "acc": 33, "dsc": "()V"}, {"nme": "isRegistered", "acc": 33, "dsc": "()Z"}, {"nme": "unregister", "acc": 33, "dsc": "()V"}, {"nme": "getReceivedNotifications", "acc": 33, "dsc": "()J"}, {"nme": "register", "acc": 33, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "handleNotification", "acc": 33, "dsc": "(Ljavax/management/Notification;Ljava/lang/Object;)V"}], "flds": [{"acc": 2, "nme": "xmbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 2, "nme": "node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 66, "nme": "received", "dsc": "J"}, {"acc": 2, "nme": "notifications", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications;"}, {"acc": 66, "nme": "unregistered", "dsc": "Z"}, {"acc": 2, "nme": "data", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Lja<PERSON>/util/ArrayList<[Ljava/lang/Object;>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications;"}]}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeDataListener.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeDataListener", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer;)V"}, {"nme": "mousePressed", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}, {"nme": "getSelectedViewedOpenType", "acc": 2, "dsc": "()Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XOpenTypeViewer;"}]}, "classes/sun/tools/jconsole/MaximizableInternalFrame$MDIButtonIcon.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MaximizableInternalFrame$MDIButtonIcon", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/swing/Icon;)V"}, {"nme": "paintIcon", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/awt/Component;Ljava/awt/Graphics;II)V"}, {"nme": "getIconWidth", "acc": 1, "dsc": "()I"}, {"nme": "getIconHeight", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 0, "nme": "windowsIcon", "dsc": "Ljavax/swing/Icon;"}, {"acc": 0, "nme": "part", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Field;"}]}, "classes/sun/tools/jconsole/MemoryTab$MemoryOverviewPanel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/MemoryTab$MemoryOverviewPanel", "super": "sun/tools/jconsole/OverviewPanel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "updateMemoryInfo", "acc": 2, "dsc": "(JJJ)V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XViewedTabularData.class": {"ver": 65, "acc": 1536, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XViewedTabularData", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/sun/tools/jconsole/inspector/XSheet$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/JConsole$4.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Lsun/tools/jconsole/ProxyClient;Z)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$proxyClient", "dsc": "Lsun/tools/jconsole/ProxyClient;"}, {"acc": 4112, "nme": "val$tile", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/ThreadTab$4$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$4$1", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab$4;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/tools/jconsole/ThreadTab$4;"}]}, "classes/sun/tools/jconsole/inspector/XTextFieldEditor$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XTextFieldEditor$1", "super": "java/awt/event/FocusAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XTextFieldEditor;)V"}, {"nme": "focusLost", "acc": 1, "dsc": "(Ljava/awt/event/FocusEvent;)V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/Utils.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/Utils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "isUniformCollection", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/util/Collection<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "canBeRenderedAsArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isSupportedArray", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getArrayClassName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getReadableClassName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isEditableType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getDefaultValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "newStringConstructor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "createNumberFromStringValue", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Number;", "exs": ["java/lang/NumberFormatException"]}, {"nme": "createObjectFromString", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getParameters", "acc": 9, "dsc": "([Lsun/tools/jconsole/inspector/XTextField;[Ljava/lang/String;)[Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "getActualException", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)<PERSON><PERSON><PERSON>/lang/Throwable;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "tableNavigationKeys", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "<PERSON>ja<PERSON>/util/Set<Ljava/lang/Integer;>;"}, {"acc": 26, "nme": "primitiveWrappers", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "primitives", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "primitiveMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "primitiveToWrapper", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "editableTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "extraEditableClasses", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "numericalTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "extraNumericalTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "booleanTypes", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}]}, "classes/com/sun/tools/jconsole/JConsoleContext.class": {"ver": 65, "acc": 1537, "nme": "com/sun/tools/jconsole/JConsoleContext", "super": "java/lang/Object", "mthds": [{"nme": "getMBeanServerConnection", "acc": 1025, "dsc": "()Ljavax/management/MBeanServerConnection;"}, {"nme": "getConnectionState", "acc": 1025, "dsc": "()Lcom/sun/tools/jconsole/JConsoleContext$ConnectionState;"}, {"nme": "addPropertyChangeListener", "acc": 1025, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}, {"nme": "removePropertyChangeListener", "acc": 1025, "dsc": "(Ljava/beans/PropertyChangeListener;)V"}], "flds": [{"acc": 25, "nme": "CONNECTION_STATE_PROPERTY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "connectionState"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$3", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;Z)V"}, {"nme": "doInBackground", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/lang/Exception"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4164, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$stopCellEditing", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/inspector/XSheet.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XSheet", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "dispose", "acc": 1, "dsc": "()V"}, {"nme": "setupScreen", "acc": 2, "dsc": "()V"}, {"nme": "isSelectedNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "showErrorDialog", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isMBeanNode", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)Z"}, {"nme": "displayNode", "acc": 33, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayMBeanNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayMetadataNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayMBeanAttributesNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayMBeanOperationsNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayMBeanNotificationsNode", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "displayEmptyNode", "acc": 2, "dsc": "()V"}, {"nme": "registerListener", "acc": 2, "dsc": "()V"}, {"nme": "unregisterListener", "acc": 2, "dsc": "()V"}, {"nme": "refreshAttributes", "acc": 2, "dsc": "()V"}, {"nme": "updateNotifications", "acc": 2, "dsc": "()V"}, {"nme": "updateReceivedNotifications", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;JZ)V"}, {"nme": "clearNotifications", "acc": 2, "dsc": "()V"}, {"nme": "clearNotifications0", "acc": 2, "dsc": "()V"}, {"nme": "updateNotificationsNodeLabel", "acc": 2, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;Ljava/lang/String;)V"}, {"nme": "clearCurrentNotifications", "acc": 2, "dsc": "()V"}, {"nme": "clear", "acc": 2, "dsc": "()V"}, {"nme": "handleNotification", "acc": 1, "dsc": "(Ljavax/management/Notification;Ljava/lang/Object;)V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}], "flds": [{"acc": 2, "nme": "mainPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 2, "nme": "southPanel", "dsc": "Ljavax/swing/<PERSON><PERSON>l;"}, {"acc": 66, "nme": "currentNode", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 66, "nme": "mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 2, "nme": "mbeanAttributes", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}, {"acc": 2, "nme": "mbeanOperations", "dsc": "Lsun/tools/jconsole/inspector/XMBeanOperations;"}, {"acc": 2, "nme": "mbeanNotifications", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications;"}, {"acc": 2, "nme": "mbeanInfo", "dsc": "Lsun/tools/jconsole/inspector/XMBeanInfo;"}, {"acc": 2, "nme": "refreshButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "clearButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "subscribeButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "unsubscribeButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "mbeansTab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}]}, "classes/sun/tools/jconsole/CreateMBeanDialog.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/CreateMBeanDialog", "super": "sun/tools/jconsole/InternalDialog", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/tools/jconsole/JConsole;)V"}, {"nme": "updateConnections", "acc": 2, "dsc": "()V"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "setVisible", "acc": 1, "dsc": "(Z)V"}], "flds": [{"acc": 0, "nme": "jConsole", "dsc": "Lsun/tools/jconsole/JConsole;"}, {"acc": 0, "nme": "connections", "dsc": "Ljavax/swing/JComboBox;", "sig": "Ljavax/swing/JComboBox<Lsun/tools/jconsole/ProxyClient;>;"}, {"acc": 0, "nme": "createMBeanButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "unregister<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/swing/JButton;"}, {"acc": 0, "nme": "cancelButton", "dsc": "Ljavax/swing/JButton;"}, {"acc": 26, "nme": "HOTSPOT_MBEAN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.management.HotspotInternal"}, {"acc": 26, "nme": "HOTSPOT_MBEAN_OBJECTNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.management:type=HotspotInternal"}]}, "classes/sun/tools/jconsole/VMPanel$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/VMPanel$1", "super": "java/awt/event/MouseAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/VMPanel;)V"}, {"nme": "mouseClicked", "acc": 1, "dsc": "(Ljava/awt/event/MouseEvent;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/VMPanel;"}]}, "classes/sun/tools/jconsole/OverviewPanel$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/OverviewPanel$1", "super": "javax/swing/JLabel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/OverviewPanel;Ljava/lang/String;I)V"}, {"nme": "setText", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/OverviewPanel;"}]}, "classes/sun/tools/jconsole/ClassTab$ClassOverviewPanel.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ClassTab$ClassOverviewPanel", "super": "sun/tools/jconsole/OverviewPanel", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "updateClassInfo", "acc": 2, "dsc": "(JJ)V"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData.class": {"ver": 65, "acc": 1056, "nme": "sun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData", "super": "javax/swing/JTable", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;)V"}, {"nme": "get<PERSON>iewed<PERSON><PERSON>nt", "acc": 1, "dsc": "()Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;"}, {"nme": "getToolTip", "acc": 1, "dsc": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(II)Ljavax/swing/table/TableCellRenderer;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/awt/Component;)V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/swing/table/TableCellRenderer;II)Ljava/awt/Component;"}, {"nme": "isClickableElement", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "updateColumnWidth", "acc": 4, "dsc": "()V"}, {"nme": "viewed", "acc": 1025, "dsc": "(Lsun/tools/jconsole/inspector/XOpenTypeViewer;)V", "exs": ["java/lang/Exception"]}, {"nme": "initTable", "acc": 4, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "emptyTable", "acc": 4, "dsc": "()V"}, {"nme": "setValueAt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;II)V"}], "flds": [{"acc": 0, "nme": "parent", "dsc": "Lsun/tools/jconsole/inspector/XOpenTypeViewer$XOpenTypeData;"}, {"acc": 4, "nme": "col1Width", "dsc": "I"}, {"acc": 4, "nme": "col2Width", "dsc": "I"}, {"acc": 2, "nme": "init", "dsc": "Z"}, {"acc": 2, "nme": "normalFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}, {"acc": 2, "nme": "boldFont", "dsc": "L<PERSON><PERSON>/awt/Font;"}]}, "classes/sun/tools/jconsole/Plotter$TimeStamps.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Plotter$TimeStamps", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "time", "acc": 1, "dsc": "(I)J"}, {"nme": "add", "acc": 1, "dsc": "(J)V"}], "flds": [{"acc": 0, "nme": "offsets", "dsc": "[J"}, {"acc": 0, "nme": "indices", "dsc": "[I"}, {"acc": 0, "nme": "rtimes", "dsc": "[I"}, {"acc": 0, "nme": "size", "dsc": "I"}]}, "classes/sun/tools/jconsole/inspector/XPlottingViewer.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XPlottingViewer", "super": "sun/tools/jconsole/PlotterPanel", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Ljava/lang/Object;Ljavax/swing/JTable;Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "dispose", "acc": 8, "dsc": "(Lsun/tools/jconsole/MBeansTab;)V"}, {"nme": "isViewableValue", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "loadPlotting", "acc": 9, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Ljava/lang/Object;Ljavax/swing/JTable;Lsun/tools/jconsole/MBeansTab;)Ljava/awt/Component;"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "createPlotter", "acc": 1, "dsc": "(Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Ljava/lang/String;Ljavax/swing/JTable;)Lsun/tools/jconsole/Plotter;"}, {"nme": "setupDisplay", "acc": 2, "dsc": "(Lsun/tools/jconsole/Plotter;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PLOTTER_DECIMALS", "dsc": "I", "val": 4}, {"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljavax/swing/JButton;"}, {"acc": 10, "nme": "plotter<PERSON>ache", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Lsun/tools/jconsole/inspector/XPlottingViewer;>;"}, {"acc": 10, "nme": "timer<PERSON>ache", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Ljava/lang/String;Ljava/util/Timer;>;"}, {"acc": 2, "nme": "tab", "dsc": "Lsun/tools/jconsole/MBeansTab;"}, {"acc": 2, "nme": "attributeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "table", "dsc": "Ljavax/swing/JTable;"}]}, "classes/sun/tools/jconsole/inspector/XSheet$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$2", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;Lsun/tools/jconsole/inspector/XNodeInfo;Ljavax/swing/tree/DefaultMutableTreeNode;Lsun/tools/jconsole/inspector/XMBeanInfo;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()Ljavax/management/MBeanAttributeInfo;"}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$uo", "dsc": "Lsun/tools/jconsole/inspector/XNodeInfo;"}, {"acc": 4112, "nme": "val$node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 4112, "nme": "val$mbi", "dsc": "Lsun/tools/jconsole/inspector/XMBeanInfo;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/Plotter$SaveDataFileChooser.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Plotter$SaveDataFileChooser", "super": "javax/swing/JFile<PERSON>hooser", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "approveSelection", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -5182890922369369669}]}, "classes/sun/tools/jconsole/inspector/XPlottingViewer$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XPlottingViewer$2", "super": "java/util/TimerTask", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XPlottingViewer;Lsun/tools/jconsole/inspector/XMBean;Ljava/lang/String;Lsun/tools/jconsole/Plotter;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$xmbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 4112, "nme": "val$attributeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$plotter", "dsc": "Lsun/tools/jconsole/Plotter;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XPlottingViewer;"}]}, "classes/sun/tools/jconsole/inspector/XOperations$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XOperations$1", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XOperations;Ljavax/swing/JButton;Lsun/tools/jconsole/inspector/OperationEntry;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "done", "acc": 4, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$button", "dsc": "Ljavax/swing/JButton;"}, {"acc": 4112, "nme": "val$entryIf", "dsc": "Lsun/tools/jconsole/inspector/OperationEntry;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XOperations;"}]}, "classes/sun/tools/jconsole/Plotter$Sequence.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/Plotter$Sequence", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "value", "acc": 1, "dsc": "(I)J"}, {"nme": "add", "acc": 1, "dsc": "(J)V"}], "flds": [{"acc": 0, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "color", "dsc": "Ljava/awt/Color;"}, {"acc": 0, "nme": "isPlotted", "dsc": "Z"}, {"acc": 0, "nme": "transitionStroke", "dsc": "<PERSON><PERSON><PERSON>/awt/Stroke;"}, {"acc": 0, "nme": "values", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 0, "nme": "size", "dsc": "I"}]}, "classes/sun/tools/jconsole/inspector/XObject.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XObject", "super": "javax/swing/JLabel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljavax/swing/Icon;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getObject", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "useHashCodeRepresentation", "acc": 9, "dsc": "(Z)V"}, {"nme": "hashCodeRepresentation", "acc": 9, "dsc": "()Z"}, {"nme": "setObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "object", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "useHashCodeRepresentation", "dsc": "Z"}, {"acc": 25, "nme": "NULL_OBJECT", "dsc": "Lsun/tools/jconsole/inspector/XObject;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$ValueCellEditor.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$ValueCellEditor", "super": "sun/tools/jconsole/inspector/XTextFieldEditor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;)V"}, {"nme": "getTableCellEditorComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZII)Ljava/awt/Component;"}, {"nme": "stopCellEditing", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/inspector/XTreeRenderer.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/<PERSON>Tree<PERSON><PERSON><PERSON>", "super": "javax/swing/tree/DefaultTreeCellRenderer", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getTreeCellRendererComponent", "acc": 1, "dsc": "(Ljavax/swing/JTree;<PERSON><PERSON><PERSON>/lang/Object;ZZZIZ)Ljava/awt/Component;"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XMBeanAttributes$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanAttributes$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanAttributes;Lsun/tools/jconsole/inspector/XMBean;Ljavax/management/MBeanInfo;[Ljavax/management/MBeanAttributeInfo;Ljava/util/HashMap;Ljava/util/HashMap;Ljava/util/HashMap;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$mbean", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 4112, "nme": "val$curMBeanInfo", "dsc": "Ljavax/management/MBeanInfo;"}, {"acc": 4112, "nme": "val$attrsInfo", "dsc": "[Ljavax/management/MBeanAttributeInfo;"}, {"acc": 4112, "nme": "val$attrs", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"acc": 4112, "nme": "val$unavailableAttrs", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"acc": 4112, "nme": "val$viewableAttrs", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanAttributes;"}]}, "classes/sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCellEditor.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XMBeanNotifications$UserDataCellEditor", "super": "sun/tools/jconsole/inspector/XTextFieldEditor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XMBeanNotifications;)V"}, {"nme": "getTableCellEditorComponent", "acc": 1, "dsc": "(Ljavax/swing/JTable;<PERSON><PERSON><PERSON>/lang/Object;ZII)Ljava/awt/Component;"}, {"nme": "stopCellEditing", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XMBeanNotifications;"}]}, "classes/sun/tools/jconsole/JConsole$5.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/JConsole$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/JConsole;Lja<PERSON>/lang/Exception;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$ex", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 4112, "nme": "val$url", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$userName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "val$password", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/JConsole;"}]}, "classes/sun/tools/jconsole/inspector/XSheet$3.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XSheet$3", "super": "javax/swing/SwingWorker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XSheet;Lsun/tools/jconsole/inspector/XMBean;Ljavax/swing/tree/DefaultMutableTreeNode;)V"}, {"nme": "doInBackground", "acc": 1, "dsc": "()Ljavax/management/MBeanInfo;", "exs": ["javax/management/InstanceNotFoundException", "javax/management/IntrospectionException", "javax/management/ReflectionException", "java/io/IOException"]}, {"nme": "done", "acc": 4, "dsc": "()V"}, {"nme": "doInBackground", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$xmb", "dsc": "Lsun/tools/jconsole/inspector/XMBean;"}, {"acc": 4112, "nme": "val$node", "dsc": "Ljavax/swing/tree/DefaultMutableTreeNode;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XSheet;"}]}, "classes/sun/tools/jconsole/inspector/XPlottingViewer$1.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XPlottingViewer$1", "super": "sun/tools/jconsole/inspector/XPlotter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/inspector/XPlottingViewer;Ljavax/swing/JTable;Lsun/tools/jconsole/Plotter$Unit;)V"}, {"nme": "getPreferredSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}, {"nme": "getMinimumSize", "acc": 1, "dsc": "()Ljava/awt/Dimension;"}], "flds": [{"acc": 0, "nme": "prefSize", "dsc": "Ljava/awt/Dimension;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/inspector/XPlottingViewer;"}]}, "classes/sun/tools/jconsole/inspector/XTree$ComparableDefaultMutableTreeNode.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/inspector/XTree$ComparableDefaultMutableTreeNode", "super": "javax/swing/tree/DefaultMutableTreeNode", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "compareTo", "acc": 1, "dsc": "(Ljavax/swing/tree/DefaultMutableTreeNode;)I"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "classes/sun/tools/jconsole/inspector/XTextField.class": {"ver": 65, "acc": 33, "nme": "sun/tools/jconsole/inspector/XTextField", "super": "javax/swing/<PERSON>anel", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;IZLjavax/swing/JButton;Lsun/tools/jconsole/inspector/XOperations;)V", "sig": "(L<PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;IZLjavax/swing/JButton;Lsun/tools/jconsole/inspector/XOperations;)V"}, {"nme": "setNullSelectionAllowed", "acc": 9, "dsc": "(Z)V"}, {"nme": "getNullSelectionAllowed", "acc": 9, "dsc": "()Z"}, {"nme": "init", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/Class<*>;)V"}, {"nme": "clearObject", "acc": 34, "dsc": "()V"}, {"nme": "setDefaultColors", "acc": 34, "dsc": "()V"}, {"nme": "setHorizontalAlignment", "acc": 1, "dsc": "(I)V"}, {"nme": "buildJMenuItem", "acc": 4, "dsc": "(Lsun/tools/jconsole/inspector/XObject;I)Ljavax/swing/JMenuItem;"}, {"nme": "actionPerformed", "acc": 1, "dsc": "(Ljava/awt/event/ActionEvent;)V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "changedUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "removeUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "insertUpdate", "acc": 1, "dsc": "(Ljavax/swing/event/DocumentEvent;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "selectedObject", "dsc": "Lsun/tools/jconsole/inspector/XObject;"}, {"acc": 4, "nme": "textField", "dsc": "Ljavax/swing/JTextField;"}, {"acc": 10, "nme": "allowNullSelection", "dsc": "Z"}, {"acc": 28, "nme": "COMPATIBLE_VALUE", "dsc": "I", "val": 1}, {"acc": 28, "nme": "CURRENT_VALUE", "dsc": "I", "val": 2}, {"acc": 28, "nme": "NULL_VALUE", "dsc": "I", "val": 3}, {"acc": 2, "nme": "button", "dsc": "Ljavax/swing/JButton;"}, {"acc": 2, "nme": "operation", "dsc": "Lsun/tools/jconsole/inspector/XOperations;"}]}, "classes/sun/tools/jconsole/ThreadTab$2.class": {"ver": 65, "acc": 32, "nme": "sun/tools/jconsole/ThreadTab$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/tools/jconsole/ThreadTab;JLjavax/swing/JTextArea;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "val$threadID", "dsc": "J"}, {"acc": 4112, "nme": "val$textArea", "dsc": "Ljavax/swing/JTextArea;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/tools/jconsole/ThreadTab;"}]}}}}