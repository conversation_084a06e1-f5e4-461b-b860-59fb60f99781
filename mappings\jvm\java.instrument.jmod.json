{"md5": "c5c95b7235a9d300d8c1c75867471f6e", "sha2": "b8b27dccb736dd3fa68c5e16f56a692998ebba30", "sha256": "215021307d0a1f6047eb032b0281f48cc6c119a6d38bc067abf99349d71fbc69", "contents": {"classes": {"classes/sun/instrument/InstrumentationImpl$1.class": {"ver": 65, "acc": 32, "nme": "sun/instrument/InstrumentationImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/reflect/AccessibleObject;Z)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$ao", "dsc": "Ljava/lang/reflect/AccessibleObject;"}, {"acc": 4112, "nme": "val$accessible", "dsc": "Z"}]}, "classes/sun/instrument/TransformerManager$TransformerInfo.class": {"ver": 65, "acc": 32, "nme": "sun/instrument/TransformerManager$TransformerInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/instrument/TransformerManager;Ljava/lang/instrument/ClassFileTransformer;)V"}, {"nme": "transformer", "acc": 0, "dsc": "()Ljava/lang/instrument/ClassFileTransformer;"}, {"nme": "getPrefix", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setPrefix", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 16, "nme": "mTransformer", "dsc": "Ljava/lang/instrument/ClassFileTransformer;"}, {"acc": 0, "nme": "mPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/java/lang/instrument/IllegalClassFormatException.class": {"ver": 65, "acc": 33, "nme": "java/lang/instrument/IllegalClassFormatException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -3841736710924794009}]}, "classes/sun/instrument/TransformerManager.class": {"ver": 65, "acc": 33, "nme": "sun/instrument/TransformerManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Z)V"}, {"nme": "isRetransformable", "acc": 0, "dsc": "()Z"}, {"nme": "addTransformer", "acc": 33, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)V"}, {"nme": "removeTransformer", "acc": 33, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)Z"}, {"nme": "includesTransformer", "acc": 32, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)Z"}, {"nme": "getSnapshotTransformerList", "acc": 2, "dsc": "()[Lsun/instrument/TransformerManager$TransformerInfo;"}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B", "sig": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON>ja<PERSON>/lang/String;Ljava/lang/Class<*>;Ljava/security/ProtectionDomain;[B)[B"}, {"nme": "getTransformerCount", "acc": 0, "dsc": "()I"}, {"nme": "setNativeMethodPrefix", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/instrument/ClassFileTransformer;Ljava/lang/String;)Z"}, {"nme": "getNativeMethodPrefixes", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "mTransformerList", "dsc": "[Lsun/instrument/TransformerManager$TransformerInfo;"}, {"acc": 2, "nme": "mIsRetransformable", "dsc": "Z"}]}, "classes/java/lang/instrument/UnmodifiableModuleException.class": {"ver": 65, "acc": 33, "nme": "java/lang/instrument/UnmodifiableModuleException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6912511912351080644}]}, "classes/java/lang/instrument/ClassDefinition.class": {"ver": 65, "acc": 49, "nme": "java/lang/instrument/ClassDefinition", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;[B)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;[B)V"}, {"nme": "getDefinitionClass", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "getDefinitionClassFile", "acc": 1, "dsc": "()[B"}], "flds": [{"acc": 18, "nme": "mClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "mClassFile", "dsc": "[B"}]}, "classes/java/lang/instrument/ClassFileTransformer.class": {"ver": 65, "acc": 1537, "nme": "java/lang/instrument/ClassFileTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B", "sig": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;Lja<PERSON>/lang/String;Ljava/lang/Class<*>;Ljava/security/ProtectionDomain;[B)[B", "exs": ["java/lang/instrument/IllegalClassFormatException"]}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B", "sig": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON>ja<PERSON>/lang/String;Ljava/lang/Class<*>;Ljava/security/ProtectionDomain;[B)[B", "exs": ["java/lang/instrument/IllegalClassFormatException"]}], "flds": []}, "classes/java/lang/instrument/Instrumentation.class": {"ver": 65, "acc": 1537, "nme": "java/lang/instrument/Instrumentation", "super": "java/lang/Object", "mthds": [{"nme": "addTransformer", "acc": 1025, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;Z)V"}, {"nme": "addTransformer", "acc": 1025, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)V"}, {"nme": "removeTransformer", "acc": 1025, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)Z"}, {"nme": "isRetransformClassesSupported", "acc": 1025, "dsc": "()Z"}, {"nme": "retransformClasses", "acc": 1153, "dsc": "([Ljava/lang/Class;)V", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;)V", "exs": ["java/lang/instrument/UnmodifiableClassException"]}, {"nme": "isRedefineClassesSupported", "acc": 1025, "dsc": "()Z"}, {"nme": "redefineClasses", "acc": 1153, "dsc": "([Ljava/lang/instrument/ClassDefinition;)V", "exs": ["java/lang/ClassNotFoundException", "java/lang/instrument/UnmodifiableClassException"]}, {"nme": "isModifiableClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getAllLoadedClasses", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;"}, {"nme": "getInitiatedClasses", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)[Ljava/lang/Class;"}, {"nme": "getObjectSize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)J"}, {"nme": "appendToBootstrapClassLoaderSearch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)V"}, {"nme": "appendToSystemClassLoaderSearch", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)V"}, {"nme": "isNativeMethodPrefixSupported", "acc": 1025, "dsc": "()Z"}, {"nme": "setNativeMethodPrefix", "acc": 1025, "dsc": "(Lja<PERSON>/lang/instrument/ClassFileTransformer;Ljava/lang/String;)V"}, {"nme": "redefineModule", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Map;Ljava/util/Map;Ljava/util/Set;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/Module;Ljava/util/Set<Ljava/lang/Module;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;Ljava/util/Set<Ljava/lang/Class<*>;>;Ljava/util/Map<Ljava/lang/Class<*>;Ljava/util/List<Ljava/lang/Class<*>;>;>;)V"}, {"nme": "isModifiableModule", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)Z"}], "flds": []}, "classes/java/lang/instrument/UnmodifiableClassException.class": {"ver": 65, "acc": 33, "nme": "java/lang/instrument/UnmodifiableClassException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1716652643585309178}]}, "classes/sun/instrument/InstrumentationImpl$HolderStackWalker.class": {"ver": 65, "acc": 32, "nme": "sun/instrument/InstrumentationImpl$HolderStackWalker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "walker", "dsc": "<PERSON><PERSON><PERSON>/lang/<PERSON>ack<PERSON>;"}]}, "classes/sun/instrument/InstrumentationImpl.class": {"ver": 65, "acc": 33, "nme": "sun/instrument/InstrumentationImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(JZZZ)V"}, {"nme": "addTransformer", "acc": 1, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)V"}, {"nme": "addTransformer", "acc": 1, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;Z)V"}, {"nme": "removeTransformer", "acc": 1, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)Z"}, {"nme": "isModifiableClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isModifiableModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;)Z"}, {"nme": "isRetransformClassesSupported", "acc": 1, "dsc": "()Z"}, {"nme": "retransformClasses", "acc": 129, "dsc": "([Ljava/lang/Class;)V", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;)V"}, {"nme": "isRedefineClassesSupported", "acc": 1, "dsc": "()Z"}, {"nme": "redefineClasses", "acc": 129, "dsc": "([Ljava/lang/instrument/ClassDefinition;)V", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "getAllLoadedClasses", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;"}, {"nme": "getInitiatedClasses", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)[Ljava/lang/Class;"}, {"nme": "getObjectSize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)J"}, {"nme": "appendToBootstrapClassLoaderSearch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)V"}, {"nme": "appendToSystemClassLoaderSearch", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)V"}, {"nme": "isNativeMethodPrefixSupported", "acc": 1, "dsc": "()Z"}, {"nme": "setNativeMethodPrefix", "acc": 1, "dsc": "(Lja<PERSON>/lang/instrument/ClassFileTransformer;Ljava/lang/String;)V"}, {"nme": "redefineModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Map;Ljava/util/Map;Ljava/util/Set;Ljava/util/Map;)V", "sig": "(Lja<PERSON>/lang/Module;Ljava/util/Set<Ljava/lang/Module;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;Ljava/util/Set<Ljava/lang/Class<*>;>;Ljava/util/Map<Ljava/lang/Class<*>;Ljava/util/List<Ljava/lang/Class<*>;>;>;)V"}, {"nme": "cloneAndCheckMap", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Map;", "sig": "(Ljava/lang/Module;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;)Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/Module;>;>;"}, {"nme": "findTransformerManager", "acc": 2, "dsc": "(Ljava/lang/instrument/ClassFileTransformer;)Lsun/instrument/TransformerManager;"}, {"nme": "jarFile", "acc": 258, "dsc": "(J)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isModifiableClass0", "acc": 258, "dsc": "(J<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(JLjava/lang/Class<*>;)Z"}, {"nme": "isRetransformClassesSupported0", "acc": 258, "dsc": "(J)Z"}, {"nme": "setHasTransformers", "acc": 258, "dsc": "(JZ)V"}, {"nme": "setHasRetransformableTransformers", "acc": 258, "dsc": "(JZ)V"}, {"nme": "retransformClasses0", "acc": 258, "dsc": "(J[<PERSON>java/lang/Class;)V", "sig": "(J[<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "redefineClasses0", "acc": 258, "dsc": "(J[Ljava/lang/instrument/ClassDefinition;)V", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "getAllLoadedClasses0", "acc": 258, "dsc": "(J)[<PERSON>java/lang/Class;"}, {"nme": "getInitiatedClasses0", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/ClassLoader;)[Lja<PERSON>/lang/Class;"}, {"nme": "getObjectSize0", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)J", "vanns": [{"dsc": "Ljdk/internal/vm/annotation/IntrinsicCandidate;"}]}, {"nme": "appendToClassLoaderSearch0", "acc": 258, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "setNativeMethodPrefixes", "acc": 258, "dsc": "(J[<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "setAccessible", "acc": 10, "dsc": "(Ljava/lang/reflect/AccessibleObject;Z)V"}, {"nme": "loadClassAndStartAgent", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["java/lang/Throwable"]}, {"nme": "loadClassAndCallPremain", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Throwable"]}, {"nme": "loadClassAndCallAgentmain", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Throwable"]}, {"nme": "transform", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[BZ)[B", "sig": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON>java/lang/String;Ljava/lang/Class<*>;Ljava/security/ProtectionDomain;[BZ)[B"}, {"nme": "loadAgent", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "loadAgent0", "acc": 266, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "trace", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "codeSource", "acc": 10, "dsc": "(Ljava/lang/Class;)Ljava/net/URL;", "sig": "(Ljava/lang/Class<*>;)Ljava/net/URL;"}, {"nme": "lambda$trace$9", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/StackWalker$StackFrame;)V"}, {"nme": "lambda$trace$8", "acc": 4106, "dsc": "(Ljava/util/stream/Stream;)Ljava/util/List;"}, {"nme": "lambda$trace$7", "acc": 4106, "dsc": "(Lja<PERSON>/lang/StackWalker$StackFrame;)Z"}, {"nme": "lambda$redefineModule$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/lang/Class;)V"}, {"nme": "lambda$redefineModule$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/Class;)V"}, {"nme": "lambda$redefineModule$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Module;)V"}, {"nme": "lambda$redefineModule$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Module;)V"}, {"nme": "lambda$redefineModule$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>le;<PERSON><PERSON><PERSON>/lang/Module;)V"}, {"nme": "lambda$redefineModule$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Module;<PERSON><PERSON><PERSON>/lang/Class;<PERSON>ja<PERSON>/lang/Class;)V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "TRACE_USAGE_PROP_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.instrument.traceUsage"}, {"acc": 26, "nme": "TRACE_USAGE", "dsc": "Z"}, {"acc": 18, "nme": "mTransformerManager", "dsc": "Lsun/instrument/TransformerManager;"}, {"acc": 2, "nme": "mRetransfomableTransformerManager", "dsc": "Lsun/instrument/TransformerManager;"}, {"acc": 18, "nme": "mNativeAgent", "dsc": "J"}, {"acc": 18, "nme": "mEnvironmentSupportsRedefineClasses", "dsc": "Z"}, {"acc": 66, "nme": "mEnvironmentSupportsRetransformClassesKnown", "dsc": "Z"}, {"acc": 66, "nme": "mEnvironmentSupportsRetransformClasses", "dsc": "Z"}, {"acc": 18, "nme": "mEnvironmentSupportsNativeMethodPrefix", "dsc": "Z"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}}}}