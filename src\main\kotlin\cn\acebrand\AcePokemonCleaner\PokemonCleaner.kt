package cn.acebrand.AcePokemonCleaner

import org.bukkit.Bukkit
import org.bukkit.World
import org.bukkit.plugin.Plugin
import org.bukkit.entity.Entity
import java.util.concurrent.ConcurrentHashMap
import java.util.logging.Level

// 不直接导入Cobblemon类，使用反射来避免混淆问题
// import com.cobblemon.mod.common.entity.pokemon.PokemonEntity
// import com.cobblemon.mod.common.pokemon.Pokemon

/**
 * 精灵清理器 - 使用简化的实体清理方法
 */
class PokemonCleaner(
    private val plugin: Plugin,
    private val configManager: ConfigManager
) {

    // 跟踪精灵生成时间
    private val pokemonSpawnTimes = ConcurrentHashMap<String, Long>()

    // 跟踪原版生物生成时间
    private val vanillaMobSpawnTimes = ConcurrentHashMap<String, Long>()

    /**
     * 执行清理任务
     */
    fun performClean(): CleanResult {
        val startTime = System.currentTimeMillis()
        var pokemonCleanedCount = 0
        var vanillaMobCleanedCount = 0
        var itemCleanedCount = 0
        val worldResults = mutableMapOf<String, WorldCleanResult>()

        try {
            for (worldName in configManager.enabledWorlds) {
                val world = Bukkit.getWorld(worldName)
                if (world == null) {
                    if (configManager.enableLogging) {
                        plugin.logger.warning("世界 '$worldName' 不存在，跳过清理")
                    }
                    continue
                }

                val worldResult = cleanWorldEntities(world)
                worldResults[worldName] = worldResult
                pokemonCleanedCount += worldResult.pokemonCount
                vanillaMobCleanedCount += worldResult.vanillaMobCount
                itemCleanedCount += worldResult.itemCount

                val totalCleaned = pokemonCleanedCount + vanillaMobCleanedCount + itemCleanedCount
                if (totalCleaned >= configManager.maxCleanPerRun) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("已达到单次清理上限 (${configManager.maxCleanPerRun})，停止清理")
                    }
                    break
                }
            }

            val duration = System.currentTimeMillis() - startTime
            val totalCleaned = pokemonCleanedCount + vanillaMobCleanedCount + itemCleanedCount

            // 发送清理公告
            if (configManager.enableAnnouncement && totalCleaned >= configManager.minAnnounceCount) {
                sendCleanupAnnouncement(pokemonCleanedCount, vanillaMobCleanedCount, itemCleanedCount, duration)
            }

            // 显示清理完成的 BossBar
            if (plugin is AcePokemonCleaner) {
                (plugin as AcePokemonCleaner).showCleanupCompleteBossBar(totalCleaned, pokemonCleanedCount, vanillaMobCleanedCount, itemCleanedCount)
            }

            return CleanResult(
                success = true,
                pokemonCleanedCount = pokemonCleanedCount,
                vanillaMobCleanedCount = vanillaMobCleanedCount,
                itemCleanedCount = itemCleanedCount,
                duration = duration,
                worldResults = worldResults
            )

        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "执行清理任务时发生错误", e)
            return CleanResult(
                success = false,
                pokemonCleanedCount = pokemonCleanedCount,
                vanillaMobCleanedCount = vanillaMobCleanedCount,
                itemCleanedCount = itemCleanedCount,
                duration = System.currentTimeMillis() - startTime,
                worldResults = worldResults,
                error = e.message
            )
        }
    }

    /**
     * 清理指定世界中的实体
     */
    private fun cleanWorldEntities(world: World): WorldCleanResult {
        var pokemonCleanedCount = 0
        var vanillaMobCleanedCount = 0
        var itemCleanedCount = 0
        val currentTime = System.currentTimeMillis()

        try {
            // 获取世界中的所有实体
            val entities = world.entities.toList()

            for (entity in entities) {
                val totalCleaned = pokemonCleanedCount + vanillaMobCleanedCount + itemCleanedCount
                if (totalCleaned >= configManager.maxCleanPerRun) {
                    break
                }

                // 检查是否为精灵实体
                if (isPokemonEntity(entity)) {
                    if (pokemonCleanedCount < configManager.maxCleanPerRun && shouldCleanPokemonEntity(entity, currentTime)) {
                        try {
                            // 从跟踪列表中移除
                            pokemonSpawnTimes.remove(entity.uniqueId.toString())

                            // 移除实体
                            entity.remove()
                            pokemonCleanedCount++

                            if (configManager.enableLogging) {
                                val entityName = try {
                                    entity.name ?: "Unknown Pokemon"
                                } catch (e: Exception) {
                                    "Unknown"
                                }
                                plugin.logger.fine("清理了精灵: $entityName 在世界 ${world.name}")
                            }
                        } catch (e: Exception) {
                            plugin.logger.log(Level.WARNING, "清理精灵时发生错误", e)
                        }
                    }
                }
                // 检查是否为原版生物实体
                else if (configManager.enableVanillaMobCleaning && isVanillaMobEntity(entity)) {
                    if (vanillaMobCleanedCount < configManager.vanillaMobMaxCleanPerRun && shouldCleanVanillaMobEntity(entity, currentTime)) {
                        try {
                            // 从跟踪列表中移除
                            vanillaMobSpawnTimes.remove(entity.uniqueId.toString())

                            // 移除实体
                            entity.remove()
                            vanillaMobCleanedCount++

                            if (configManager.enableLogging) {
                                val entityName = try {
                                    entity.name ?: entity.type.name
                                } catch (e: Exception) {
                                    "Unknown"
                                }
                                plugin.logger.fine("清理了原版生物: $entityName 在世界 ${world.name}")
                            }
                        } catch (e: Exception) {
                            plugin.logger.log(Level.WARNING, "清理原版生物时发生错误", e)
                        }
                    }
                }
                // 检查是否为掉落物实体
                else if (configManager.enableItemCleaning && isItemEntity(entity)) {
                    if (itemCleanedCount < configManager.itemMaxCleanPerRun && shouldCleanItemEntity(entity, currentTime)) {
                        try {
                            // 移除实体
                            entity.remove()
                            itemCleanedCount++

                            if (configManager.enableLogging) {
                                val itemName = try {
                                    getItemEntityInfo(entity)
                                } catch (e: Exception) {
                                    "Unknown Item"
                                }
                                plugin.logger.fine("清理了掉落物: $itemName 在世界 ${world.name}")
                            }
                        } catch (e: Exception) {
                            plugin.logger.log(Level.WARNING, "清理掉落物时发生错误", e)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "清理世界 ${world.name} 时发生错误", e)
        }

        return WorldCleanResult(pokemonCleanedCount, vanillaMobCleanedCount, itemCleanedCount)
    }

    /**
     * 检查是否为精灵实体 - 使用反射避免混淆问题
     */
    private fun isPokemonEntity(entity: Entity): Boolean {
        return try {
            val className = entity.javaClass.simpleName
            val fullClassName = entity.javaClass.name

            if (configManager.debugMode) {
                plugin.logger.info("检查实体类型:")
                plugin.logger.info("  类名: $className")
                plugin.logger.info("  完整类名: $fullClassName")
            }

            // 方法1: 检查类名是否包含Pokemon（更宽松的检查，但排除精灵球）
            if (className.contains("Pokemon", ignoreCase = true)) {
                // 排除精灵球相关类
                if (className.contains("Ball", ignoreCase = true) ||
                    className.contains("Pokeball", ignoreCase = true)) {
                    if (configManager.debugMode) {
                        plugin.logger.info("  类名包含Ball，排除精灵球实体: $className")
                    }
                    return false
                }

                if (configManager.debugMode) {
                    plugin.logger.info("  通过类名识别为精灵实体")
                }
                return true
            }

            // 方法2: 检查完整类名是否包含cobblemon（但排除精灵球）
            if (fullClassName.contains("cobblemon", ignoreCase = true)) {
                // 排除精灵球相关类
                if (fullClassName.contains("ball", ignoreCase = true) ||
                    fullClassName.contains("pokeball", ignoreCase = true)) {
                    if (configManager.debugMode) {
                        plugin.logger.info("  完整类名包含ball，排除精灵球实体: $fullClassName")
                    }
                    return false
                }

                if (configManager.debugMode) {
                    plugin.logger.info("  通过完整类名识别为精灵实体")
                }
                return true
            }

            // 方法3: 尝试通过反射检查是否有Pokemon字段
            try {
                val pokemonField = entity.javaClass.declaredFields.find { field ->
                    field.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                    !field.type.simpleName.contains("Entity", ignoreCase = true)
                }
                if (pokemonField != null) {
                    if (configManager.debugMode) {
                        plugin.logger.info("  通过Pokemon字段识别为精灵实体: ${pokemonField.name}")
                    }
                    return true
                }
            } catch (e: Exception) {
                // 反射失败，继续其他检查
            }

            // 方法4: 检查是否有getPokemon方法
            try {
                val getPokemonMethod = entity.javaClass.getMethod("getPokemon")
                if (getPokemonMethod != null) {
                    if (configManager.debugMode) {
                        plugin.logger.info("  通过getPokemon方法识别为精灵实体")
                    }
                    return true
                }
            } catch (e: Exception) {
                // 方法不存在，继续其他检查
            }

            // 方法5: 检查实体类型名称（排除精灵球）
            val entityTypeName = entity.type.name.lowercase()
            if (entityTypeName.contains("pokemon") || entityTypeName.contains("cobblemon")) {
                // 排除精灵球相关实体
                if (entityTypeName.contains("pokeball") ||
                    entityTypeName.contains("poke_ball") ||
                    entityTypeName.contains("ball")) {
                    if (configManager.debugMode) {
                        plugin.logger.info("  实体类型包含ball，排除精灵球实体: $entityTypeName")
                    }
                    return false
                }

                if (configManager.debugMode) {
                    plugin.logger.info("  通过实体类型名称识别为精灵实体: $entityTypeName")
                }
                return true
            }

            if (configManager.debugMode) {
                plugin.logger.info("  未识别为精灵实体")
            }
            false
        } catch (e: Exception) {
            if (configManager.debugMode) {
                plugin.logger.warning("检查精灵实体时发生错误: ${e.message}")
            }
            false
        }
    }

    /**
     * 判断是否应该清理指定的精灵实体
     */
    private fun shouldCleanPokemonEntity(entity: Entity, currentTime: Long): Boolean {
        try {
            // 基本检查
            if (entity.isDead) {
                return false
            }

            // 检查是否在排除列表中
            if (isPokemonExcluded(entity)) {
                if (configManager.enableLogging) {
                    val entityInfo = getPokemonEntityInfo(entity)
                    plugin.logger.info("精灵被排除清理: $entityInfo")
                }
                return false
            }

            // 检查精灵拥有状态（统一处理cleanOnlyWild和cleanOwned设置）
            if (isPokemonEntity(entity)) {
                try {
                    val isOwned = isPokemonPlayerOwned(entity)
                    if (configManager.enableLogging) {
                        plugin.logger.info("检查精灵拥有状态: ${getPokemonEntityInfo(entity)} - 被拥有: $isOwned")
                        plugin.logger.info("cleanOnlyWild: ${configManager.cleanOnlyWild}, cleanOwned: ${configManager.cleanOwned}")
                    }

                    if (isOwned) {
                        // 如果精灵被玩家拥有，检查是否应该清理
                        if (configManager.cleanOnlyWild && !configManager.cleanOwned) {
                            // cleanOnlyWild=true 且 cleanOwned=false：不清理被拥有的精灵
                            if (configManager.enableLogging) {
                                plugin.logger.info("精灵被排除（只清理野生 + 不清理被拥有）: ${getPokemonEntityInfo(entity)}")
                            }
                            return false
                        } else if (!configManager.cleanOwned) {
                            // cleanOwned=false：不清理被拥有的精灵（无论cleanOnlyWild设置如何）
                            if (configManager.enableLogging) {
                                plugin.logger.info("精灵被排除（高级设置：不清理被拥有）: ${getPokemonEntityInfo(entity)}")
                            }
                            return false
                        } else {
                            // cleanOwned=true：清理被拥有的精灵
                            if (configManager.enableLogging) {
                                plugin.logger.info("精灵将被清理（高级设置：清理被拥有）: ${getPokemonEntityInfo(entity)}")
                            }
                        }
                    } else {
                        // 精灵不被玩家拥有（野生精灵）
                        if (configManager.enableLogging) {
                            plugin.logger.info("野生精灵，继续检查其他条件: ${getPokemonEntityInfo(entity)}")
                        }
                    }
                } catch (e: Exception) {
                    // 如果无法检查，保守处理
                    if (configManager.enableLogging) {
                        plugin.logger.warning("检查精灵拥有状态时发生错误: ${e.message}")
                    }
                    return false
                }
            }

            // 检查实体年龄
            val entityId = entity.uniqueId.toString()
            val spawnTime = pokemonSpawnTimes[entityId]
            if (spawnTime != null) {
                val ageSeconds = (currentTime - spawnTime) / 1000
                if (ageSeconds < configManager.minAge) {
                    return false
                }
            } else {
                // 如果没有记录生成时间，使用实体存在时间
                val ageSeconds = entity.ticksLived / 20
                if (ageSeconds < configManager.minAge) {
                    return false
                }
            }

            // 检查是否在玩家附近
            if (isNearPlayer(entity)) {
                return false
            }

            return true

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "检查实体清理条件时发生错误", e)
            return false
        }
    }

    /**
     * 检查精灵是否应该被排除清理 - 使用反射避免混淆问题
     */
    private fun isPokemonExcluded(entity: Entity): Boolean {
        try {
            // 确保是Cobblemon精灵实体
            if (!isPokemonEntity(entity)) {
                return false
            }

            val entityName = try {
                entity.name?.lowercase() ?: entity.type.name.lowercase()
            } catch (e: Exception) {
                "unknown"
            }

            if (configManager.debugMode) {
                plugin.logger.info("检查精灵排除状态:")
                plugin.logger.info("  实体名称: $entityName")
                plugin.logger.info("  精灵种类: ${getPokemonSpecies(entity)}")
                plugin.logger.info("  是否闪光: ${isPokemonShiny(entity)}")
                plugin.logger.info("  是否传说: ${isPokemonLegendary(entity)}")
                plugin.logger.info("  是否被玩家拥有: ${isPokemonPlayerOwned(entity)}")
                plugin.logger.info("  是否在战斗: ${isPokemonBattling(entity)}")
                plugin.logger.info("  是否繁忙: ${isPokemonBusy(entity)}")
            }

            // 检查基本名称匹配
            if (configManager.isPokemonExcluded(entityName)) {
                if (configManager.enableLogging) {
                    plugin.logger.info("精灵被排除（实体名称匹配）: $entityName")
                }
                return true
            }

            // 检查精灵种类名称匹配
            val speciesName = getPokemonSpecies(entity).lowercase()
            if (configManager.isPokemonExcluded(speciesName)) {
                if (configManager.enableLogging) {
                    plugin.logger.info("精灵被排除（种类名称匹配）: $speciesName")
                }
                return true
            }

            // 检查是否为闪光精灵
            if (configManager.excludedPokemon.any { it.equals("shiny", ignoreCase = true) }) {
                if (isPokemonShiny(entity)) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("精灵被排除（闪光精灵）: $entityName")
                    }
                    return true
                }
            }

            // 检查是否为传说精灵
            if (configManager.excludedPokemon.any { it.equals("legendary", ignoreCase = true) }) {
                if (isPokemonLegendary(entity)) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("精灵被排除（传说精灵）: $entityName")
                    }
                    return true
                }
            }

            // 检查是否为幻之精灵（通过种类名称）
            if (configManager.excludedPokemon.any { it.equals("mythical", ignoreCase = true) }) {
                if (isMythicalPokemonByName(speciesName)) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("精灵被排除（幻之精灵）: $entityName")
                    }
                    return true
                }
            }

            // 拥有状态检查已在shouldCleanPokemonEntity中统一处理，这里不再重复检查

            // 检查高级设置 - 是否在战斗中
            val isBattling = isPokemonBattling(entity)
            if (configManager.enableLogging) {
                plugin.logger.info("战斗状态检查: cleanBattling=${configManager.cleanBattling}, isBattling=$isBattling")
            }
            if (!configManager.cleanBattling && isBattling) {
                if (configManager.enableLogging) {
                    plugin.logger.info("精灵被排除（正在战斗）: $entityName")
                }
                return true
            }

            // 检查高级设置 - 是否繁忙
            val isBusy = isPokemonBusy(entity)
            if (configManager.enableLogging) {
                plugin.logger.info("繁忙状态检查: cleanBusy=${configManager.cleanBusy}, isBusy=$isBusy")
            }
            if (!configManager.cleanBusy && isBusy) {
                if (configManager.enableLogging) {
                    plugin.logger.info("精灵被排除（繁忙状态）: $entityName")
                }
                return true
            }

            // 检查高级设置 - 是否不可捕获
            if (!configManager.cleanUncatchable && isPokemonUncatchable(entity)) {
                if (configManager.enableLogging) {
                    plugin.logger.info("精灵被排除（不可捕获）: $entityName")
                }
                return true
            }

            return false

        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("检查精灵排除状态时发生错误: ${e.message}")
            }
            // 如果检查失败，保守处理：不排除（允许清理）
            return false
        }
    }

    /**
     * 检查是否为幻之精灵（基于种类名称）
     */
    private fun isMythicalPokemonByName(speciesName: String): Boolean {
        try {
            val mythicalNames = listOf(
                "mew", "celebi", "jirachi", "deoxys", "phione", "manaphy",
                "darkrai", "shaymin", "arceus", "victini", "keldeo",
                "meloetta", "genesect", "diancie", "hoopa", "volcanion",
                "magearna", "marshadow", "zeraora", "meltan", "melmetal",
                "zarude", "calyrex"
            )

            return mythicalNames.any { speciesName.contains(it) }
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 通过反射获取精灵种类名称
     */
    private fun getPokemonSpecies(entity: Entity): String {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("开始获取精灵种类:")
                plugin.logger.info("  实体类: ${entity.javaClass.name}")
                plugin.logger.info("  实体类型: ${entity.type.name}")
            }

            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                if (configManager.enableLogging) {
                    plugin.logger.info("  NMS实体类: ${nmsEntity.javaClass.name}")
                    plugin.logger.info("  NMS实体字段: ${nmsEntity.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.joinToString(", ")}")
                }

                // 在NMS实体中查找Pokemon字段
                val pokemonField = nmsEntity.javaClass.declaredFields.find {
                    it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                    !it.type.simpleName.contains("Entity", ignoreCase = true)
                }

                if (pokemonField != null) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("  找到Pokemon字段: ${pokemonField.name}, 类型: ${pokemonField.type.simpleName}")
                    }

                    pokemonField.isAccessible = true
                    val pokemon = pokemonField.get(nmsEntity)

                    if (pokemon != null) {
                        return extractSpeciesFromPokemon(pokemon)
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  无法通过getHandle()获取NMS实体: ${e.message}")
                }
            }

            // 方法2: 直接在Bukkit实体中查找Pokemon字段
            val pokemonField = entity.javaClass.declaredFields.find {
                it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                !it.type.simpleName.contains("Entity", ignoreCase = true)
            }

            if (pokemonField != null) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  在Bukkit实体中找到Pokemon字段: ${pokemonField.name}")
                }

                pokemonField.isAccessible = true
                val pokemon = pokemonField.get(entity)

                if (pokemon != null) {
                    return extractSpeciesFromPokemon(pokemon)
                }
            }

            // 如果反射失败，返回实体类型名称作为备用
            if (configManager.enableLogging) {
                plugin.logger.info("  无法获取精灵种类，使用实体类型名称")
            }
            return entity.type.name
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("获取精灵种类时发生错误: ${e.message}")
            }
            return entity.type.name
        }
    }

    /**
     * 从Pokemon对象中提取种类信息
     */
    private fun extractSpeciesFromPokemon(pokemon: Any): String {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("  Pokemon对象类: ${pokemon.javaClass.name}")
                plugin.logger.info("  Pokemon对象字段: ${pokemon.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.joinToString(", ")}")
            }

            // 获取species字段
            val speciesField = pokemon.javaClass.declaredFields.find {
                it.name.equals("species", ignoreCase = true)
            }

            if (speciesField != null) {
                speciesField.isAccessible = true
                val species = speciesField.get(pokemon)

                if (species != null) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("  Species对象类: ${species.javaClass.name}")
                        plugin.logger.info("  Species对象字段: ${species.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.joinToString(", ")}")
                    }

                    // 获取resourceIdentifier
                    val resourceIdField = species.javaClass.declaredFields.find {
                        it.name.contains("resourceIdentifier", ignoreCase = true) ||
                        it.name.contains("identifier", ignoreCase = true) ||
                        it.name.contains("name", ignoreCase = true)
                    }

                    if (resourceIdField != null) {
                        resourceIdField.isAccessible = true
                        val resourceId = resourceIdField.get(species)
                        val result = resourceId?.toString() ?: "unknown"

                        if (configManager.enableLogging) {
                            plugin.logger.info("  获取到种类: $result")
                        }
                        return result
                    }
                }
            }

            return "unknown"
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("从Pokemon对象提取种类时发生错误: ${e.message}")
            }
            return "unknown"
        }
    }

    /**
     * 通过反射检查是否为闪光精灵
     */
    private fun isPokemonShiny(entity: Entity): Boolean {
        try {
            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                val pokemonField = nmsEntity.javaClass.declaredFields.find {
                    it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                    !it.type.simpleName.contains("Entity", ignoreCase = true)
                }

                if (pokemonField != null) {
                    pokemonField.isAccessible = true
                    val pokemon = pokemonField.get(nmsEntity)

                    if (pokemon != null) {
                        val shinyField = pokemon.javaClass.declaredFields.find {
                            it.name.equals("shiny", ignoreCase = true)
                        }

                        if (shinyField != null) {
                            shinyField.isAccessible = true
                            val isShiny = shinyField.get(pokemon)
                            return isShiny as? Boolean ?: false
                        }
                    }
                }
            } catch (e: Exception) {
                // NMS方法失败，尝试直接访问
            }

            // 方法2: 直接在Bukkit实体中查找
            val pokemonField = entity.javaClass.declaredFields.find {
                it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                !it.type.simpleName.contains("Entity", ignoreCase = true)
            }

            if (pokemonField != null) {
                pokemonField.isAccessible = true
                val pokemon = pokemonField.get(entity)

                if (pokemon != null) {
                    val shinyField = pokemon.javaClass.declaredFields.find {
                        it.name.equals("shiny", ignoreCase = true)
                    }

                    if (shinyField != null) {
                        shinyField.isAccessible = true
                        val isShiny = shinyField.get(pokemon)
                        return isShiny as? Boolean ?: false
                    }
                }
            }

            return false
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 通过反射检查是否为传说精灵
     */
    private fun isPokemonLegendary(entity: Entity): Boolean {
        try {
            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                val pokemonField = nmsEntity.javaClass.declaredFields.find {
                    it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                    !it.type.simpleName.contains("Entity", ignoreCase = true)
                }

                if (pokemonField != null) {
                    pokemonField.isAccessible = true
                    val pokemon = pokemonField.get(nmsEntity)

                    if (pokemon != null) {
                        return checkPokemonLegendary(pokemon)
                    }
                }
            } catch (e: Exception) {
                // NMS方法失败，尝试直接访问
            }

            // 方法2: 直接在Bukkit实体中查找
            val pokemonField = entity.javaClass.declaredFields.find {
                it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                !it.type.simpleName.contains("Entity", ignoreCase = true)
            }

            if (pokemonField != null) {
                pokemonField.isAccessible = true
                val pokemon = pokemonField.get(entity)

                if (pokemon != null) {
                    return checkPokemonLegendary(pokemon)
                }
            }

            return false
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 检查Pokemon对象是否为传说精灵
     */
    private fun checkPokemonLegendary(pokemon: Any): Boolean {
        try {
            // 尝试调用isLegendary方法
            try {
                val isLegendaryMethod = pokemon.javaClass.getMethod("isLegendary")
                val isLegendary = isLegendaryMethod.invoke(pokemon)
                return isLegendary as? Boolean ?: false
            } catch (e: Exception) {
                // 方法不存在，尝试字段
                val legendaryField = pokemon.javaClass.declaredFields.find {
                    it.name.contains("legendary", ignoreCase = true)
                }

                if (legendaryField != null) {
                    legendaryField.isAccessible = true
                    val isLegendary = legendaryField.get(pokemon)
                    return isLegendary as? Boolean ?: false
                }
            }

            return false
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 通过反射检查是否被玩家拥有
     */
    private fun isPokemonPlayerOwned(entity: Entity): Boolean {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("开始检查精灵拥有状态:")
                plugin.logger.info("  实体类: ${entity.javaClass.name}")
            }

            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                if (configManager.enableLogging) {
                    plugin.logger.info("  NMS实体类: ${nmsEntity.javaClass.name}")
                }

                // 在NMS实体中查找Pokemon字段
                val pokemonField = nmsEntity.javaClass.declaredFields.find {
                    it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                    !it.type.simpleName.contains("Entity", ignoreCase = true)
                }

                if (pokemonField != null) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("  在NMS实体中找到Pokemon字段: ${pokemonField.name}")
                    }

                    pokemonField.isAccessible = true
                    val pokemon = pokemonField.get(nmsEntity)

                    if (pokemon != null) {
                        return checkPokemonOwnership(pokemon)
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  无法通过getHandle()获取NMS实体: ${e.message}")
                }
            }

            // 方法2: 直接在Bukkit实体中查找Pokemon字段
            val pokemonField = entity.javaClass.declaredFields.find {
                it.type.simpleName.contains("Pokemon", ignoreCase = true) &&
                !it.type.simpleName.contains("Entity", ignoreCase = true)
            }

            if (pokemonField != null) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  在Bukkit实体中找到Pokemon字段: ${pokemonField.name}")
                }

                pokemonField.isAccessible = true
                val pokemon = pokemonField.get(entity)

                if (pokemon != null) {
                    return checkPokemonOwnership(pokemon)
                }
            } else {
                if (configManager.enableLogging) {
                    plugin.logger.info("  未找到Pokemon字段")
                    plugin.logger.info("  实体可用字段: ${entity.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.joinToString(", ")}")
                }
            }

            if (configManager.enableLogging) {
                plugin.logger.info("  无法检查拥有状态，默认为野生")
            }
            return false // 无法确定时，假设是野生的（可以清理）
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("检查精灵拥有状态时发生错误: ${e.message}")
            }
            return false // 出错时，假设是野生的（可以清理）
        }
    }

    /**
     * 检查Pokemon对象的拥有状态
     */
    private fun checkPokemonOwnership(pokemon: Any): Boolean {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("  Pokemon对象类: ${pokemon.javaClass.name}")
                plugin.logger.info("  Pokemon对象可用方法: ${pokemon.javaClass.methods.map { it.name }.take(10).joinToString(", ")}...")
            }

            // 尝试调用isPlayerOwned方法
            try {
                val isPlayerOwnedMethod = pokemon.javaClass.getMethod("isPlayerOwned")
                val isPlayerOwned = isPlayerOwnedMethod.invoke(pokemon)
                val result = isPlayerOwned as? Boolean ?: false
                if (configManager.enableLogging) {
                    plugin.logger.info("  通过isPlayerOwned方法检查: $result")
                }
                return result
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  isPlayerOwned方法不存在: ${e.message}")
                }

                // 方法不存在，尝试字段
                val ownerField = pokemon.javaClass.declaredFields.find {
                    it.name.contains("owner", ignoreCase = true) ||
                    it.name.contains("player", ignoreCase = true)
                }

                if (ownerField != null) {
                    ownerField.isAccessible = true
                    val owner = ownerField.get(pokemon)
                    val result = owner != null
                    if (configManager.enableLogging) {
                        plugin.logger.info("  通过owner字段检查: $result (字段: ${ownerField.name})")
                    }
                    return result
                } else {
                    if (configManager.enableLogging) {
                        plugin.logger.info("  Pokemon对象可用字段: ${pokemon.javaClass.declaredFields.map { it.name }.take(10).joinToString(", ")}...")
                    }
                }
            }

            return false
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("检查Pokemon拥有状态时发生错误: ${e.message}")
            }
            return false
        }
    }

    /**
     * 通过反射检查是否在战斗中
     */
    private fun isPokemonBattling(entity: Entity): Boolean {
        try {
            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                return checkEntityBattling(nmsEntity)
            } catch (e: Exception) {
                // NMS方法失败，尝试直接访问
            }

            // 方法2: 直接检查Bukkit实体
            return checkEntityBattling(entity)
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 检查实体是否在战斗中
     */
    private fun checkEntityBattling(entity: Any): Boolean {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("  检查战斗状态，实体类: ${entity.javaClass.name}")
                plugin.logger.info("  实体字段: ${entity.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.take(20).joinToString(", ")}...")
            }

            // 方法1: 尝试获取isBattling字段
            try {
                val isBattlingField = entity.javaClass.declaredFields.find {
                    it.name.equals("isBattling", ignoreCase = true)
                }

                if (isBattlingField != null) {
                    isBattlingField.isAccessible = true
                    val isBattling = isBattlingField.get(entity)
                    val result = isBattling as? Boolean ?: false
                    if (configManager.enableLogging) {
                        plugin.logger.info("  通过isBattling字段检查: $result")
                    }
                    return result
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  isBattling字段检查失败: ${e.message}")
                }
            }

            // 方法2: 尝试调用isBattling方法
            try {
                val isBattlingMethod = entity.javaClass.getMethod("isBattling")
                val isBattling = isBattlingMethod.invoke(entity)
                val result = isBattling as? Boolean ?: false
                if (configManager.enableLogging) {
                    plugin.logger.info("  通过isBattling方法检查: $result")
                }
                return result
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  isBattling方法检查失败: ${e.message}")
                }
            }

            // 方法3: 检查BATTLE_ID字段（从日志中看到的）
            try {
                val battleIdField = entity.javaClass.declaredFields.find {
                    it.name.contains("BATTLE_ID", ignoreCase = true)
                }

                if (battleIdField != null) {
                    battleIdField.isAccessible = true
                    val battleId = battleIdField.get(entity)
                    if (configManager.enableLogging) {
                        plugin.logger.info("  BATTLE_ID字段值: $battleId")
                    }
                    // 如果有战斗ID，可能表示在战斗中
                    if (battleId != null) {
                        // 需要进一步检查battleId的值
                        val battleIdStr = battleId.toString()
                        val result = battleIdStr.isNotEmpty() && !battleIdStr.equals("null", ignoreCase = true)
                        if (configManager.enableLogging) {
                            plugin.logger.info("  通过BATTLE_ID检查: $result (值: $battleIdStr)")
                        }
                        return result
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  BATTLE_ID字段检查失败: ${e.message}")
                }
            }

            // 方法4: 检查battle相关字段
            try {
                val battleFields = entity.javaClass.declaredFields.filter {
                    it.name.contains("battle", ignoreCase = true)
                }

                if (configManager.enableLogging) {
                    plugin.logger.info("  找到的battle相关字段: ${battleFields.map { it.name }.joinToString(", ")}")
                }

                for (field in battleFields) {
                    try {
                        field.isAccessible = true
                        val value = field.get(entity)
                        if (configManager.enableLogging) {
                            plugin.logger.info("  字段 ${field.name} 值: $value")
                        }

                        // 如果字段值不为null且不为空，可能表示在战斗中
                        if (value != null) {
                            val valueStr = value.toString()
                            if (valueStr.isNotEmpty() && !valueStr.equals("null", ignoreCase = true)) {
                                if (configManager.enableLogging) {
                                    plugin.logger.info("  通过${field.name}字段检测到战斗状态")
                                }
                                return true
                            }
                        }
                    } catch (e: Exception) {
                        // 忽略单个字段的错误
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  battle字段检查失败: ${e.message}")
                }
            }

            if (configManager.enableLogging) {
                plugin.logger.info("  所有战斗状态检查都失败，返回false")
            }
            return false
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("检查战斗状态时发生错误: ${e.message}")
            }
            return false
        }
    }

    /**
     * 通过反射检查是否繁忙
     */
    private fun isPokemonBusy(entity: Entity): Boolean {
        try {
            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                return checkEntityBusy(nmsEntity)
            } catch (e: Exception) {
                // NMS方法失败，尝试直接访问
            }

            // 方法2: 直接检查Bukkit实体
            return checkEntityBusy(entity)
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 检查实体是否繁忙
     */
    private fun checkEntityBusy(entity: Any): Boolean {
        try {
            if (configManager.enableLogging) {
                plugin.logger.info("  检查繁忙状态，实体类: ${entity.javaClass.name}")
                plugin.logger.info("  实体字段: ${entity.javaClass.declaredFields.map { "${it.name}:${it.type.simpleName}" }.take(20).joinToString(", ")}...")
            }

            // 方法1: 尝试获取isBusy字段
            try {
                val isBusyField = entity.javaClass.declaredFields.find {
                    it.name.equals("isBusy", ignoreCase = true)
                }

                if (isBusyField != null) {
                    isBusyField.isAccessible = true
                    val isBusy = isBusyField.get(entity)
                    val result = isBusy as? Boolean ?: false
                    if (configManager.enableLogging) {
                        plugin.logger.info("  通过isBusy字段检查: $result")
                    }
                    return result
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  isBusy字段检查失败: ${e.message}")
                }
            }

            // 方法2: 尝试调用isBusy方法
            try {
                val isBusyMethod = entity.javaClass.getMethod("isBusy")
                val isBusy = isBusyMethod.invoke(entity)
                val result = isBusy as? Boolean ?: false
                if (configManager.enableLogging) {
                    plugin.logger.info("  通过isBusy方法检查: $result")
                }
                return result
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  isBusy方法检查失败: ${e.message}")
                }
            }

            // 方法3: 检查busyLocks字段（从日志中看到的）
            try {
                val busyLocksField = entity.javaClass.declaredFields.find {
                    it.name.contains("busyLocks", ignoreCase = true)
                }

                if (busyLocksField != null) {
                    busyLocksField.isAccessible = true
                    val busyLocks = busyLocksField.get(entity)
                    if (configManager.enableLogging) {
                        plugin.logger.info("  busyLocks字段值: $busyLocks")
                    }
                    // 如果busyLocks不为空且有内容，表示繁忙
                    if (busyLocks != null) {
                        val result = when (busyLocks) {
                            is Collection<*> -> busyLocks.isNotEmpty()
                            is Array<*> -> busyLocks.isNotEmpty()
                            else -> true // 如果不是集合类型但不为null，也认为是繁忙
                        }
                        if (configManager.enableLogging) {
                            plugin.logger.info("  通过busyLocks检查: $result")
                        }
                        if (result) return true
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  busyLocks字段检查失败: ${e.message}")
                }
            }

            // 方法4: 检查EVOLUTION_STARTED字段（进化状态）
            try {
                val evolutionStartedField = entity.javaClass.declaredFields.find {
                    it.name.contains("EVOLUTION_STARTED", ignoreCase = true)
                }

                if (evolutionStartedField != null) {
                    evolutionStartedField.isAccessible = true
                    val evolutionStarted = evolutionStartedField.get(entity)
                    if (configManager.enableLogging) {
                        plugin.logger.info("  EVOLUTION_STARTED字段值: $evolutionStarted")
                    }
                    // 如果进化已开始，表示繁忙
                    if (evolutionStarted != null) {
                        val result = when (evolutionStarted) {
                            is Boolean -> evolutionStarted
                            else -> {
                                val valueStr = evolutionStarted.toString()
                                valueStr.isNotEmpty() && !valueStr.equals("null", ignoreCase = true) && !valueStr.equals("false", ignoreCase = true)
                            }
                        }
                        if (configManager.enableLogging) {
                            plugin.logger.info("  通过EVOLUTION_STARTED检查: $result")
                        }
                        if (result) return true
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  EVOLUTION_STARTED字段检查失败: ${e.message}")
                }
            }

            // 方法5: 检查evolutionEntity字段（进化实体）
            try {
                val evolutionEntityField = entity.javaClass.declaredFields.find {
                    it.name.contains("evolutionEntity", ignoreCase = true)
                }

                if (evolutionEntityField != null) {
                    evolutionEntityField.isAccessible = true
                    val evolutionEntity = evolutionEntityField.get(entity)
                    if (configManager.enableLogging) {
                        plugin.logger.info("  evolutionEntity字段值: $evolutionEntity")
                    }
                    // 如果有进化实体，表示正在进化（繁忙）
                    if (evolutionEntity != null) {
                        if (configManager.enableLogging) {
                            plugin.logger.info("  通过evolutionEntity检测到进化状态（繁忙）")
                        }
                        return true
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  evolutionEntity字段检查失败: ${e.message}")
                }
            }

            // 方法6: 检查busy相关字段
            try {
                val busyFields = entity.javaClass.declaredFields.filter {
                    it.name.contains("busy", ignoreCase = true)
                }

                if (configManager.enableLogging) {
                    plugin.logger.info("  找到的busy相关字段: ${busyFields.map { it.name }.joinToString(", ")}")
                }

                for (field in busyFields) {
                    try {
                        field.isAccessible = true
                        val value = field.get(entity)
                        if (configManager.enableLogging) {
                            plugin.logger.info("  字段 ${field.name} 值: $value")
                        }

                        // 根据字段类型判断繁忙状态
                        if (value != null) {
                            val result = when (value) {
                                is Boolean -> value
                                is Collection<*> -> value.isNotEmpty()
                                is Array<*> -> value.isNotEmpty()
                                else -> {
                                    val valueStr = value.toString()
                                    valueStr.isNotEmpty() && !valueStr.equals("null", ignoreCase = true)
                                }
                            }
                            if (result) {
                                if (configManager.enableLogging) {
                                    plugin.logger.info("  通过${field.name}字段检测到繁忙状态")
                                }
                                return true
                            }
                        }
                    } catch (e: Exception) {
                        // 忽略单个字段的错误
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  busy字段检查失败: ${e.message}")
                }
            }

            // 方法7: 检查evolution相关字段
            try {
                val evolutionFields = entity.javaClass.declaredFields.filter {
                    it.name.contains("evolution", ignoreCase = true)
                }

                if (configManager.enableLogging) {
                    plugin.logger.info("  找到的evolution相关字段: ${evolutionFields.map { it.name }.joinToString(", ")}")
                }

                for (field in evolutionFields) {
                    try {
                        field.isAccessible = true
                        val value = field.get(entity)
                        if (configManager.enableLogging) {
                            plugin.logger.info("  字段 ${field.name} 值: $value")
                        }

                        // 如果有进化相关的非空值，可能表示正在进化
                        if (value != null) {
                            val result = when (value) {
                                is Boolean -> value
                                else -> {
                                    val valueStr = value.toString()
                                    valueStr.isNotEmpty() && !valueStr.equals("null", ignoreCase = true) && !valueStr.equals("false", ignoreCase = true)
                                }
                            }
                            if (result) {
                                if (configManager.enableLogging) {
                                    plugin.logger.info("  通过${field.name}字段检测到进化状态（繁忙）")
                                }
                                return true
                            }
                        }
                    } catch (e: Exception) {
                        // 忽略单个字段的错误
                    }
                }
            } catch (e: Exception) {
                if (configManager.enableLogging) {
                    plugin.logger.info("  evolution字段检查失败: ${e.message}")
                }
            }

            if (configManager.enableLogging) {
                plugin.logger.info("  所有繁忙状态检查都失败，返回false")
            }
            return false
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("检查繁忙状态时发生错误: ${e.message}")
            }
            return false
        }
    }

    /**
     * 通过反射检查是否不可捕获
     */
    private fun isPokemonUncatchable(entity: Entity): Boolean {
        try {
            // 方法1: 尝试通过getHandle()获取NMS实体
            try {
                val getHandleMethod = entity.javaClass.getMethod("getHandle")
                val nmsEntity = getHandleMethod.invoke(entity)

                return checkEntityUncatchable(nmsEntity)
            } catch (e: Exception) {
                // NMS方法失败，尝试直接访问
            }

            // 方法2: 直接检查Bukkit实体
            return checkEntityUncatchable(entity)
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 检查实体是否不可捕获
     */
    private fun checkEntityUncatchable(entity: Any): Boolean {
        try {
            // 尝试获取isUncatchable方法
            try {
                val isUncatchableMethod = entity.javaClass.getMethod("isUncatchable")
                val isUncatchable = isUncatchableMethod.invoke(entity)
                return isUncatchable as? Boolean ?: false
            } catch (e: Exception) {
                // 方法不存在，尝试字段
                val uncatchableField = entity.javaClass.declaredFields.find {
                    it.name.contains("uncatchable", ignoreCase = true)
                }

                if (uncatchableField != null) {
                    uncatchableField.isAccessible = true
                    val isUncatchable = uncatchableField.get(entity)
                    return isUncatchable as? Boolean ?: false
                }
            }

            return false
        } catch (e: Exception) {
            return false
        }
    }



    /**
     * 获取精灵实体信息（用于日志）- 使用反射避免混淆问题
     */
    private fun getPokemonEntityInfo(entity: Entity): String {
        try {
            if (isPokemonEntity(entity)) {
                val speciesName = getPokemonSpecies(entity)
                val customName = entity.customName

                val tags = mutableListOf<String>()
                if (isPokemonShiny(entity)) tags.add("闪光")
                if (isPokemonLegendary(entity)) tags.add("传说")
                if (isMythicalPokemonByName(speciesName)) tags.add("幻之")
                if (isPokemonPlayerOwned(entity)) tags.add("已拥有")
                if (isPokemonBattling(entity)) tags.add("战斗中")
                if (isPokemonBusy(entity)) tags.add("繁忙")

                val tagStr = if (tags.isNotEmpty()) " [${tags.joinToString(", ")}]" else ""
                val nameStr = when {
                    customName != null -> "$customName ($speciesName)"
                    else -> speciesName
                }

                return "$nameStr$tagStr"
            } else {
                return entity.name ?: entity.type.name
            }
        } catch (e: Exception) {
            return entity.name ?: entity.type.name
        }
    }



    /**
     * 检查实体是否在玩家附近
     */
    private fun isNearPlayer(entity: Entity): Boolean {
        try {
            // 获取实体位置
            val entityLoc = entity.location

            // 获取所有在线玩家
            for (player in Bukkit.getOnlinePlayers()) {
                val playerLoc = player.location

                // 检查是否在同一世界
                if (entityLoc.world?.name != playerLoc.world?.name) {
                    continue
                }

                // 计算距离
                val distance = entityLoc.distance(playerLoc)

                if (distance <= configManager.maxDistance) {
                    return true
                }
            }

            return false

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "检查玩家距离时发生错误", e)
            return true // 出错时保守处理，不清理
        }
    }

    /**
     * 检查掉落物是否在玩家附近
     */
    private fun isNearPlayerForItem(entity: Entity): Boolean {
        try {
            // 获取实体位置
            val entityLoc = entity.location

            // 获取所有在线玩家
            for (player in Bukkit.getOnlinePlayers()) {
                val playerLoc = player.location

                // 检查是否在同一世界
                if (entityLoc.world?.name != playerLoc.world?.name) {
                    continue
                }

                // 计算距离
                val distance = entityLoc.distance(playerLoc)

                if (distance <= configManager.itemMaxDistance) {
                    return true
                }
            }

            return false

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "检查掉落物玩家距离时发生错误", e)
            return true // 出错时保守处理，不清理
        }
    }

    /**
     * 检查是否为原版生物实体
     */
    private fun isVanillaMobEntity(entity: Entity): Boolean {
        return try {
            // 排除玩家、精灵、NPC等特殊实体
            !isPokemonEntity(entity) &&
            !entity.javaClass.simpleName.contains("Player") &&
            !entity.javaClass.simpleName.contains("NPC") &&
            entity.type.isAlive &&
            entity.type.name.lowercase() != "armor_stand" &&
            entity.type.name.lowercase() != "item_frame" &&
            entity.type.name.lowercase() != "painting"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否为掉落物实体
     */
    private fun isItemEntity(entity: Entity): Boolean {
        return try {
            entity.type.name.lowercase() == "dropped_item" ||
            entity.type.name.lowercase() == "item" ||
            entity.javaClass.simpleName.contains("Item", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 判断是否应该清理指定的原版生物实体
     */
    private fun shouldCleanVanillaMobEntity(entity: Entity, currentTime: Long): Boolean {
        try {
            // 基本检查
            if (entity.isDead) {
                return false
            }

            // 检查是否在排除列表中
            val entityName = try {
                entity.name?.lowercase() ?: entity.type.name.lowercase()
            } catch (e: Exception) {
                "unknown"
            }

            if (configManager.isVanillaMobExcluded(entityName)) {
                return false
            }

            // 检查是否有自定义名称（命名牌保护）
            if (configManager.protectNamedVanillaMobs) {
                try {
                    if (entity.customName != null) {
                        if (configManager.enableLogging) {
                            val customName = entity.customName
                            val displayName = if (customName.isNullOrEmpty()) {
                                "${entity.type.name}（空命名牌）"
                            } else {
                                "$customName (${entity.type.name})"
                            }
                            plugin.logger.info("原版生物有命名牌，不清理: $displayName")
                        }
                        return false
                    }
                } catch (e: Exception) {
                    // 如果无法检查，保守处理
                    if (configManager.enableLogging) {
                        plugin.logger.warning("检查原版生物命名牌时发生错误: ${e.message}")
                    }
                    return false
                }
            }

            // 检查实体年龄
            val entityId = entity.uniqueId.toString()
            val spawnTime = vanillaMobSpawnTimes[entityId]
            if (spawnTime != null) {
                val ageSeconds = (currentTime - spawnTime) / 1000
                if (ageSeconds < configManager.vanillaMobMinAge) {
                    return false
                }
            } else {
                // 如果没有记录生成时间，使用实体存在时间
                val ageSeconds = entity.ticksLived / 20
                if (ageSeconds < configManager.vanillaMobMinAge) {
                    return false
                }
            }

            // 检查是否在玩家附近
            if (isNearPlayer(entity)) {
                return false
            }

            return true

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "检查原版生物清理条件时发生错误", e)
            return false
        }
    }

    /**
     * 判断是否应该清理指定的掉落物实体
     */
    private fun shouldCleanItemEntity(entity: Entity, currentTime: Long): Boolean {
        try {
            // 基本检查
            if (entity.isDead) {
                return false
            }

            // 检查实体年龄
            val ageSeconds = entity.ticksLived / 20
            if (ageSeconds < configManager.itemMinAge) {
                return false
            }

            // 检查是否在玩家附近
            if (isNearPlayerForItem(entity)) {
                return false
            }

            // 获取物品信息进行进一步检查
            val itemInfo = getItemEntityInfo(entity)

            if (configManager.enableLogging) {
                plugin.logger.info("检查掉落物: $itemInfo")
                plugin.logger.info("  配置: cleanNamedItems=${configManager.cleanNamedItems}, cleanEnchantedItems=${configManager.cleanEnchantedItems}")
                plugin.logger.info("  物品状态: hasCustomName=${hasCustomName(entity)}, isEnchanted=${isEnchantedItem(entity)}")
            }

            // 检查是否在排除列表中
            if (configManager.isItemExcluded(itemInfo)) {
                if (configManager.enableLogging) {
                    plugin.logger.info("掉落物 $itemInfo 被排除，不清理")
                }
                return false
            }

            // 检查是否有自定义名称
            if (!configManager.cleanNamedItems) {
                if (hasCustomName(entity)) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("掉落物有自定义名称且设置不清理命名物品，跳过清理")
                    }
                    return false
                }
            }

            // 检查是否为附魔物品
            if (!configManager.cleanEnchantedItems) {
                if (isEnchantedItem(entity)) {
                    if (configManager.enableLogging) {
                        plugin.logger.info("掉落物有附魔且设置不清理附魔物品，跳过清理")
                    }
                    return false
                }
            }

            // 检查稀有度保护
            if (configManager.itemRarityProtectionEnabled) {
                if (isProtectedByRarity(entity)) {
                    return false
                }
            }

            return true

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "检查掉落物清理条件时发生错误", e)
            return false
        }
    }

    /**
     * 获取掉落物实体信息
     */
    private fun getItemEntityInfo(entity: Entity): String {
        return try {
            // 方法1: 尝试通过Bukkit API获取ItemStack
            if (entity is org.bukkit.entity.Item) {
                val itemStack = entity.itemStack
                return getDetailedItemInfo(itemStack)
            }

            // 方法2: 通过反射获取物品堆栈信息
            val itemField = entity.javaClass.declaredFields.find {
                it.name.contains("item", ignoreCase = true) ||
                it.name.contains("stack", ignoreCase = true)
            }

            if (itemField != null) {
                itemField.isAccessible = true
                val itemStack = itemField.get(entity)

                if (itemStack != null) {
                    // 如果是Bukkit ItemStack，直接获取详细信息
                    if (itemStack is org.bukkit.inventory.ItemStack) {
                        return getDetailedItemInfo(itemStack)
                    }

                    // 尝试获取物品类型
                    val typeField = itemStack.javaClass.declaredFields.find {
                        it.name.contains("type", ignoreCase = true) ||
                        it.name.contains("material", ignoreCase = true)
                    }

                    if (typeField != null) {
                        typeField.isAccessible = true
                        val type = typeField.get(itemStack)

                        // 尝试获取完整的物品ID
                        if (type != null) {
                            val typeStr = type.toString()

                            // 如果已经包含命名空间，直接返回
                            if (typeStr.contains(":")) {
                                return typeStr.lowercase()
                            }

                            // 否则尝试添加minecraft命名空间
                            return "minecraft:${typeStr.lowercase()}"
                        }
                    }
                }
            }

            // 如果无法获取详细信息，返回实体类型
            entity.type.name.lowercase()
        } catch (e: Exception) {
            if (configManager.enableLogging) {
                plugin.logger.warning("获取掉落物信息时发生错误: ${e.message}")
            }
            "unknown_item"
        }
    }

    /**
     * 获取详细的物品信息（包括NBT）
     */
    private fun getDetailedItemInfo(itemStack: org.bukkit.inventory.ItemStack): String {
        return try {
            val material = itemStack.type
            val key = material.key
            val baseId = "${key.namespace}:${key.key}"

            // 获取物品的详细信息
            val details = mutableListOf<String>()
            details.add(baseId)

            // 添加自定义名称
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                val displayName = itemStack.itemMeta?.displayName?.replace("§", "")?.replace(" ", "_")
                if (!displayName.isNullOrEmpty()) {
                    details.add("name:$displayName")
                }
            }

            // 添加Lore信息
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasLore() == true) {
                val lore = itemStack.itemMeta?.lore?.joinToString("|") { it.replace("§", "").replace(" ", "_") }
                if (!lore.isNullOrEmpty()) {
                    details.add("lore:$lore")
                }
            }

            // 添加附魔信息
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasEnchants() == true) {
                val enchants = itemStack.itemMeta?.enchants?.map { "${it.key.key}:${it.value}" }?.joinToString("|")
                if (!enchants.isNullOrEmpty()) {
                    details.add("enchants:$enchants")
                }
            }

            // 添加数量信息（如果不是1）
            if (itemStack.amount != 1) {
                details.add("amount:${itemStack.amount}")
            }

            // 尝试获取NBT数据
            try {
                val nbtString = getNBTString(itemStack)
                if (nbtString.isNotEmpty()) {
                    details.add("nbt:$nbtString")
                }
            } catch (e: Exception) {
                // NBT获取失败，忽略
            }

            return details.joinToString(";")
        } catch (e: Exception) {
            // 如果失败，使用简单的材料名称
            "minecraft:${itemStack.type.name.lowercase()}"
        }
    }

    /**
     * 获取ItemStack的NBT字符串
     */
    private fun getNBTString(itemStack: org.bukkit.inventory.ItemStack): String {
        return try {
            // 尝试通过NMS获取NBT数据
            val craftItemStackClass = Class.forName("org.bukkit.craftbukkit.v1_21_R1.inventory.CraftItemStack")
            val asNMSCopyMethod = craftItemStackClass.getMethod("asNMSCopy", org.bukkit.inventory.ItemStack::class.java)
            val nmsItemStack = asNMSCopyMethod.invoke(null, itemStack)

            if (nmsItemStack != null) {
                // 获取NBT标签
                val getTagMethod = nmsItemStack.javaClass.getMethod("getTag")
                val nbtTag = getTagMethod.invoke(nmsItemStack)

                if (nbtTag != null) {
                    // 转换为字符串并简化
                    val nbtString = nbtTag.toString()
                    // 只保留关键的NBT信息，去除过长的数据
                    return simplifyNBT(nbtString)
                }
            }

            ""
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 简化NBT字符串，只保留关键信息
     */
    private fun simplifyNBT(nbtString: String): String {
        return try {
            // 移除过长的数据，只保留关键标识
            var simplified = nbtString
                .replace("\\s+".toRegex(), "")
                .replace("\"", "")
                .take(200) // 限制长度

            // 提取关键字段
            val keyFields = listOf("id", "Count", "tag", "display", "Name", "Lore", "Enchantments")
            val extractedFields = mutableListOf<String>()

            keyFields.forEach { field ->
                if (simplified.contains(field, ignoreCase = true)) {
                    extractedFields.add(field)
                }
            }

            if (extractedFields.isNotEmpty()) {
                return extractedFields.joinToString(",")
            }

            simplified
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 从ItemStack获取物品ID（简化版本）
     */
    private fun getItemIdFromItemStack(itemStack: org.bukkit.inventory.ItemStack): String {
        return try {
            // 获取完整的物品ID（包括命名空间）
            val material = itemStack.type
            val key = material.key
            "${key.namespace}:${key.key}"
        } catch (e: Exception) {
            // 如果失败，使用简单的材料名称
            "minecraft:${itemStack.type.name.lowercase()}"
        }
    }

    /**
     * 检查掉落物是否有自定义名称
     */
    private fun hasCustomName(entity: Entity): Boolean {
        return try {
            // 方法1: 检查实体的自定义名称
            if (entity.customName != null) {
                return true
            }

            // 方法2: 如果是Item实体，检查ItemStack的自定义名称
            if (entity is org.bukkit.entity.Item) {
                val itemStack = entity.itemStack
                if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                    return true
                }
            }

            // 方法3: 通过反射检查ItemStack的自定义名称
            val itemField = entity.javaClass.declaredFields.find {
                it.name.contains("item", ignoreCase = true) ||
                it.name.contains("stack", ignoreCase = true)
            }

            if (itemField != null) {
                itemField.isAccessible = true
                val itemStack = itemField.get(entity)

                if (itemStack is org.bukkit.inventory.ItemStack) {
                    if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                        return true
                    }
                }
            }

            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否为附魔物品
     */
    private fun isEnchantedItem(entity: Entity): Boolean {
        return try {
            // 方法1: 如果是Item实体，直接检查ItemStack
            if (entity is org.bukkit.entity.Item) {
                val itemStack = entity.itemStack
                if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasEnchants() == true) {
                    return itemStack.itemMeta?.enchants?.isNotEmpty() == true
                }
            }

            // 方法2: 通过反射获取物品堆栈
            val itemField = entity.javaClass.declaredFields.find {
                it.name.contains("item", ignoreCase = true) ||
                it.name.contains("stack", ignoreCase = true)
            }

            if (itemField != null) {
                itemField.isAccessible = true
                val itemStack = itemField.get(entity)

                if (itemStack is org.bukkit.inventory.ItemStack) {
                    if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasEnchants() == true) {
                        return itemStack.itemMeta?.enchants?.isNotEmpty() == true
                    }
                } else if (itemStack != null) {
                    // 检查是否有附魔
                    val enchantmentsField = itemStack.javaClass.declaredFields.find {
                        it.name.contains("enchant", ignoreCase = true)
                    }

                    if (enchantmentsField != null) {
                        enchantmentsField.isAccessible = true
                        val enchantments = enchantmentsField.get(itemStack)

                        return when (enchantments) {
                            is Map<*, *> -> enchantments.isNotEmpty()
                            is Collection<*> -> enchantments.isNotEmpty()
                            else -> false
                        }
                    }
                }
            }

            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否受稀有度保护
     */
    private fun isProtectedByRarity(entity: Entity): Boolean {
        return try {
            val itemInfo = getItemEntityInfo(entity)

            // 检查是否为保护的稀有物品
            val protectedItems = listOf(
                "diamond", "emerald", "netherite", "elytra", "totem_of_undying",
                "enchanted_book", "nether_star", "dragon_egg", "beacon",
                "shulker_box", "end_crystal"
            )

            protectedItems.any { itemInfo.contains(it, ignoreCase = true) }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 发送清理公告
     */
    private fun sendCleanupAnnouncement(pokemonCount: Int, vanillaMobCount: Int, itemCount: Int, duration: Long) {
        try {
            val message = configManager.announcementMessage
                .replace("{pokemonCount}", pokemonCount.toString())
                .replace("{vanillaCount}", vanillaMobCount.toString())
                .replace("{itemCount}", itemCount.toString())
                .replace("{duration}", duration.toString())

            // 向所有在线玩家发送消息
            for (player in Bukkit.getOnlinePlayers()) {
                player.sendMessage(message)
            }

            // 同时在控制台输出
            plugin.logger.info("清理公告: 已清理 $pokemonCount 只精灵，$vanillaMobCount 只原版生物，$itemCount 个掉落物，用时 ${duration}ms")

        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "发送清理公告时发生错误", e)
        }
    }

    /**
     * 清理跟踪数据
     */
    fun cleanup() {
        pokemonSpawnTimes.clear()
        vanillaMobSpawnTimes.clear()
    }

    // 公共方法供调试使用
    fun isPokemonEntityPublic(entity: Entity): Boolean = isPokemonEntity(entity)
    fun isPokemonExcludedPublic(entity: Entity): Boolean = isPokemonExcluded(entity)
    fun getPokemonEntityInfoPublic(entity: Entity): String = getPokemonEntityInfo(entity)

    /**
     * 获取详细的精灵调试信息 - 使用反射避免混淆问题
     */
    fun getDetailedPokemonInfo(entity: Entity): String {
        try {
            val info = mutableListOf<String>()

            // 基本信息
            info.add("实体类型: ${entity.type.name}")
            info.add("实体类型Key: ${entity.type.key}")
            info.add("实体类名: ${entity.javaClass.simpleName}")
            info.add("实体完整类名: ${entity.javaClass.name}")
            info.add("实体名称: ${entity.name ?: "null"}")
            info.add("自定义名称: ${entity.customName ?: "null"}")
            info.add("是否为Cobblemon精灵: ${isPokemonEntity(entity)}")

            if (isPokemonEntity(entity)) {
                val speciesName = getPokemonSpecies(entity)

                // Cobblemon特定信息
                info.add("精灵种类: $speciesName")
                info.add("是否闪光: ${isPokemonShiny(entity)}")
                info.add("是否传说: ${isPokemonLegendary(entity)}")
                info.add("是否幻之: ${isMythicalPokemonByName(speciesName)}")
                info.add("是否被玩家拥有: ${isPokemonPlayerOwned(entity)}")
                info.add("是否在战斗: ${isPokemonBattling(entity)}")
                info.add("是否繁忙: ${isPokemonBusy(entity)}")
                info.add("是否可捕获: ${!isPokemonUncatchable(entity)}")

                // 排除检查
                info.add("是否被排除: ${isPokemonExcluded(entity)}")

                // 排除配置检查
                info.add("排除配置: ${configManager.excludedPokemon}")

                // 高级设置检查
                info.add("高级设置:")
                info.add("  清理被拥有精灵: ${configManager.cleanOwned}")
                info.add("  清理战斗中精灵: ${configManager.cleanBattling}")
                info.add("  清理繁忙精灵: ${configManager.cleanBusy}")
                info.add("  清理被骑乘精灵: ${configManager.cleanRiding}")
                info.add("  清理不可捕获精灵: ${configManager.cleanUncatchable}")

                // 详细排除检查
                info.add("排除检查详情:")
                configManager.excludedPokemon.forEach { excluded ->
                    when (excluded.lowercase()) {
                        "shiny" -> info.add("  闪光检查: ${isPokemonShiny(entity)}")
                        "legendary" -> info.add("  传说检查: ${isPokemonLegendary(entity)}")
                        "mythical" -> info.add("  幻之检查: ${isMythicalPokemonByName(speciesName)}")
                        else -> {
                            val speciesMatch = speciesName.contains(excluded, ignoreCase = true)
                            val nameMatch = (entity.name?.contains(excluded, ignoreCase = true) ?: false)
                            info.add("  '$excluded' 匹配: 种类=$speciesMatch, 名称=$nameMatch")
                        }
                    }
                }

                // 高级设置状态检查
                info.add("高级设置状态检查:")
                info.add("  被拥有: ${isPokemonPlayerOwned(entity)} (清理=${configManager.cleanOwned})")
                info.add("  战斗中: ${isPokemonBattling(entity)} (清理=${configManager.cleanBattling})")
                info.add("  繁忙: ${isPokemonBusy(entity)} (清理=${configManager.cleanBusy})")
                info.add("  不可捕获: ${isPokemonUncatchable(entity)} (清理=${configManager.cleanUncatchable})")
            } else {
                info.add("不是Cobblemon精灵实体，无法获取详细信息")
            }

            return info.joinToString("\n  ")

        } catch (e: Exception) {
            return "获取调试信息失败: ${e.message}"
        }
    }
}

/**
 * 清理结果数据类
 */
data class CleanResult(
    val success: Boolean,
    val pokemonCleanedCount: Int,
    val vanillaMobCleanedCount: Int,
    val itemCleanedCount: Int = 0,
    val duration: Long,
    val worldResults: Map<String, WorldCleanResult>,
    val error: String? = null
) {
    val totalCleanedCount: Int
        get() = pokemonCleanedCount + vanillaMobCleanedCount + itemCleanedCount
}

/**
 * 世界清理结果数据类
 */
data class WorldCleanResult(
    val pokemonCount: Int,
    val vanillaMobCount: Int,
    val itemCount: Int = 0
) {
    val totalCount: Int
        get() = pokemonCount + vanillaMobCount + itemCount
}
