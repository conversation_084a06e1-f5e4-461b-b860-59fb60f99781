/*
 * Copyright (C) 2024 AceBrand Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package cn.acebrand.AcePokemonCleaner.license

import org.bukkit.plugin.Plugin
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonObject
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.InetAddress
import java.net.URI
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PublicKey
import java.security.SecureRandom
import java.security.Signature
import java.security.spec.X509EncodedKeySpec
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 许可证管理器
 *
 * 基于 Lukittu 许可证系统的在线验证实现
 */
class LicenseManager(private val plugin: Plugin) {

    private val logger = plugin.logger

    companion object {
        // Lukittu API 常量
        private const val API_BASE_URL = "https://app.lukittu.com/api/v1/client/teams"
        private const val VERIFY_ENDPOINT = "/verification/verify"
        private const val HEARTBEAT_ENDPOINT = "/verification/heartbeat"
        private const val VERSION = "2.0.0"
        private const val TIMEOUT_MILLIS = 10000 // 10 seconds

        // 硬编码的许可证信息（生产环境中应该混淆）
        private const val TEAM_ID = "3dc75429-cdf4-411d-a901-9c7775a5a758"
        private const val PRODUCT_ID = "55e74fcd-b36f-4582-8a4b-41aac4cf7f0a"
        private const val RSA_PUBLIC_KEY = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE0ZXZSQ1RxSk5CaUhtbVFGdUFiNgpGQUVWREZHbUdkYWhrS2t3eFdpVjFZT3lhdCtwSjh2emRVNnoyU1p2MkMrYk1FWGREbHFjQXBQVThhUVNvcUxqCmtsVjNwMW42Q3dGUEtDNGt1ck9yQy9tVzIwR0VqdjRqTC93dW9ydWQxS002UUhCMk5tbGVER25qa0I2VXhOL3AKUkdQU1QzYlpsc1dRWm92R0ZBNXM0RWFnSGhOZVcxS3BQbm0vclFTQzBKYnd6Wmo0SU1UK29Hb3lJSTZPbXRmYQo4TEgvV0JhejVPcXBqaC94RjVkbE9NK2VBNklrQ3FBSmdhWFJ1ck1CemhPWHZ6dHdvSEdTQUE1YTlTeE9oSi9sCjEwY21wTStSZkFNcFdHTUxxZGtkeTk2Yk83cWp5cjQrMEZmbWw5QitGSWxGU0NGZEMwWm4wd3JCZWdOM2xBYVgKTVFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="

        // JSON 处理器
        private val GSON = GsonBuilder()
            .disableHtmlEscaping()
            .setPrettyPrinting()
            .create()

        // 错误消息映射
        private val ERROR_MESSAGES = mapOf(
            "RELEASE_NOT_FOUND" to "配置中指定的版本无效",
            "LICENSE_NOT_FOUND" to "许可证未在配置中指定，或许可证无效",
            "IP_LIMIT_REACHED" to "许可证的IP地址限制已达到上限。如有问题请联系支持",
            "MAXIMUM_CONCURRENT_SEATS" to "同一许可证连接的最大设备数",
            "RATE_LIMIT" to "同一IP地址在短时间内连接过多。请稍等片刻！",
            "LICENSE_EXPIRED" to "许可证已过期",
            "INTERNAL_SERVER_ERROR" to "上游服务有问题。请通知支持！",
            "BAD_REQUEST" to "无效的请求格式或参数。检查您的许可证配置"
        )
    }

    private var isLicenseValid = false
    private var licenseInfo: LicenseInfo? = null
    private var deviceIdentifier: String? = null
    private var scheduler: ScheduledExecutorService? = null
    
    /**
     * 许可证信息数据类
     */
    data class LicenseInfo(
        val licenseKey: String,
        val serverName: String,
        val expiryDate: String?,
        val maxPlayers: Int,
        val features: Set<String>,
        val issuedDate: String,
        val version: String
    )
    
    /**
     * 验证许可证密钥（在线验证）
     */
    fun validateLicenseKey(licenseKey: String): Boolean {

        return try {
            if (licenseKey.isBlank()) {
                plugin.logger.severe("许可证密钥为空")
                return false
            }

            // 生成设备标识符
            deviceIdentifier = generateHardwareIdentifier()

            // 生成随机挑战
            val challenge = generateRandomChallenge()

            // 构建 API URL
            val url = "$API_BASE_URL/$TEAM_ID$VERIFY_ENDPOINT"

            // 构建请求体
            val jsonBody = """
                {
                  "licenseKey": "$licenseKey",
                  "productId": "$PRODUCT_ID",
                  "challenge": "$challenge",
                  "version": "$VERSION",
                  "deviceIdentifier": "$deviceIdentifier"
                }
            """.trimIndent()

            // 发送请求并处理响应
            val verificationSuccess = fetchAndHandleResponse(url, jsonBody, RSA_PUBLIC_KEY, challenge)

            if (verificationSuccess) {
                isLicenseValid = true
                plugin.logger.info("许可证验证成功")

                // 启动心跳调度器
                setupHeartbeatScheduler(licenseKey)

                return true
            } else {
                plugin.logger.severe("许可证验证失败")
                return false
            }

        } catch (e: Exception) {
            plugin.logger.severe("验证许可证时发生错误: ${e.message}")
            false
        }
    }
    /**
     * 生成随机挑战字符串以防止重放攻击
     */
    private fun generateRandomChallenge(): String {
        val secureRandom = SecureRandom()
        val randomBytes = ByteArray(32)
        secureRandom.nextBytes(randomBytes)
        return bytesToHex(randomBytes)
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private fun bytesToHex(bytes: ByteArray): String {
        val result = StringBuilder(bytes.size * 2)
        for (b in bytes) {
            result.append(String.format("%02x", b))
        }
        return result.toString()
    }

    /**
     * 发送 HTTP 请求到许可证服务器并处理响应
     */
    private fun fetchAndHandleResponse(
        urlString: String,
        jsonBody: String,
        publicKeyBase64: String,
        challenge: String
    ): Boolean {
        var connection: HttpURLConnection? = null
        var success = false

        try {
            val url = URI.create(urlString).toURL()
            connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("User-Agent", buildUserAgent())
            connection.connectTimeout = TIMEOUT_MILLIS
            connection.readTimeout = TIMEOUT_MILLIS
            connection.doOutput = true

            // 发送请求体
            connection.outputStream.use { os ->
                val input = jsonBody.toByteArray(StandardCharsets.UTF_8)
                os.write(input, 0, input.size)
            }

            val responseCode = connection.responseCode

            if (responseCode == HttpURLConnection.HTTP_OK) {
                connection.inputStream.use { inputStream ->
                    success = handleJsonResponse(inputStream, publicKeyBase64, challenge)
                }
            } else {
                connection.errorStream?.use { errorStream ->
                    handleJsonResponse(errorStream, null, null)
                }

                if (responseCode >= 400) {
                    plugin.logger.severe("HTTP 错误代码: $responseCode")
                }
            }

        } catch (e: Exception) {
            plugin.logger.severe("连接到 AceBrand 许可证服务失败: ${e.message}")
            throw IOException("连接到许可证服务器失败", e)
        } finally {
            connection?.disconnect()
        }

        return success
    }
    
    /**
     * 处理 JSON 响应
     */
    private fun handleJsonResponse(
        inputStream: InputStream,
        publicKey: String?,
        challenge: String?
    ): Boolean {
        try {
            BufferedReader(InputStreamReader(inputStream, StandardCharsets.UTF_8)).use { reader ->
                val json = GSON.fromJson(reader, JsonObject::class.java)

                // 记录响应用于调试
                val respString = GSON.toJson(json)

                // 检查这是否是需要验证的成功响应
                if (publicKey != null && challenge != null) {
                    if (validateResponse(json) && validateChallenge(json, challenge, publicKey)) {
                        // 创建许可证信息
                        licenseInfo = LicenseInfo(
                            licenseKey = "验证成功",
                            serverName = "AceBrand Licensed Server",
                            expiryDate = null,
                            maxPlayers = -1,
                            features = setOf("ALL"),
                            issuedDate = "2024-01-01",
                            version = VERSION
                        )
                        return true
                    }
                }

                // 处理错误响应
                if (json.has("result")) {
                    val result = json.getAsJsonObject("result")

                    if (result.has("code")) {
                        val errorCode = result.get("code").asString
                        val errorMessage = ERROR_MESSAGES[errorCode] ?: "AceBrand 许可证检查失败，错误代码: $errorCode"
                        plugin.logger.severe("错误: $errorMessage")
                        return false
                    }
                }

                return false
            }
        } catch (e: Exception) {
            plugin.logger.severe("处理 JSON 响应时发生错误: ${e.message}")
            return false
        }
    }

    /**
     * 验证响应是否表示有效许可证
     */
    private fun validateResponse(json: JsonObject): Boolean {
        return try {
            val result = json.getAsJsonObject("result")
            result != null && result.has("valid") && result.get("valid").asBoolean
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 验证挑战签名
     */
    private fun validateChallenge(response: JsonObject, originalChallenge: String, base64PublicKey: String): Boolean {
        return try {
            if (!validateResponse(response)) {
                return false
            }

            val signedChallenge = response.getAsJsonObject("result")
                .get("challengeResponse").asString

            verifySignature(originalChallenge, signedChallenge, base64PublicKey)
        } catch (e: Exception) {
            plugin.logger.severe("挑战验证失败: ${e.message}")
            false
        }
    }

    /**
     * 执行实际的加密签名验证
     */
    private fun verifySignature(challenge: String, signatureHex: String, base64PublicKey: String): Boolean {
        return try {
            val signatureBytes = hexStringToByteArray(signatureHex)
            val decodedKeyBytes = Base64.getDecoder().decode(base64PublicKey)

            val decodedKeyString = String(decodedKeyBytes)
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replace("\\s".toRegex(), "")

            val publicKeyBytes = Base64.getDecoder().decode(decodedKeyString)
            val keySpec = X509EncodedKeySpec(publicKeyBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val publicKey = keyFactory.generatePublic(keySpec)

            val signature = Signature.getInstance("SHA256withRSA")
            signature.initVerify(publicKey)
            signature.update(challenge.toByteArray())

            signature.verify(signatureBytes)
        } catch (e: Exception) {
            plugin.logger.severe("签名验证失败: ${e.message}")
            false
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private fun hexStringToByteArray(hex: String): ByteArray {
        val len = hex.length
        val data = ByteArray(len / 2)
        for (i in 0 until len step 2) {
            data[i / 2] = ((Character.digit(hex[i], 16) shl 4) + Character.digit(hex[i + 1], 16)).toByte()
        }
        return data
    }
    

    
    /**
     * 设置心跳调度器
     */
    private fun setupHeartbeatScheduler(licenseKey: String) {
        scheduler = Executors.newSingleThreadScheduledExecutor()

        scheduler?.scheduleAtFixedRate({
            try {
                sendHeartbeat(licenseKey)
            } catch (e: Exception) {
                // 静默处理心跳失败
            }
        }, 15, 15, TimeUnit.MINUTES)
    }

    /**
     * 发送心跳到许可证服务器
     */
    private fun sendHeartbeat(licenseKey: String) {
        val urlString = "$API_BASE_URL/$TEAM_ID$HEARTBEAT_ENDPOINT"
        val url = URI.create(urlString).toURL()

        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setRequestProperty("User-Agent", buildUserAgent())
        connection.connectTimeout = TIMEOUT_MILLIS
        connection.readTimeout = TIMEOUT_MILLIS
        connection.doOutput = true

        val jsonBody = """
            {
                "licenseKey": "$licenseKey",
                "productId": "$PRODUCT_ID",
                "deviceIdentifier": "$deviceIdentifier"
            }
        """.trimIndent()

        try {
            connection.outputStream.use { os ->
                val input = jsonBody.toByteArray(StandardCharsets.UTF_8)
                os.write(input, 0, input.size)
            }

            val responseCode = connection.responseCode

            // 静默处理心跳响应
        } catch (e: IOException) {
            // 静默处理心跳异常
        } finally {
            connection.disconnect()
        }
    }

    /**
     * 构建用户代理字符串
     */
    private fun buildUserAgent(): String {
        return String.format(
            "AceDexLoader/%s (%s %s; %s)",
            VERSION,
            System.getProperty("os.name"),
            System.getProperty("os.version"),
            System.getProperty("os.arch")
        )
    }

    /**
     * 生成硬件标识符
     */
    private fun generateHardwareIdentifier(): String {
        return try {
            val osName = System.getProperty("os.name")
            val osVersion = System.getProperty("os.version")
            val osArch = System.getProperty("os.arch")
            val hostname = InetAddress.getLocalHost().hostName

            val combinedIdentifier = osName + osVersion + osArch + hostname
            UUID.nameUUIDFromBytes(combinedIdentifier.toByteArray()).toString()
        } catch (e: Exception) {
            UUID.randomUUID().toString()
        }
    }

    /**
     * 检查许可证是否有效
     */
    fun isValid(): Boolean = isLicenseValid

    /**
     * 获取许可证信息
     */
    fun getLicenseInfo(): LicenseInfo? = licenseInfo

    /**
     * 检查是否有特定功能的许可
     */
    fun hasFeature(feature: String): Boolean {
        if (!isLicenseValid) return false
        val info = licenseInfo ?: return false
        return info.features.contains("ALL") || info.features.contains(feature)
    }

    /**
     * 关闭许可证管理器
     */
    fun shutdown() {
        scheduler?.let { scheduler ->
            if (!scheduler.isShutdown) {
                scheduler.shutdown()
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow()
                    }
                } catch (e: InterruptedException) {
                    scheduler.shutdownNow()
                    Thread.currentThread().interrupt()
                }
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            shutdown()
        } catch (e: Exception) {
            logger.warning("清理许可证管理器资源时发生错误: ${e.message}")
        }
    }
}
