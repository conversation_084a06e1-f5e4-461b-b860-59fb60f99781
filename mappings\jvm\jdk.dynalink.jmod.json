{"md5": "d12afff69ea27fe5c058188d0e94c86d", "sha2": "99d329df83a4c4bd16824e0dd95b64713ea53a79", "sha256": "a230dbd57fdfdf38ec70666e38329d6e4d0032b6535e12641033fdef9423d4e1", "contents": {"classes": {"classes/jdk/dynalink/linker/support/DefaultInternalObjectFilter.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/linker/support/DefaultInternalObjectFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "transform", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "checkHandle", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/String;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "filterVarArgs", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;[<PERSON><PERSON><PERSON>/lang/Object;)[<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FILTER_VARARGS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "parameterFilter", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "returnFilter", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "var<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/linker/GuardedInvocation.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/linker/GuardedInvocation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/SwitchPoint;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/SwitchPoint;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/SwitchPoint;L<PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/SwitchPoint;Ljava/lang/Class<+Ljava/lang/Throwable;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;[Lja<PERSON>/lang/invoke/SwitchPoint;Ljava/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;[Ljava/lang/invoke/SwitchPoint;Ljava/lang/Class<+Ljava/lang/Throwable;>;)V"}, {"nme": "getInvocation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "getSwitchPoints", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/invoke/SwitchPoint;"}, {"nme": "getException", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<+Ljava/lang/Throwable;>;"}, {"nme": "hasBeenInvalidated", "acc": 1, "dsc": "()Z"}, {"nme": "replaceMethods", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/invoke/MethodHandle;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "addSwitchPoint", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/SwitchPoint;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "replaceMethodsOrThis", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/invoke/MethodHandle;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "asType", "acc": 1, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodType;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "asType", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkerServices;<PERSON><PERSON><PERSON>/lang/invoke/MethodType;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "asTypeSafeReturn", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkerServices;<PERSON><PERSON><PERSON>/lang/invoke/MethodType;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "asType", "acc": 1, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "filterArguments", "acc": 129, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "dropArguments", "acc": 1, "dsc": "(ILjava/util/List;)Ljdk/dynalink/linker/GuardedInvocation;", "sig": "(ILjava/util/List<Ljava/lang/Class<*>;>;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "dropArguments", "acc": 129, "dsc": "(I[<PERSON><PERSON><PERSON>/lang/Class;)Ljdk/dynalink/linker/GuardedInvocation;", "sig": "(I[<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "compose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "compose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [{"acc": 18, "nme": "invocation", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "guard", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "exception", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<+Ljava/lang/Throwable;>;"}, {"acc": 18, "nme": "switchPoints", "dsc": "[<PERSON><PERSON><PERSON>/lang/invoke/SwitchPoint;"}]}, "classes/jdk/dynalink/beans/MaximallySpecific.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/MaximallySpecific", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getMaximallySpecificMethods", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Z)Ljava/util/List;", "sig": "(Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;Z)Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;"}, {"nme": "getMaximallySpecificMethodHandles", "acc": 8, "dsc": "(L<PERSON><PERSON>/util/List;Z[Ljava/lang/Class;Ljdk/dynalink/linker/LinkerServices;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;Z[Ljava/lang/Class<*>;Ljdk/dynalink/linker/LinkerServices;)Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "getMaximallySpecificMethods", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/List;Z[Ljava/lang/Class;Ljdk/dynalink/linker/LinkerServices;L<PERSON>va/util/function/Function;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;Z[Ljava/lang/Class<*>;Ljdk/dynalink/linker/LinkerServices;Ljava/util/function/Function<TT;Ljava/lang/invoke/MethodType;>;)Ljava/util/List<TT;>;"}, {"nme": "isMoreSpecific", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;Ljava/lang/invoke/MethodType;Z[Ljava/lang/Class;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodType;Z[Ljava/lang/Class<*>;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "compare", "acc": 10, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;ILjdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;ILjdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "getParameterClass", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;IIZ)Ljava/lang/Class;", "sig": "(Ljava/lang/invoke/MethodType;IIZ)Ljava/lang/Class<*>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/beans/ApplicableOverloadedMethods$1.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ApplicableOverloadedMethods$1", "super": "jdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isApplicable", "acc": 0, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}], "flds": []}, "classes/jdk/dynalink/beans/StaticClass$1.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/StaticClass$1", "super": "java/lang/ClassValue", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(Ljava/lang/Class;)Ljdk/dynalink/beans/StaticClass;", "sig": "(Ljava/lang/Class<*>;)Ljdk/dynalink/beans/StaticClass;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/dynalink/NamedOperation.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/NamedOperation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/dynalink/Operation;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getBaseOperation", "acc": 1, "dsc": "()Ljdk/dynalink/Operation;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "changeName", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/NamedOperation;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseOperation", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;)Ljdk/dynalink/Operation;"}, {"nme": "getName", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;)L<PERSON>va/lang/Object;"}], "flds": [{"acc": 18, "nme": "baseOperation", "dsc": "Ljdk/dynalink/Operation;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/dynalink/beans/AbstractJavaLinker$AnnotatedDynamicMethod.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/beans/AbstractJavaLinker$AnnotatedDynamicMethod", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "getInvocation", "acc": 0, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "method", "dsc": "Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"acc": 16, "nme": "validationType", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/beans/ClassString.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/beans/ClassString", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Ljava/lang/Class;)V", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isVisibleFrom", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)Z"}, {"nme": "getMaximallySpecifics", "acc": 0, "dsc": "(Ljava/util/List;Ljdk/dynalink/linker/LinkerServices;Z)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;Ljdk/dynalink/linker/LinkerServices;Z)Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "getApplicables", "acc": 0, "dsc": "(Lja<PERSON>/util/List;Ljdk/dynalink/linker/LinkerServices;Z)Ljava/util/LinkedList;", "sig": "(Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;Ljdk/dynalink/linker/LinkerServices;Z)Ljava/util/LinkedList<Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "isApplicable", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljdk/dynalink/linker/LinkerServices;Z)Z"}, {"nme": "canConvert", "acc": 10, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/Class;Ljava/lang/Class;)Z", "sig": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "lambda$isVisibleFrom$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GET_CLASS_LOADER_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 24, "nme": "NULL_CLASS", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "classes", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}, {"acc": 2, "nme": "hashCode", "dsc": "I"}]}, "classes/jdk/dynalink/beans/BeanLinker$Binder.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeanLinker$Binder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Lja<PERSON>/lang/invoke/MethodType;Ljava/lang/Object;)V"}, {"nme": "bind", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "bindTest", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "convertArgToNumber", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "bindToFixedKey", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [{"acc": 18, "nme": "linkerServices", "dsc": "Ljdk/dynalink/linker/LinkerServices;"}, {"acc": 18, "nme": "methodType", "dsc": "Ljava/lang/invoke/MethodType;"}, {"acc": 18, "nme": "fixedKey", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/dynalink/beans/GuardedInvocationComponent.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/GuardedInvocationComponent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V", "sig": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class<*>;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;Ljava/lang/Class;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V", "sig": "(Ljdk/dynalink/linker/GuardedInvocation;Ljava/lang/Class<*>;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "replaceInvocation", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "replaceInvocation", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;)V"}, {"nme": "getGuardedInvocation", "acc": 0, "dsc": "()Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "getValidatorClass", "acc": 0, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "getValidationType", "acc": 0, "dsc": "()Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"nme": "compose", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class<*>;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}], "flds": [{"acc": 18, "nme": "guardedInvocation", "dsc": "Ljdk/dynalink/linker/GuardedInvocation;"}, {"acc": 18, "nme": "validator", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;"}]}, "classes/jdk/dynalink/linker/MethodTypeConversionStrategy.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/MethodTypeConversionStrategy", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/dynalink/StandardNamespace.class": {"ver": 65, "acc": 16433, "nme": "jdk/dynalink/StandardNamespace", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/StandardNamespace;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/StandardNamespace;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;)Ljdk/dynalink/StandardNamespace;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/StandardNamespace;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PROPERTY", "dsc": "Ljdk/dynalink/StandardNamespace;"}, {"acc": 16409, "nme": "ELEMENT", "dsc": "Ljdk/dynalink/StandardNamespace;"}, {"acc": 16409, "nme": "METHOD", "dsc": "Ljdk/dynalink/StandardNamespace;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/StandardNamespace;"}]}, "classes/jdk/dynalink/beans/CallerSensitiveDynamicMethod.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/CallerSensitiveDynamicMethod", "super": "jdk/dynalink/beans/SingleDynamicMethod", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Executable;)V"}, {"nme": "getName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Executable;)Ljava/lang/String;"}, {"nme": "getMethodType", "acc": 0, "dsc": "()Ljava/lang/invoke/MethodType;"}, {"nme": "getMethodType", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Executable;)Ljava/lang/invoke/MethodType;"}, {"nme": "isVarArgs", "acc": 0, "dsc": "()Z"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "isConstructor", "acc": 0, "dsc": "()Z"}, {"nme": "unreflect", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectConstructor", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Constructor;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Constructor<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GET_LOOKUP_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 18, "nme": "target", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Executable;"}, {"acc": 18, "nme": "type", "dsc": "Ljava/lang/invoke/MethodType;"}]}, "classes/jdk/dynalink/beans/CheckRestrictedPackage.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/CheckRestrictedPackage", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isRestrictedClass", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "lambda$isRestrictedClass$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/SecurityManager;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Void;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NO_PERMISSIONS_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}]}, "classes/jdk/dynalink/beans/OverloadedMethod.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/OverloadedMethod", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/List;Ljdk/dynalink/beans/OverloadedDynamicMethod;Ljava/lang/ClassLoader;Ljava/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/SecureLookupSupplier;)V", "sig": "(Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;Ljdk/dynalink/beans/OverloadedDynamicMethod;Ljava/lang/ClassLoader;Ljava/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/SecureLookupSupplier;)V"}, {"nme": "getInvoker", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "selectMethod", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "getNoSuchMethodThrower", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "([<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "throwNoSuchMethod", "acc": 2, "dsc": "([Ljava/lang/Class;)V", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;)V", "exs": ["java/lang/NoSuchMethodException"]}, {"nme": "getAmbiguousMethodThrower", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;Lja<PERSON>/util/List;)<PERSON>ja<PERSON>/lang/invoke/MethodHandle;", "sig": "([<PERSON>ja<PERSON>/lang/Class<*>;Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "adaptThrower", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "throwAmbiguousMethod", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;Ljava/util/List;)V", "sig": "([Lja<PERSON>/lang/Class<*>;Ljava/util/List<Ljava/lang/invoke/MethodHandle;>;)V", "exs": ["java/lang/NoSuchMethodException"]}, {"nme": "argTypesString", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "([<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "getSignatureList", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/String;", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/String;"}, {"nme": "appendSig", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "appendTypes", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;[Ljava/lang/Class;Z)V", "sig": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;[Ljava/lang/Class<*>;Z)V"}, {"nme": "getCommonReturnType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/util/List<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/Class<*>;"}, {"nme": "lambda$selectMethod$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "argTypesToMethods", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/dynalink/beans/ClassString;Ljava/lang/invoke/MethodHandle;>;"}, {"acc": 18, "nme": "parent", "dsc": "Ljdk/dynalink/beans/OverloadedDynamicMethod;"}, {"acc": 18, "nme": "callSiteClassLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 18, "nme": "callSiteType", "dsc": "Ljava/lang/invoke/MethodType;"}, {"acc": 18, "nme": "invoker", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "linkerServices", "dsc": "Ljdk/dynalink/linker/LinkerServices;"}, {"acc": 18, "nme": "lookupSupplier", "dsc": "Ljdk/dynalink/SecureLookupSupplier;"}, {"acc": 18, "nme": "fixArgMethods", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/invoke/MethodHandle;>;"}, {"acc": 18, "nme": "varArgMethods", "dsc": "<PERSON><PERSON><PERSON>/util/ArrayList;", "sig": "Ljava/util/ArrayList<Ljava/lang/invoke/MethodHandle;>;"}, {"acc": 26, "nme": "SELECT_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "THROW_NO_SUCH_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "THROW_AMBIGUOUS_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/support/SimpleRelinkableCallSite.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/support/SimpleRelinkableCallSite", "super": "jdk/dynalink/support/AbstractRelinkableCallSite", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)V"}, {"nme": "relink", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "resetAndRelink", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}], "flds": []}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/dynalink/beans/BeanLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeanLinker", "super": "jdk/dynalink/beans/AbstractJavaLinker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "canLinkType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "createFacetIntrospector", "acc": 0, "dsc": "()Ljdk/dynalink/beans/FacetIntrospector;"}, {"nme": "getGuardedInvocationComponent", "acc": 4, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getConstructorMethod", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "dropObjectArguments", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;I)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getElementGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "guardedInvocationComponentAndCollectionType", "acc": 2, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;Ljava/util/function/Function;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/BeanLinker$GuardedInvocationComponentAndCollectionType;", "sig": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;Ljava/util/function/Function<Ljava/lang/Class<*>;Ljava/lang/invoke/MethodHandle;>;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/BeanLinker$GuardedInvocationComponentAndCollectionType;"}, {"nme": "getTypedName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;ZLjdk/dynalink/linker/LinkerServices;)<PERSON>java/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "guardComponentWithRangeCheck", "acc": 10, "dsc": "(Ljdk/dynalink/beans/BeanLinker$GuardedInvocationComponentAndCollectionType;Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/GuardedInvocationComponent;Ljdk/dynalink/beans/BeanLinker$Binder;Ljava/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "createInternalFilteredGuardedInvocationComponent", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "createGuardedInvocationComponentAsType", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "createInternalFilteredGuardedInvocationComponent", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/Class<*>;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "convertKeyToInteger", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljdk/dynalink/linker/LinkerServices;)<PERSON><PERSON><PERSON>/lang/Integer;", "exs": ["java/lang/Exception"]}, {"nme": "findRangeCheck", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "rangeCheck", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "rangeCheck", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(<PERSON><PERSON><PERSON>/util/List<*>;Ljava/lang/Object;)Z"}, {"nme": "noOp", "acc": 10, "dsc": "()V"}, {"nme": "getElementSetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getElementRemover", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "assertParameterCount", "acc": 10, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;I)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GET_LIST_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_MAP_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "LIST_GUARD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "MAP_GUARD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NULL_GETTER_1", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NULL_GETTER_2", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "INVALID_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "RANGE_CHECK_ARRAY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "RANGE_CHECK_LIST", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONTAINS_MAP", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "SET_LIST_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "PUT_MAP_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NO_OP_1", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NO_OP_2", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "NO_OP_3", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "REMOVE_LIST_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "REMOVE_MAP_ELEMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_COLLECTION_LENGTH", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_MAP_LENGTH", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_ARRAY_LENGTH", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/linker/ConversionComparator.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/ConversionComparator", "super": "java/lang/Object", "mthds": [{"nme": "compareConversion", "acc": 1025, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}], "flds": []}, "classes/jdk/dynalink/beans/MissingMemberHandlerFactory.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/beans/MissingMemberHandlerFactory", "super": "java/lang/Object", "mthds": [{"nme": "createMissingMemberHandler", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;", "exs": ["java/lang/Exception"]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/dynalink/BiClassValue.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/BiClassValue", "super": "java/lang/Object", "mthds": [{"nme": "computing", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/function/BiFunction;)Ljdk/dynalink/BiClassValue;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/BiFunction<Ljava/lang/Class<*>;Ljava/lang/Class<*>;TT;>;)Ljdk/dynalink/BiClassValue<TT;>;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/BiFunction;)V", "sig": "(Lja<PERSON>/util/function/BiFunction<Ljava/lang/Class<*>;Ljava/lang/Class<*>;TT;>;)V"}, {"nme": "get", "acc": 16, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)TT;"}, {"nme": "getRetentionDirection", "acc": 10, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/BiClassValue$RetentionDirection;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"nme": "lambda$getRetentionDirection$2", "acc": 4106, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"nme": "lambda$get$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "lambda$get$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "root", "dsc": "Ljdk/dynalink/BiClassValue$BiClassValuesRoot;", "sig": "Ljdk/dynalink/BiClassValue$BiClassValuesRoot<TT;>;"}, {"acc": 18, "nme": "compute", "dsc": "Lja<PERSON>/util/function/BiFunction;", "sig": "Ljava/util/function/BiFunction<Ljava/lang/Class<*>;Ljava/lang/Class<*>;TT;>;"}, {"acc": 26, "nme": "GET_CLASS_LOADER_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}]}, "classes/jdk/dynalink/linker/support/CompositeTypeBasedGuardingDynamicLinker$ClassToLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/linker/support/CompositeTypeBasedGuardingDynamicLinker$ClassToLinker", "super": "java/lang/ClassValue", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;)V"}, {"nme": "computeValue", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/List<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NO_LINKER", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;"}, {"acc": 18, "nme": "linkers", "dsc": "[Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;"}, {"acc": 18, "nme": "singletonLinkers", "dsc": "[<PERSON><PERSON><PERSON>/util/List;", "sig": "[Ljava/util/List<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;"}]}, "classes/jdk/dynalink/TypeConverterFactory.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/TypeConverterFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;Ljdk/dynalink/linker/MethodTypeConversionStrategy;)V", "sig": "(L<PERSON><PERSON>/lang/Iterable<+Ljdk/dynalink/linker/GuardingTypeConverterFactory;>;Ljdk/dynalink/linker/MethodTypeConversionStrategy;)V"}, {"nme": "asType", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "applyConverters", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;ILjava/util/List;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/invoke/MethodHandle;ILjava/util/List<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "canConvert", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "compareConversion", "acc": 0, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "canAutoConvert", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getCacheableTypeConverterNull", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON>java/lang/Class;Ljdk/dynalink/BiClassValue;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljdk/dynalink/BiClassValue<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTypeConverterNull", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON>java/lang/Class;Ljdk/dynalink/BiClassValue;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljdk/dynalink/BiClassValue<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getCacheableTypeConverter", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON>java/lang/Class;Ljdk/dynalink/BiClassValue;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljdk/dynalink/BiClassValue<Ljava/lang/invoke/MethodHandle;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTypeConverter", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "createConverter", "acc": 8, "dsc": "(Ljava/lang/Class;Ljava/lang/Class;[Ljdk/dynalink/linker/GuardingTypeConverterFactory;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;[Ljdk/dynalink/linker/GuardingTypeConverterFactory;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "lambda$new$2", "acc": 4106, "dsc": "(Ljdk/dynalink/BiClassValue;<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)<PERSON><PERSON><PERSON>/lang/Boolean;"}, {"nme": "lambda$new$1", "acc": 4106, "dsc": "(Ljdk/dynalink/BiClassValue;Lja<PERSON>/lang/Class;Ljava/lang/Class;)<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "([Ljdk/dynalink/linker/GuardingTypeConverterFactory;Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "comparators", "dsc": "[Ljdk/dynalink/linker/ConversionComparator;"}, {"acc": 18, "nme": "autoConversionStrategy", "dsc": "Ljdk/dynalink/linker/MethodTypeConversionStrategy;"}, {"acc": 18, "nme": "converterMap", "dsc": "Ljdk/dynalink/BiClassValue;", "sig": "Ljdk/dynalink/BiClassValue<Ljava/lang/invoke/MethodHandle;>;"}, {"acc": 18, "nme": "converterIdentityMap", "dsc": "Ljdk/dynalink/BiClassValue;", "sig": "Ljdk/dynalink/BiClassValue<Ljava/lang/invoke/MethodHandle;>;"}, {"acc": 18, "nme": "canConvert", "dsc": "Ljdk/dynalink/BiClassValue;", "sig": "Ljdk/dynalink/BiClassValue<Ljava/lang/Bo<PERSON>an;>;"}, {"acc": 24, "nme": "IDENTITY_CONVERSION", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/beans/AbstractJavaLinker$MethodPair.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/AbstractJavaLinker$MethodPair", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "guardWithTest", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [{"acc": 16, "nme": "method1", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 16, "nme": "method2", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/beans/OverloadedDynamicMethod.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/OverloadedDynamicMethod", "super": "jdk/dynalink/beans/DynamicMethod", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/String;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;)V"}, {"nme": "getMethodForExactParamTypes", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "getInvocation", "acc": 0, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getCallSiteClassLoader", "acc": 10, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljava/lang/ClassLoader;"}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "isConstructor", "acc": 1, "dsc": "()Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isApplicableDynamically", "acc": 10, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "isApplicableDynamically", "acc": 10, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/Class;Ljava/lang/Class;)Z", "sig": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getApplicables", "acc": 2, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;)Ljdk/dynalink/beans/ApplicableOverloadedMethods;"}, {"nme": "addMethod", "acc": 1, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;)V"}, {"nme": "constructorFlagConsistent", "acc": 2, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "isPotentiallyConvertible", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "createPrimitiveWrapperTypes", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"nme": "addClassHierarchy", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Map;Ljava/lang/Class;)V", "sig": "(Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Class<*>;>;Ljava/lang/Class<*>;)V"}, {"nme": "isAssignableFromBoxedPrimitive", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "lambda$getCallSiteClassLoader$1", "acc": 4106, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljava/lang/ClassLoader;"}, {"nme": "lambda$getInvocation$0", "acc": 4106, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "methods", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Ljdk/dynalink/beans/SingleDynamicMethod;>;"}, {"acc": 26, "nme": "GET_CALL_SITE_CLASS_LOADER_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 26, "nme": "PRIMITIVE_WRAPPER_TYPES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/linker/support/CompositeGuardingDynamicLinker.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/linker/support/CompositeGuardingDynamicLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(L<PERSON><PERSON>/lang/Iterable<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;)V"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "linkers", "dsc": "[Ljdk/dynalink/linker/GuardingDynamicLinker;"}]}, "classes/jdk/dynalink/LinkerServicesImpl$LinkerException.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/LinkerServicesImpl$LinkerException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "classes/jdk/dynalink/linker/support/CompositeTypeBasedGuardingDynamicLinker.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/linker/support/CompositeTypeBasedGuardingDynamicLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(Ljava/lang/Iterable<+Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;)V"}, {"nme": "canLinkType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "optimize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;)Ljava/util/List<Ljdk/dynalink/linker/GuardingDynamicLinker;>;"}, {"nme": "addTypeBased", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljdk/dynalink/linker/GuardingDynamicLinker;>;Ljava/util/List<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;)V"}], "flds": [{"acc": 18, "nme": "classToLinker", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassValue;", "sig": "Ljava/lang/ClassValue<Ljava/util/List<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;>;"}]}, "classes/jdk/dynalink/beans/LinkerServicesWithMissingMemberHandlerFactory.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/beans/LinkerServicesWithMissingMemberHandlerFactory", "super": "java/lang/Object", "mthds": [{"nme": "get", "acc": 8, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/beans/MissingMemberHandlerFactory;)Ljdk/dynalink/linker/LinkerServices;"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/beans/MissingMemberHandlerFactory;)V"}, {"nme": "asType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTypeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "canConvert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "compareConversion", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "filterInternalObjects", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "getWithLookup", "acc": 1, "dsc": "(Lja<PERSON>/util/function/Supplier;Ljdk/dynalink/SecureLookupSupplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/Supplier<TT;>;Ljdk/dynalink/SecureLookupSupplier;)TT;"}], "flds": [{"acc": 16, "nme": "linkerServices", "dsc": "Ljdk/dynalink/linker/LinkerServices;"}, {"acc": 16, "nme": "missingMemberHandlerFactory", "dsc": "Ljdk/dynalink/beans/MissingMemberHandlerFactory;"}]}, "classes/jdk/dynalink/beans/ApplicableOverloadedMethods$2.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ApplicableOverloadedMethods$2", "super": "jdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isApplicable", "acc": 0, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}], "flds": []}, "classes/jdk/dynalink/support/AbstractRelinkableCallSite.class": {"ver": 65, "acc": 1057, "nme": "jdk/dynalink/support/AbstractRelinkableCallSite", "super": "java/lang/invoke/MutableCallSite", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)V"}, {"nme": "getDescriptor", "acc": 1, "dsc": "()Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "initialize", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}], "flds": [{"acc": 18, "nme": "descriptor", "dsc": "Ljdk/dynalink/CallSiteDescriptor;"}]}, "classes/jdk/dynalink/beans/AbstractJavaLinker.class": {"ver": 65, "acc": 1056, "nme": "jdk/dynalink/beans/AbstractJavaLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/invoke/MethodHandle;)V", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/invoke/MethodHandle;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)V"}, {"nme": "decapitalize", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createFacetIntrospector", "acc": 1024, "dsc": "()Ljdk/dynalink/beans/FacetIntrospector;"}, {"nme": "getReadablePropertyNames", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getWritablePropertyNames", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getMethodNames", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getUnmodifiableKeys", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/util/Set;", "sig": "(Ljava/util/Map<Ljava/lang/String;*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "setPropertyGetter", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/dynalink/beans/SingleDynamicMethod;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "setPropertyGetter", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;I)V"}, {"nme": "setPropertyGetter", "acc": 0, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/invoke/MethodHandle;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "addMember", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/reflect/Executable;Ljava/util/Map;)V", "sig": "(Ljava/lang/String;Ljava/lang/reflect/Executable;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)V"}, {"nme": "addMember", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/dynalink/beans/SingleDynamicMethod;Ljava/util/Map;)V", "sig": "(Ljava/lang/String;Ljdk/dynalink/beans/SingleDynamicMethod;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)V"}, {"nme": "createDynamicMethod", "acc": 8, "dsc": "(L<PERSON><PERSON>/lang/Iterable;Ljava/lang/Class;Ljava/lang/String;)Ljdk/dynalink/beans/DynamicMethod;", "sig": "(Ljava/lang/Iterable<+Ljava/lang/reflect/Executable;>;Ljava/lang/Class<*>;Ljava/lang/String;)Ljdk/dynalink/beans/DynamicMethod;"}, {"nme": "createDynamicMethod", "acc": 10, "dsc": "(Ljava/lang/reflect/Executable;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "unreflectSafely", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Executable;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "mergeMethods", "acc": 10, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;Ljdk/dynalink/beans/DynamicMethod;Ljava/lang/Class;Ljava/lang/String;)Ljdk/dynalink/beans/DynamicMethod;", "sig": "(Ljdk/dynalink/beans/SingleDynamicMethod;Ljdk/dynalink/beans/DynamicMethod;Ljava/lang/Class<*>;Ljava/lang/String;)Ljdk/dynalink/beans/DynamicMethod;"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "getGuardedInvocationComponent", "acc": 4, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getNextComponent", "acc": 0, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "createNoSuchMemberHandler", "acc": 2, "dsc": "(Ljdk/dynalink/beans/MissingMemberHandlerFactory;Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getClassGuard", "acc": 0, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getClassGuardedInvocationComponent", "acc": 0, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"nme": "getConstructorMethod", "acc": 1024, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "getAssignable<PERSON><PERSON>", "acc": 2, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "createGuardedDynamicMethodInvocation", "acc": 2, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;Ljava/lang/String;Ljava/util/Map;)Ljdk/dynalink/linker/GuardedInvocation;", "sig": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "getDynamicMethodInvocation", "acc": 2, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;Ljava/lang/String;Ljava/util/Map;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getDynamicMethod", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/util/Map;)Ljdk/dynalink/beans/DynamicMethod;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)Ljdk/dynalink/beans/DynamicMethod;"}, {"nme": "getExplicitSignatureDynamicMethod", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/util/Map;)Ljdk/dynalink/beans/SingleDynamicMethod;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "getPropertySetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getUnnamedPropertySetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getNamedPropertySetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getPropertyGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getUnnamedPropertyGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getNamedPropertyGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 2, "dsc": "(Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getMethodGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getMethodGetterType", "acc": 10, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljava/lang/invoke/MethodType;"}, {"nme": "getUnnamedMethodGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getNamedMethodGetter", "acc": 2, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "matchReturnTypes", "acc": 8, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljdk/dynalink/beans/AbstractJavaLinker$MethodPair;"}, {"nme": "assertParameterCount", "acc": 10, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;I)V"}, {"nme": "getPropertyGetterHandle", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPropertySetterHandle", "acc": 2, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getDynamicMethod", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getDynamicMethod", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/DynamicMethod;"}, {"nme": "getMostGenericGetter", "acc": 10, "dsc": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/reflect/Method;"}, {"nme": "getMostGenericGetter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/reflect/Method;", "sig": "(L<PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;)Ljava/lang/reflect/Method;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "classGuard", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "assignableGuard", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "propertyGetters", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/AbstractJavaLinker$AnnotatedDynamicMethod;>;"}, {"acc": 18, "nme": "propertySetters", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;"}, {"acc": 18, "nme": "methods", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/dynalink/beans/DynamicMethod;>;"}, {"acc": 26, "nme": "IS_METHOD_HANDLE_NOT_NULL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTANT_NULL_DROP_METHOD_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "privateLookup", "dsc": "Ljdk/dynalink/linker/support/Lookup;"}, {"acc": 26, "nme": "IS_ANNOTATED_METHOD_NOT_NULL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "CONSTANT_NULL_DROP_ANNOTATED_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_ANNOTATED_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GETTER_INVOKER", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_DYNAMIC_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "OBJECT_IDENTITY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_PROPERTY_GETTER_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "getPropertyGetterHandle", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_PROPERTY_SETTER_HANDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "getPropertySetterHandle", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "GET_DYNAMIC_METHOD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "getDynamicMethod", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/beans/BeanLinker$CollectionType.class": {"ver": 65, "acc": 16432, "nme": "jdk/dynalink/beans/BeanLinker$CollectionType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ARRAY", "dsc": "Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"acc": 16409, "nme": "LIST", "dsc": "Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"acc": 16409, "nme": "MAP", "dsc": "Ljdk/dynalink/beans/BeanLinker$CollectionType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/beans/BeanLinker$CollectionType;"}]}, "classes/jdk/dynalink/Namespace.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/Namespace", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/jdk/dynalink/SecureLookupSupplier.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/SecureLookupSupplier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;)V"}, {"nme": "getLookup", "acc": 17, "dsc": "()Ljava/lang/invoke/MethodHandles$Lookup;"}, {"nme": "getLookupPrivileged", "acc": 20, "dsc": "()Ljava/lang/invoke/MethodHandles$Lookup;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "GET_LOOKUP_PERMISSION_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dynalink.getLookup"}, {"acc": 26, "nme": "GET_LOOKUP_PERMISSION", "dsc": "<PERSON><PERSON><PERSON>/lang/RuntimePermission;"}, {"acc": 18, "nme": "lookup", "dsc": "Ljava/lang/invoke/MethodHandles$Lookup;"}]}, "classes/jdk/dynalink/beans/SimpleDynamicMethod.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/SimpleDynamicMethod", "super": "jdk/dynalink/beans/SingleDynamicMethod", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/Class;Lja<PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class<*>;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Z)V", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/Class<*>;Ljava/lang/String;Z)V"}, {"nme": "getName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class<*>;Ljava/lang/String;Z)Ljava/lang/String;"}, {"nme": "isVarArgs", "acc": 0, "dsc": "()Z"}, {"nme": "getMethodType", "acc": 0, "dsc": "()Ljava/lang/invoke/MethodType;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "isConstructor", "acc": 0, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "target", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 18, "nme": "constructor", "dsc": "Z"}]}, "classes/jdk/dynalink/TypeConverterFactory$NotCacheableConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/TypeConverterFactory$NotCacheableConverter", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}], "flds": [{"acc": 16, "nme": "converter", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/beans/DynamicMethod.class": {"ver": 65, "acc": 1056, "nme": "jdk/dynalink/beans/DynamicMethod", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getInvocation", "acc": 1024, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getMethodForExactParamTypes", "acc": 1024, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "contains", "acc": 1024, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "getClassAndMethodName", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isConstructor", "acc": 0, "dsc": "()Z"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/dynalink/internal/AccessControlContextFactory.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/internal/AccessControlContextFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "createAccessControlContext", "acc": 9, "dsc": "()Ljava/security/AccessControlContext;"}, {"nme": "createAccessControlContext", "acc": 137, "dsc": "([Ljava/security/Permission;)Ljava/security/AccessControlContext;"}, {"nme": "createAccessControlContext", "acc": 137, "dsc": "([Lja<PERSON>/lang/String;)Ljava/security/AccessControlContext;"}, {"nme": "makeRuntimePermissions", "acc": 138, "dsc": "([Lja<PERSON>/lang/String;)[Ljava/security/Permission;"}, {"nme": "lambda$makeRuntimePermissions$0", "acc": 4106, "dsc": "(I)[Ljava/security/Permission;"}], "flds": []}, "classes/jdk/dynalink/beans/ApplicableOverloadedMethods.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ApplicableOverloadedMethods", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/List;Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;)V", "sig": "(Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;)V"}, {"nme": "getMethods", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;"}, {"nme": "findMaximallySpecificMethods", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "methods", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/dynalink/beans/SingleDynamicMethod;>;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 24, "nme": "APPLICABLE_BY_SUBTYPING", "dsc": "Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;"}, {"acc": 24, "nme": "APPLICABLE_BY_METHOD_INVOCATION_CONVERSION", "dsc": "Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;"}, {"acc": 24, "nme": "APPLICABLE_BY_VARIABLE_ARITY", "dsc": "Ljdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest;"}]}, "classes/jdk/dynalink/linker/support/SimpleLinkRequest.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/linker/support/SimpleLinkRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Z[<PERSON>ja<PERSON>/lang/Object;)V"}, {"nme": "getArguments", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getReceiver", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getCallSiteDescriptor", "acc": 1, "dsc": "()Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "isCallSiteUnstable", "acc": 1, "dsc": "()Z"}, {"nme": "replaceArguments", "acc": 129, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;[<PERSON><PERSON><PERSON>/lang/Object;)Ljdk/dynalink/linker/LinkRequest;"}], "flds": [{"acc": 18, "nme": "callSiteDescriptor", "dsc": "Ljdk/dynalink/CallSiteDescriptor;"}, {"acc": 18, "nme": "arguments", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "callSiteUnstable", "dsc": "Z"}]}, "classes/jdk/dynalink/NamespaceOperation.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/NamespaceOperation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 129, "dsc": "(Ljdk/dynalink/Operation;[Ljdk/dynalink/Namespace;)V"}, {"nme": "getBaseOperation", "acc": 1, "dsc": "()Ljdk/dynalink/Operation;"}, {"nme": "getNamespaces", "acc": 1, "dsc": "()[Ljdk/dynalink/Namespace;"}, {"nme": "getNamespaceCount", "acc": 1, "dsc": "()I"}, {"nme": "getNamespace", "acc": 1, "dsc": "(I)Ljdk/dynalink/Namespace;"}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/dynalink/Namespace;)Z"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getBaseOperation", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;)Ljdk/dynalink/Operation;"}, {"nme": "getNamespaces", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;)[Ljdk/dynalink/Namespace;"}, {"nme": "contains", "acc": 9, "dsc": "(Ljdk/dynalink/Operation;Ljdk/dynalink/Operation;Ljdk/dynalink/Namespace;)Z"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "baseOperation", "dsc": "Ljdk/dynalink/Operation;"}, {"acc": 18, "nme": "namespaces", "dsc": "[Ljdk/dynalink/Namespace;"}]}, "classes/jdk/dynalink/beans/BeanLinker$GuardedInvocationComponentAndCollectionType.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeanLinker$GuardedInvocationComponentAndCollectionType", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/beans/GuardedInvocationComponent;Ljdk/dynalink/beans/BeanLinker$CollectionType;)V"}], "flds": [{"acc": 16, "nme": "gic", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent;"}, {"acc": 16, "nme": "collectionType", "dsc": "Ljdk/dynalink/beans/BeanLinker$CollectionType;"}]}, "classes/jdk/dynalink/beans/StaticClassLinker$SingleClassStaticsLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/StaticClassLinker$SingleClassStaticsLinker", "super": "jdk/dynalink/beans/AbstractJavaLinker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "createConstructorMethod", "acc": 10, "dsc": "(Ljava/lang/Class;)Ljdk/dynalink/beans/DynamicMethod;", "sig": "(Ljava/lang/Class<*>;)Ljdk/dynalink/beans/DynamicMethod;"}, {"nme": "createFacetIntrospector", "acc": 0, "dsc": "()Ljdk/dynalink/beans/FacetIntrospector;"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "getGuardedInvocationComponent", "acc": 4, "dsc": "(Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;)Ljdk/dynalink/beans/GuardedInvocationComponent;", "exs": ["java/lang/Exception"]}, {"nme": "getConstructorMethod", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}], "flds": [{"acc": 18, "nme": "constructor", "dsc": "Ljdk/dynalink/beans/DynamicMethod;"}]}, "classes/jdk/dynalink/beans/AccessibleMembersLookup$MethodSignature.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/beans/AccessibleMembersLookup$MethodSignature", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/Class<*>;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "args", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}]}, "classes/jdk/dynalink/beans/StaticClassLinker$1.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/StaticClassLinker$1", "super": "java/lang/ClassValue", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(Ljava/lang/Class;)Ljdk/dynalink/beans/StaticClassLinker$SingleClassStaticsLinker;", "sig": "(Ljava/lang/Class<*>;)Ljdk/dynalink/beans/StaticClassLinker$SingleClassStaticsLinker;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/dynalink/DynamicLinkerFactory.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/DynamicLinkerFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "set<PERSON>lass<PERSON><PERSON>der", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "setPrioritizedLinkers", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;)V"}, {"nme": "setPrioritizedLinkers", "acc": 129, "dsc": "([Ljdk/dynalink/linker/GuardingDynamicLinker;)V"}, {"nme": "setPrioritizedLinker", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardingDynamicLinker;)V"}, {"nme": "setFallbackLinkers", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;)V"}, {"nme": "setFallbackLinkers", "acc": 129, "dsc": "([Ljdk/dynalink/linker/GuardingDynamicLinker;)V"}, {"nme": "setSyncOnRelink", "acc": 1, "dsc": "(Z)V"}, {"nme": "setUnstableRelinkThreshold", "acc": 1, "dsc": "(I)V"}, {"nme": "setPrelinkTransformer", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardedInvocationTransformer;)V"}, {"nme": "setAutoConversionStrategy", "acc": 1, "dsc": "(Ljdk/dynalink/linker/MethodTypeConversionStrategy;)V"}, {"nme": "setInternalObjectsFilter", "acc": 1, "dsc": "(Ljdk/dynalink/linker/MethodHandleTransformer;)V"}, {"nme": "createLinker", "acc": 1, "dsc": "()Ljdk/dynalink/DynamicLinker;"}, {"nme": "getAutoLoadingErrors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/util/ServiceConfigurationError;>;"}, {"nme": "discoverAutoLoadLinkers", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/dynalink/linker/GuardingDynamicLinker;>;"}, {"nme": "getThreadContextClassLoader", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "addClasses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/Set<Ljava/lang/Class<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;>;Ljava/util/List<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;)V"}, {"nme": "copyListRequireNonNullElements", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;)Ljava/util/List<TT;>;"}, {"nme": "requireNonNullElements", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/List;Ljava/util/function/Supplier;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/List<TT;>;Ljava/util/function/Supplier<Ljava/lang/String;>;)Ljava/util/List<TT;>;"}, {"nme": "lambda$copyListRequireNonNullElements$6", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$getThreadContextClassLoader$5", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "lambda$discoverAutoLoadLinkers$4", "acc": 4106, "dsc": "(Ljdk/dynalink/linker/GuardingDynamicLinkerExporter;)Ljava/lang/String;"}, {"nme": "lambda$discoverAutoLoadLinkers$3", "acc": 4106, "dsc": "(Ljdk/dynalink/linker/GuardingDynamicLinkerExporter;)Ljava/lang/String;"}, {"nme": "lambda$discoverAutoLoadLinkers$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON>ja<PERSON>/util/ServiceLoader;"}, {"nme": "lambda$createLinker$1", "acc": 4106, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "lambda$createLinker$0", "acc": 4106, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "GET_CLASS_LOADER_CONTEXT", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 26, "nme": "DEFAULT_UNSTABLE_RELINK_THRESHOLD", "dsc": "I", "val": 8}, {"acc": 2, "nme": "classLoaderExplicitlySet", "dsc": "Z"}, {"acc": 2, "nme": "classLoader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "prioritizedLinkers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;"}, {"acc": 2, "nme": "fallbackLinkers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<+Ljdk/dynalink/linker/GuardingDynamicLinker;>;"}, {"acc": 2, "nme": "sync<PERSON>n<PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 2, "nme": "unstableRelinkThreshold", "dsc": "I"}, {"acc": 2, "nme": "prelinkTransformer", "dsc": "Ljdk/dynalink/linker/GuardedInvocationTransformer;"}, {"acc": 2, "nme": "autoConversionStrategy", "dsc": "Ljdk/dynalink/linker/MethodTypeConversionStrategy;"}, {"acc": 2, "nme": "internalObjectsFilter", "dsc": "Ljdk/dynalink/linker/MethodHandleTransformer;"}, {"acc": 2, "nme": "autoLoadingErrors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/ServiceConfigurationError;>;"}]}, "classes/jdk/dynalink/beans/AccessibleMembersLookup.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/AccessibleMembersLookup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Z)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Z)V"}, {"nme": "getMethods", "acc": 0, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Method;>;"}, {"nme": "getInnerClasses", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Class;", "sig": "()[<PERSON><PERSON><PERSON>/lang/Class<*>;"}, {"nme": "getAccessibleMethod", "acc": 0, "dsc": "(Lja<PERSON>/lang/reflect/Method;)Ljava/lang/reflect/Method;"}, {"nme": "lookupAccessibleMembers", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}], "flds": [{"acc": 18, "nme": "methods", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/dynalink/beans/AccessibleMembersLookup$MethodSignature;Ljava/lang/reflect/Method;>;"}, {"acc": 18, "nme": "innerClasses", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 18, "nme": "instance", "dsc": "Z"}]}, "classes/jdk/dynalink/linker/ConversionComparator$Comparison.class": {"ver": 65, "acc": 16433, "nme": "jdk/dynalink/linker/ConversionComparator$Comparison", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "INDETERMINATE", "dsc": "Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"acc": 16409, "nme": "TYPE_1_BETTER", "dsc": "Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"acc": 16409, "nme": "TYPE_2_BETTER", "dsc": "Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/linker/ConversionComparator$Comparison;"}]}, "classes/jdk/dynalink/RelinkableCallSite.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/RelinkableCallSite", "super": "java/lang/Object", "mthds": [{"nme": "initialize", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "getDescriptor", "acc": 1025, "dsc": "()Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "relink", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "resetAndRelink", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}], "flds": []}, "classes/jdk/dynalink/BiClassValue$BiClassValues.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/BiClassValue$BiClassValues", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getForwardValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)TT;"}, {"nme": "getReverseValue", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)TT;"}, {"nme": "compute", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/VarHandle;Lja<PERSON>/lang/Class;L<PERSON><PERSON>/util/function/Function;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/VarHandle;Ljava/lang/Class<*>;Ljava/util/function/Function<Ljava/lang/Class<*>;TT;>;)TT;"}, {"nme": "computeForward", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Function;)Ljava/lang/Object;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Function<Ljava/lang/Class<*>;TT;>;)TT;"}, {"nme": "computeReverse", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/util/function/Function;)Ljava/lang/Object;", "sig": "(Ljava/lang/Class<*>;Ljava/util/function/Function<Ljava/lang/Class<*>;TT;>;)TT;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FORWARD", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/VarHandle;"}, {"acc": 26, "nme": "REVERSE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/VarHandle;"}, {"acc": 2, "nme": "forward", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;TT;>;"}, {"acc": 2, "nme": "reverse", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;TT;>;"}]}, "classes/jdk/dynalink/linker/LinkerServices.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/LinkerServices", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "asTypeLosslessReturn", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTypeConverter", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "canConvert", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getGuardedInvocation", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/LinkRequest;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "compareConversion", "acc": 1025, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "filterInternalObjects", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "getWithLookup", "acc": 1025, "dsc": "(Lja<PERSON>/util/function/Supplier;Ljdk/dynalink/SecureLookupSupplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/Supplier<TT;>;Ljdk/dynalink/SecureLookupSupplier;)TT;"}], "flds": []}, "classes/jdk/dynalink/LinkerServicesImpl.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/LinkerServicesImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/TypeConverterFactory;Ljdk/dynalink/linker/GuardingDynamicLinker;Ljdk/dynalink/linker/MethodHandleTransformer;)V"}, {"nme": "canConvert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "asType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTypeConverter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;L<PERSON><PERSON>/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "compareConversion", "acc": 1, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/lang/Class;)Ljdk/dynalink/linker/ConversionComparator$Comparison;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljdk/dynalink/linker/ConversionComparator$Comparison;"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "filterInternalObjects", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "getWithLookup", "acc": 1, "dsc": "(Lja<PERSON>/util/function/Supplier;Ljdk/dynalink/SecureLookupSupplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/Supplier<TT;>;Ljdk/dynalink/SecureLookupSupplier;)TT;"}, {"nme": "getWithLookupInternal", "acc": 10, "dsc": "(Lja<PERSON>/util/function/Supplier;Ljdk/dynalink/SecureLookupSupplier;)Ljava/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/function/Supplier<TT;>;Ljdk/dynalink/SecureLookupSupplier;)TT;"}, {"nme": "getCurrentLookup", "acc": 8, "dsc": "()Ljava/lang/invoke/MethodHandles$Lookup;"}, {"nme": "lambda$getGuardedInvocation$0", "acc": 4098, "dsc": "(Ljdk/dynalink/linker/LinkRequest;)Ljdk/dynalink/linker/GuardedInvocation;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "threadLookupSupplier", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Ljdk/dynalink/SecureLookupSupplier;>;"}, {"acc": 18, "nme": "typeConverterFactory", "dsc": "Ljdk/dynalink/TypeConverterFactory;"}, {"acc": 18, "nme": "topLevelLinker", "dsc": "Ljdk/dynalink/linker/GuardingDynamicLinker;"}, {"acc": 18, "nme": "internalObjectsFilter", "dsc": "Ljdk/dynalink/linker/MethodHandleTransformer;"}]}, "classes/jdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/beans/MissingMemberHandlerFactory;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/beans/MissingMemberHandlerFactory;Ljdk/dynalink/Operation;Ljava/util/List;Ljava/lang/Object;)V", "sig": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/beans/MissingMemberHandlerFactory;Ljdk/dynalink/Operation;Ljava/util/List<Ljdk/dynalink/Namespace;>;Ljava/lang/Object;)V"}, {"nme": "getDescriptor", "acc": 0, "dsc": "()Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "popNamespace", "acc": 0, "dsc": "()Ljdk/dynalink/beans/AbstractJavaLinker$ComponentLinkRequest;"}], "flds": [{"acc": 16, "nme": "linkRequest", "dsc": "Ljdk/dynalink/linker/LinkRequest;"}, {"acc": 16, "nme": "linkerServices", "dsc": "Ljdk/dynalink/linker/LinkerServices;"}, {"acc": 16, "nme": "missingMemberHandlerFactory", "dsc": "Ljdk/dynalink/beans/MissingMemberHandlerFactory;"}, {"acc": 16, "nme": "baseOperation", "dsc": "Ljdk/dynalink/Operation;"}, {"acc": 16, "nme": "namespaces", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/dynalink/Namespace;>;"}, {"acc": 16, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/dynalink/beans/AbstractJavaLinker$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/dynalink/beans/AbstractJavaLinker$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$dynalink$beans$GuardedInvocationComponent$ValidationType", "dsc": "[I"}]}, "classes/jdk/dynalink/BiClassValue$BiClassValuesRoot.class": {"ver": 65, "acc": 48, "nme": "jdk/dynalink/BiClassValue$BiClassValuesRoot", "super": "java/lang/ClassValue", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(Lja<PERSON>/lang/Class;)Ljdk/dynalink/BiClassValue$BiClassValues;", "sig": "(Ljava/lang/Class<*>;)Ljdk/dynalink/BiClassValue$BiClassValues<TT;>;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/dynalink/beans/GuardedInvocationComponent$Validator.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/GuardedInvocationComponent$Validator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/Class;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V", "sig": "(Ljava/lang/Class<*>;Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;)V"}, {"nme": "compose", "acc": 0, "dsc": "(Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;)Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;"}, {"nme": "isAssignableFrom", "acc": 2, "dsc": "(Ljdk/dynalink/beans/GuardedInvocationComponent$Validator;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "validatorClass", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 16, "nme": "validationType", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}]}, "classes/jdk/dynalink/linker/GuardingDynamicLinker.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/GuardingDynamicLinker", "super": "java/lang/Object", "mthds": [{"nme": "getGuardedInvocation", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/jdk/dynalink/beans/ApplicableOverloadedMethods$3.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ApplicableOverloadedMethods$3", "super": "jdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isApplicable", "acc": 0, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}], "flds": []}, "classes/jdk/dynalink/linker/MethodHandleTransformer.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/MethodHandleTransformer", "super": "java/lang/Object", "mthds": [{"nme": "transform", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/dynalink/beans/BeansLinker$1.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeansLinker$1", "super": "java/lang/ClassValue", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/dynalink/linker/TypeBasedGuardingDynamicLinker.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/TypeBasedGuardingDynamicLinker", "super": "java/lang/Object", "mthds": [{"nme": "canLinkType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}], "flds": []}, "classes/jdk/dynalink/beans/StaticClassIntrospector.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/StaticClassIntrospector", "super": "jdk/dynalink/beans/FacetIntrospector", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "getInnerClassGetters", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "getRecordComponentGetters", "acc": 0, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Method;>;"}, {"nme": "edit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "editStaticMethodHandle", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "editConstructorMethodHandle", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "dropReceiver", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}], "flds": []}, "classes/jdk/dynalink/beans/BeansLinker$NoSuchMemberHandlerBindingLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeansLinker$NoSuchMemberHandlerBindingLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;Ljdk/dynalink/beans/MissingMemberHandlerFactory;)V"}, {"nme": "canLinkType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "linker", "dsc": "Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;"}, {"acc": 18, "nme": "missingMemberHandlerFactory", "dsc": "Ljdk/dynalink/beans/MissingMemberHandlerFactory;"}]}, "classes/jdk/dynalink/beans/BeansLinker.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/beans/BeansLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljdk/dynalink/beans/MissingMemberHandlerFactory;)V"}, {"nme": "getLinkerForClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;"}, {"nme": "getStaticLinkerForClass", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;)Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;"}, {"nme": "isDynamicMethod", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isDynamicConstructor", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getConstructorMethod", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/Object;"}, {"nme": "getReadableInstancePropertyNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getWritableInstancePropertyNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getInstanceMethodNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getReadableStaticPropertyNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getWritableStaticPropertyNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getStaticMethodNames", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "linkers", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassValue;", "sig": "Ljava/lang/ClassValue<Ljdk/dynalink/linker/TypeBasedGuardingDynamicLinker;>;"}, {"acc": 18, "nme": "missingMemberHandlerFactory", "dsc": "Ljdk/dynalink/beans/MissingMemberHandlerFactory;"}]}, "classes/jdk/dynalink/beans/DynamicMethodLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/DynamicMethodLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "canLinkType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;"}], "flds": []}, "classes/jdk/dynalink/beans/MaximallySpecific$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/dynalink/beans/MaximallySpecific$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$dynalink$linker$ConversionComparator$Comparison", "dsc": "[I"}]}, "classes/jdk/dynalink/beans/GuardedInvocationComponent$ValidationType.class": {"ver": 65, "acc": 16432, "nme": "jdk/dynalink/beans/GuardedInvocationComponent$ValidationType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "NONE", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"acc": 16409, "nme": "INSTANCE_OF", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"acc": 16409, "nme": "EXACT_CLASS", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"acc": 16409, "nme": "IS_ARRAY", "dsc": "Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/beans/GuardedInvocationComponent$ValidationType;"}]}, "classes/jdk/dynalink/linker/support/Guards.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/linker/support/Guards", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "isOfClass", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "isInstance", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "isInstance", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Ljava/lang/Class<*>;ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "isArray", "acc": 9, "dsc": "(ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getClassBoundArgumentTest", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class;ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(L<PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/Class<*>;ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "asType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "asType", "acc": 9, "dsc": "(Ljdk/dynalink/linker/LinkerServices;<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON><PERSON><PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getTestType", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;"}, {"nme": "asType", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandle;ILjava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getClassGuard", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getInstanceOfGuard", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getIdentityGuard", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "isNull", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "isNotNull", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"nme": "isNull", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isNotNull", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isArray", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "isOfClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;)Z"}, {"nme": "isIdentical", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "constantTrue", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "constantFalse", "acc": 10, "dsc": "(Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "constantBoolean", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Boolean;<PERSON><PERSON><PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "LOG", "dsc": "Ljava/util/logging/Logger;"}, {"acc": 26, "nme": "IS_INSTANCE", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_OF_CLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_ARRAY", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_IDENTICAL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_NULL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "IS_NOT_NULL", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/beans/SingleDynamicMethod.class": {"ver": 65, "acc": 1056, "nme": "jdk/dynalink/beans/SingleDynamicMethod", "super": "jdk/dynalink/beans/DynamicMethod", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isVarArgs", "acc": 1024, "dsc": "()Z"}, {"nme": "getMethodType", "acc": 1024, "dsc": "()Ljava/lang/invoke/MethodType;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1024, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getInvocation", "acc": 0, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "getMethodForExactParamTypes", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/dynalink/beans/SingleDynamicMethod;"}, {"nme": "contains", "acc": 0, "dsc": "(Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}, {"nme": "getMethodNameWithSignature", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodType;Ljava/lang/String;Z)Ljava/lang/String;"}, {"nme": "getInvocation", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Lja<PERSON>/lang/invoke/MethodType;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "canConvertTo", "acc": 10, "dsc": "(Ljdk/dynalink/linker/LinkerServices;<PERSON>ja<PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(Ljdk/dynalink/linker/LinkerServices;Ljava/lang/Class<*>;Ljava/lang/Object;)Z"}, {"nme": "collectArguments", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;I)Ljava/lang/invoke/MethodHandle;"}, {"nme": "createConvertingInvocation", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandle;Ljdk/dynalink/linker/LinkerServices;L<PERSON><PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "typeMatchesDescription", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/invoke/MethodType;)Z"}, {"nme": "typeNameMatches", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;)Z"}, {"nme": "lambda$getInvocation$0", "acc": 4098, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Ljdk/dynalink/linker/LinkerServices;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CAN_CONVERT_TO", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/CallSiteDescriptor.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/CallSiteDescriptor", "super": "jdk/dynalink/SecureLookupSupplier", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/invoke/MethodHandles$Lookup;Ljdk/dynalink/Operation;Ljava/lang/invoke/MethodType;)V"}, {"nme": "getOperation", "acc": 17, "dsc": "()Ljdk/dynalink/Operation;"}, {"nme": "getMethodType", "acc": 17, "dsc": "()Ljava/lang/invoke/MethodType;"}, {"nme": "changeMethodType", "acc": 17, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodType;)Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "changeMethodTypeInternal", "acc": 4, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodType;)Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "changeOperation", "acc": 17, "dsc": "(Ljdk/dynalink/Operation;)Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "changeOperationInternal", "acc": 4, "dsc": "(Ljdk/dynalink/Operation;)Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lookupsEqual", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/invoke/MethodHandles$Lookup;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "lookupHashCode", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "assertChangeInvariants", "acc": 2, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;Lja<PERSON>/lang/String;)V"}, {"nme": "alwaysAssert", "acc": 10, "dsc": "(ZLjava/util/function/Supplier;)V", "sig": "(ZLjava/util/function/Supplier<Ljava/lang/String;>;)V"}, {"nme": "lambda$assertChangeInvariants$6", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$assertChangeInvariants$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$assertChangeInvariants$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$changeOperation$3", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$changeOperation$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$changeMethodType$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$changeMethodType$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "operation", "dsc": "Ljdk/dynalink/Operation;"}, {"acc": 18, "nme": "methodType", "dsc": "Ljava/lang/invoke/MethodType;"}]}, "classes/jdk/dynalink/beans/StaticClass.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/beans/StaticClass", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "forClass", "acc": 9, "dsc": "(Ljava/lang/Class;)Ljdk/dynalink/beans/StaticClass;", "sig": "(Ljava/lang/Class<*>;)Ljdk/dynalink/beans/StaticClass;"}, {"nme": "getRepresentedClass", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<*>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "readResolve", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "staticClasses", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassValue;", "sig": "Ljava/lang/ClassValue<Ljdk/dynalink/beans/StaticClass;>;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}, {"acc": 18, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "classes/jdk/dynalink/beans/ClassLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ClassLinker", "super": "jdk/dynalink/beans/BeanLinker", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "FOR_CLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/beans/StaticClassLinker.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/StaticClassLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getConstructorMethod", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/String;)Ljava/lang/Object;"}, {"nme": "getReadableStaticPropertyNames", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getWritableStaticPropertyNames", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getStaticMethodNames", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "getGuardedInvocation", "acc": 1, "dsc": "(Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}, {"nme": "canLinkType", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "isClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "linkers", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassValue;", "sig": "Ljava/lang/ClassValue<Ljdk/dynalink/beans/StaticClassLinker$SingleClassStaticsLinker;>;"}, {"acc": 24, "nme": "GET_CLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "IS_CLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 24, "nme": "ARRAY_CTOR", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/BiClassValue$RetentionDirection.class": {"ver": 65, "acc": 16432, "nme": "jdk/dynalink/BiClassValue$RetentionDirection", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "FORWARD", "dsc": "Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"acc": 16409, "nme": "REVERSE", "dsc": "Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"acc": 16409, "nme": "NEITHER", "dsc": "Ljdk/dynalink/BiClassValue$RetentionDirection;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/BiClassValue$RetentionDirection;"}]}, "classes/jdk/dynalink/beans/BeanIntrospector.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/BeanIntrospector", "super": "jdk/dynalink/beans/FacetIntrospector", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V"}, {"nme": "getInnerClassGetters", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "getRecordComponentGetters", "acc": 0, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Method;>;"}, {"nme": "edit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [{"acc": 18, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}]}, "classes/jdk/dynalink/beans/FacetIntrospector.class": {"ver": 65, "acc": 1056, "nme": "jdk/dynalink/beans/FacetIntrospector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Z)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;Z)V"}, {"nme": "getInnerClassGetters", "acc": 1024, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/invoke/MethodHandle;>;"}, {"nme": "getRecordComponentGetters", "acc": 1024, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Method;>;"}, {"nme": "getFields", "acc": 0, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Field;>;"}, {"nme": "isAccessible", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Member;)Z"}, {"nme": "getMethods", "acc": 0, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Ljava/lang/reflect/Method;>;"}, {"nme": "unreflectGetter", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectSetter", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "edit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}], "flds": [{"acc": 18, "nme": "clazz", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "instance", "dsc": "Z"}, {"acc": 18, "nme": "isRestricted", "dsc": "Z"}, {"acc": 20, "nme": "membersLookup", "dsc": "Ljdk/dynalink/beans/AccessibleMembersLookup;"}]}, "classes/jdk/dynalink/linker/GuardedInvocationTransformer.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/GuardedInvocationTransformer", "super": "java/lang/Object", "mthds": [{"nme": "filter", "acc": 1025, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;Ljdk/dynalink/linker/LinkRequest;Ljdk/dynalink/linker/LinkerServices;)Ljdk/dynalink/linker/GuardedInvocation;"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/FunctionalInterface;"}]}, "classes/jdk/dynalink/linker/support/TypeUtilities.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/linker/support/TypeUtilities", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "createWrapperTypes", "acc": 10, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Class<*>;>;"}, {"nme": "createClassNameMapping", "acc": 10, "dsc": "(Lja<PERSON>/util/Collection;)Ljava/util/Map;", "sig": "(Ljava/util/Collection<Ljava/lang/Class<*>;>;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"nme": "invertMap", "acc": 10, "dsc": "(Ljava/util/Map;)Ljava/util/Map;", "sig": "<K:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/util/Map<TK;TV;>;)Ljava/util/Map<TV;TK;>;"}, {"nme": "isMethodInvocationConvertible", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isBoxingAndWideningReferenceConversion", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isConvertibleWithoutLoss", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isSubtype", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isProperPrimitiveSubtype", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "isProperPrimitiveLosslessSubtype", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "getPrimitiveTypeByName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;"}, {"nme": "getPrimitiveType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getWrapperType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "isWrapperType", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "WRAPPER_TYPES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "PRIMITIVE_TYPES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Class<*>;>;"}, {"acc": 26, "nme": "PRIMITIVE_TYPES_BY_NAME", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/dynalink/support/ChainedCallSite.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/support/ChainedCallSite", "super": "jdk/dynalink/support/AbstractRelinkableCallSite", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;)V"}, {"nme": "getMaxChain<PERSON>ength", "acc": 4, "dsc": "()I"}, {"nme": "relink", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "resetAndRelink", "acc": 1, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;L<PERSON><PERSON>/lang/invoke/MethodHandle;)V"}, {"nme": "relinkInternal", "acc": 2, "dsc": "(Ljdk/dynalink/linker/GuardedInvocation;Lja<PERSON>/lang/invoke/MethodHandle;ZZ)Ljava/lang/invoke/MethodHandle;"}, {"nme": "checkMaxChain<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "(I)I"}, {"nme": "makePruneAndInvokeMethod", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;<PERSON>ja<PERSON>/lang/invoke/MethodHandle;)Lja<PERSON>/lang/invoke/MethodHandle;"}, {"nme": "prune", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;Z)Ljava/lang/invoke/MethodHandle;"}, {"nme": "lambda$relinkInternal$0", "acc": 4106, "dsc": "(ZLjdk/dynalink/linker/GuardedInvocation;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PRUNE_CATCHES", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 26, "nme": "PRUNE_SWITCHPOINTS", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}, {"acc": 2, "nme": "invocations", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/jdk/dynalink/linker/LinkRequest.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/LinkRequest", "super": "java/lang/Object", "mthds": [{"nme": "getCallSiteDescriptor", "acc": 1025, "dsc": "()Ljdk/dynalink/CallSiteDescriptor;"}, {"nme": "getArguments", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getReceiver", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "isCallSiteUnstable", "acc": 1025, "dsc": "()Z"}, {"nme": "replaceArguments", "acc": 1153, "dsc": "(Ljdk/dynalink/CallSiteDescriptor;[<PERSON><PERSON><PERSON>/lang/Object;)Ljdk/dynalink/linker/LinkRequest;"}], "flds": []}, "classes/jdk/dynalink/internal/InternalTypeUtilities.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/internal/InternalTypeUtilities", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "areAssignable", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Z", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/Class<*>;)Z"}, {"nme": "canReferenceDirectly", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Z"}, {"nme": "getCommonLosslessConversionType", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/lang/Class;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getMostSpecificCommonTypeUnequalNonprimitives", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/lang/Class;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/lang/Class<*>;"}, {"nme": "getAssignables", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)Ljava/util/Set;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)Ljava/util/Set<Ljava/lang/Class<*>;>;"}, {"nme": "collectAssignables", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/util/Set<Ljava/lang/Class<*>;>;)V"}], "flds": []}, "classes/jdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest.class": {"ver": 65, "acc": 1056, "nme": "jdk/dynalink/beans/ApplicableOverloadedMethods$ApplicabilityTest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "isApplicable", "acc": 1024, "dsc": "(Ljava/lang/invoke/MethodType;Ljdk/dynalink/beans/SingleDynamicMethod;)Z"}], "flds": []}, "classes/jdk/dynalink/linker/support/Lookup.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/linker/support/Lookup", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;)V"}, {"nme": "unreflect", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflect", "acc": 9, "dsc": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectGetter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findGetter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectSetter", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Field;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectConstructor", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/reflect/Constructor<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "unreflectConstructor", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Constructor;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/reflect/Constructor<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findSpecial", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "methodDescription", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/String;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/String;"}, {"nme": "findStatic", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findVirtual", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findOwnSpecial", "acc": 137, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findOwnSpecial", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findOwnStatic", "acc": 137, "dsc": "(L<PERSON><PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;", "sig": "(Lja<PERSON>/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "findOwnStatic", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Class;[Ljava/lang/Class;)Lja<PERSON>/lang/invoke/MethodHandle;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/Class<*>;[Ljava/lang/Class<*>;)Ljava/lang/invoke/MethodHandle;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "lookup", "dsc": "Ljava/lang/invoke/MethodHandles$Lookup;"}, {"acc": 25, "nme": "PUBLIC", "dsc": "Ljdk/dynalink/linker/support/Lookup;"}]}, "classes/jdk/dynalink/NoSuchDynamicMethodException.class": {"ver": 65, "acc": 33, "nme": "jdk/dynalink/NoSuchDynamicMethodException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "classes/jdk/dynalink/linker/GuardingTypeConverterFactory.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/linker/GuardingTypeConverterFactory", "super": "java/lang/Object", "mthds": [{"nme": "convertToType", "acc": 1025, "dsc": "(Lja<PERSON>/lang/Class;Ljava/lang/Class;Ljava/util/function/Supplier;)Ljdk/dynalink/linker/GuardedInvocation;", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;Ljava/util/function/Supplier<Ljava/lang/invoke/MethodHandles$Lookup;>;)Ljdk/dynalink/linker/GuardedInvocation;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/jdk/dynalink/Operation.class": {"ver": 65, "acc": 1537, "nme": "jdk/dynalink/Operation", "super": "java/lang/Object", "mthds": [{"nme": "withNamespace", "acc": 1, "dsc": "(Ljdk/dynalink/Namespace;)Ljdk/dynalink/NamespaceOperation;"}, {"nme": "withNamespaces", "acc": 129, "dsc": "([Ljdk/dynalink/Namespace;)Ljdk/dynalink/NamespaceOperation;"}, {"nme": "named", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljdk/dynalink/NamedOperation;"}], "flds": []}, "classes/jdk/dynalink/beans/ClassString$1.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/beans/ClassString$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": []}, "classes/jdk/dynalink/DynamicLinker.class": {"ver": 65, "acc": 49, "nme": "jdk/dynalink/DynamicLinker", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/dynalink/linker/LinkerServices;Ljdk/dynalink/linker/GuardedInvocationTransformer;ZI)V"}, {"nme": "link", "acc": 1, "dsc": "(Ljdk/dynalink/RelinkableCallSite;)Ljdk/dynalink/RelinkableCallSite;", "sig": "<T::Ljdk/dynalink/RelinkableCallSite;>(TT;)TT;"}, {"nme": "getLinkerServices", "acc": 1, "dsc": "()Ljdk/dynalink/linker/LinkerServices;"}, {"nme": "createRelinkAndInvokeMethod", "acc": 2, "dsc": "(Ljdk/dynalink/RelinkableCallSite;I)Ljava/lang/invoke/MethodHandle;"}, {"nme": "relink", "acc": 130, "dsc": "(Ljdk/dynalink/RelinkableCallSite;I[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/invoke/MethodHandle;", "exs": ["java/lang/Exception"]}, {"nme": "getLinkedCallSiteLocation", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/StackTraceElement;"}, {"nme": "isInitialLinkFrame", "acc": 10, "dsc": "(Lja<PERSON>/lang/StackWalker$StackFrame;)Z"}, {"nme": "isRelinkFrame", "acc": 10, "dsc": "(Lja<PERSON>/lang/StackWalker$StackFrame;)Z"}, {"nme": "testFrame", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/StackWalker$StackFrame;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$getLinkedCallSiteLocation$2", "acc": 4106, "dsc": "(Lja<PERSON>/util/stream/Stream;)Ljava/lang/StackTraceElement;"}, {"nme": "lambda$getLinkedCallSiteLocation$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/StackWalker$StackFrame;)Z"}, {"nme": "lambda$getLinkedCallSiteLocation$0", "acc": 4106, "dsc": "(Lja<PERSON>/lang/StackWalker$StackFrame;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "RELINK_METHOD_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "relink"}, {"acc": 26, "nme": "INITIAL_LINK_CLASS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "java.lang.invoke.MethodHandleNatives"}, {"acc": 26, "nme": "INITIAL_LINK_METHOD_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "linkCallSite"}, {"acc": 26, "nme": "INVOKE_PACKAGE_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "java.lang.invoke."}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/<PERSON>ack<PERSON>;"}, {"acc": 18, "nme": "linkerServices", "dsc": "Ljdk/dynalink/linker/LinkerServices;"}, {"acc": 18, "nme": "prelinkTransformer", "dsc": "Ljdk/dynalink/linker/GuardedInvocationTransformer;"}, {"acc": 18, "nme": "sync<PERSON>n<PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 18, "nme": "unstableRelinkThreshold", "dsc": "I"}, {"acc": 26, "nme": "RELINK", "dsc": "<PERSON><PERSON><PERSON>/lang/invoke/MethodHandle;"}]}, "classes/jdk/dynalink/linker/GuardingDynamicLinkerExporter.class": {"ver": 65, "acc": 1057, "nme": "jdk/dynalink/linker/GuardingDynamicLinkerExporter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "AUTOLOAD_PERMISSION_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dynalink.exportLinkersAutomatically"}, {"acc": 26, "nme": "AUTOLOAD_PERMISSION", "dsc": "Ljava/security/Permission;"}]}, "classes/jdk/dynalink/StandardOperation.class": {"ver": 65, "acc": 16433, "nme": "jdk/dynalink/StandardOperation", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/dynalink/StandardOperation;"}, {"nme": "valueOf", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/dynalink/StandardOperation;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/dynalink/StandardOperation;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "GET", "dsc": "Ljdk/dynalink/StandardOperation;"}, {"acc": 16409, "nme": "SET", "dsc": "Ljdk/dynalink/StandardOperation;"}, {"acc": 16409, "nme": "REMOVE", "dsc": "Ljdk/dynalink/StandardOperation;"}, {"acc": 16409, "nme": "CALL", "dsc": "Ljdk/dynalink/StandardOperation;"}, {"acc": 16409, "nme": "NEW", "dsc": "Ljdk/dynalink/StandardOperation;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/dynalink/StandardOperation;"}]}, "classes/jdk/dynalink/TypeConverterFactory$LookupSupplier.class": {"ver": 65, "acc": 32, "nme": "jdk/dynalink/TypeConverterFactory$LookupSupplier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "()Ljava/lang/invoke/MethodHandles$Lookup;"}, {"nme": "get", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 64, "nme": "returned<PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 64, "nme": "closed", "dsc": "Z"}]}}}}