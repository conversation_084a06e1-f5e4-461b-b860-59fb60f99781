# 测试命名牌保护功能

## 测试准备

1. **编译插件**
   ```bash
   ./gradlew build
   ```

2. **安装插件**
   - 将生成的 jar 文件放入服务器的 plugins 目录
   - 重启服务器或使用 `/reload` 命令

3. **启用日志记录**
   - 编辑 `config.yml`
   - 设置 `enable-logging: true`
   - 设置 `debug-mode: true`（可选，用于详细调试）

## 测试步骤

### 测试1：基本命名牌保护

1. **生成精灵**
   ```
   /pokespawn pikachu
   ```

2. **给精灵命名**
   - 制作命名牌：`/give @s name_tag`
   - 在铁砧上重命名为"我的皮卡丘"
   - 右键点击精灵使用命名牌

3. **触发清理**
   ```
   /pokeclean now
   ```

4. **验证结果**
   - 精灵应该仍然存在
   - 检查日志中是否有："精灵有命名牌，不清理: 我的皮卡丘 (Pikachu) [有命名牌]"

### 测试2：无命名牌精灵清理

1. **生成精灵**
   ```
   /pokespawn pikachu
   ```

2. **不使用命名牌**
   - 保持精灵为默认状态

3. **触发清理**
   ```
   /pokeclean now
   ```

4. **验证结果**
   - 精灵应该被清理（如果满足其他清理条件）
   - 检查日志中的清理记录

### 测试3：空命名牌保护

1. **生成精灵**
   ```
   /pokespawn pikachu
   ```

2. **使用空命名牌**
   - 制作命名牌：`/give @s name_tag`
   - 在铁砧上不输入任何名称（保持空白）
   - 右键点击精灵使用命名牌

3. **触发清理**
   ```
   /pokeclean now
   ```

4. **验证结果**
   - 精灵应该仍然存在
   - 检查日志中是否有："精灵有命名牌，不清理: (Pikachu) [有命名牌]"

### 测试4：调试信息验证

1. **启用调试模式**
   ```yaml
   debug-mode: true
   enable-logging: true
   ```

2. **检查精灵信息**
   ```
   /pokeclean debug <精灵附近>
   ```

3. **验证调试输出**
   - 查看是否包含："是否有命名牌: true/false"
   - 查看精灵信息标签是否正确显示

## 预期结果

### 有命名牌的精灵
```
[INFO] 精灵有命名牌，不清理: 我的皮卡丘 (Pikachu) [有命名牌]
```

### 无命名牌的精灵
```
[INFO] 清理了精灵: Pikachu 在世界 world
```

### 调试信息示例
```
实体类型: UNKNOWN
实体名称: Pikachu
自定义名称: 我的皮卡丘
是否有命名牌: true
是否为Cobblemon精灵: true
精灵种类: Pikachu
```

## 故障排除

### 问题1：命名牌不生效
- **检查**：确认命名牌正确应用到精灵
- **解决**：重新使用命名牌，确保右键点击精灵

### 问题2：日志没有显示
- **检查**：确认 `enable-logging: true`
- **解决**：重新加载配置或重启插件

### 问题3：精灵仍被清理
- **检查**：确认精灵确实有自定义名称
- **解决**：使用 `/pokeclean debug` 检查精灵状态

## 配置示例

```yaml
# config.yml
enable-logging: true
debug-mode: true
max-clean-per-run: 50
clean-interval: 300
max-distance: 100.0

# 其他清理设置保持不变
excluded-pokemon:
  - "shiny"
  - "legendary"
  - "mythical"

# 命名牌保护自动启用，无需额外配置
```

## 性能考虑

- 命名牌检查是轻量级操作
- 检查在排除列表之前进行，提高效率
- 对服务器性能影响极小

## 兼容性测试

1. **与现有配置兼容**
   - 测试各种排除设置
   - 验证高级设置仍然有效

2. **与其他插件兼容**
   - 测试与其他精灵相关插件的兼容性
   - 验证命名牌功能不冲突

3. **版本兼容性**
   - 测试不同 Minecraft 版本
   - 验证 Bukkit API 兼容性
