/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.cobblemon.mod.common.entity.pokemon.PokemonEntity
 *  com.cobblemon.mod.common.pokemon.Pokemon
 *  kotlin.Metadata
 *  kotlin.jvm.functions.Function1
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.text.StringsKt
 *  net.minecraft.class_1297
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.utils;

import com.cobblemon.mod.common.entity.pokemon.PokemonEntity;
import com.cobblemon.mod.common.pokemon.Pokemon;
import com.pokeskies.skiesclear.config.clearables.CobblemonClearable;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;
import net.minecraft.class_1297;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ\u001d\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u000b\u0010\f\u00a8\u0006\r"}, d2={"Lcom/pokeskies/skiesclear/utils/CobblemonAdaptor;", "", "<init>", "()V", "Lnet/minecraft/class_1297;", "entity", "", "isEntityType", "(Lnet/minecraft/class_1297;)Z", "Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;", "clearable", "shouldClearEntity", "(Lcom/pokeskies/skiesclear/config/clearables/CobblemonClearable;Lnet/minecraft/class_1297;)Z", "SkiesClear"})
public final class CobblemonAdaptor {
    @NotNull
    public static final CobblemonAdaptor INSTANCE = new CobblemonAdaptor();

    private CobblemonAdaptor() {
    }

    public final boolean isEntityType(@NotNull class_1297 entity) {
        Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
        return entity instanceof PokemonEntity;
    }

    public final boolean shouldClearEntity(@NotNull CobblemonClearable clearable, @NotNull class_1297 entity) {
        List<String> aspects;
        Intrinsics.checkNotNullParameter((Object)clearable, (String)"clearable");
        Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
        if (!(entity instanceof PokemonEntity)) {
            return false;
        }
        Pokemon pokemon = ((PokemonEntity)entity).getPokemon();
        if (!((Collection)clearable.getBlacklist()).isEmpty()) {
            if (pokemon.isPlayerOwned() && clearable.getBlacklist().contains("#owned")) {
                return false;
            }
            if (((PokemonEntity)entity).isBattling() && clearable.getBlacklist().contains("#battling")) {
                return false;
            }
            if (pokemon.getShiny() && clearable.getBlacklist().contains("#shiny")) {
                return false;
            }
            if (pokemon.isLegendary() && clearable.getBlacklist().contains("#legendary")) {
                return false;
            }
            if (pokemon.isUltraBeast() && clearable.getBlacklist().contains("#ultrabeast")) {
                return false;
            }
            if (((PokemonEntity)entity).isBusy() && clearable.getBlacklist().contains("#busy")) {
                return false;
            }
            if (((PokemonEntity)entity).isUncatchable() && clearable.getBlacklist().contains("#uncatchable")) {
                return false;
            }
            if (clearable.getBlacklist().stream().anyMatch(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$1(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$0(pokemon, arg_0), arg_0))) {
                return false;
            }
            aspects = clearable.getBlacklistedAspects();
            if (!((Collection)aspects).isEmpty() && !((Collection)pokemon.getAspects()).isEmpty() && pokemon.getAspects().stream().anyMatch(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$3(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$2(aspects, arg_0), arg_0))) {
                return false;
            }
        }
        if (!((Collection)clearable.getWhitelist()).isEmpty()) {
            if (pokemon.isPlayerOwned() && clearable.getWhitelist().contains("#owned")) {
                return true;
            }
            if (((PokemonEntity)entity).isBattling() && clearable.getWhitelist().contains("#battling")) {
                return true;
            }
            if (pokemon.getShiny() && clearable.getWhitelist().contains("#shiny")) {
                return true;
            }
            if (pokemon.isLegendary() && clearable.getWhitelist().contains("#legendary")) {
                return true;
            }
            if (pokemon.isUltraBeast() && clearable.getWhitelist().contains("#ultrabeast")) {
                return true;
            }
            if (((PokemonEntity)entity).isBusy() && clearable.getBlacklist().contains("#busy")) {
                return true;
            }
            if (((PokemonEntity)entity).isUncatchable() && clearable.getBlacklist().contains("#uncatchable")) {
                return true;
            }
            if (clearable.getWhitelist().stream().anyMatch(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$5(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$4(pokemon, arg_0), arg_0))) {
                return true;
            }
            aspects = clearable.getWhitelistedAspects();
            return !((Collection)aspects).isEmpty() && !((Collection)pokemon.getAspects()).isEmpty() && pokemon.getAspects().stream().anyMatch(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$7(arg_0 -> CobblemonAdaptor.shouldClearEntity$lambda$6(aspects, arg_0), arg_0));
        }
        return true;
    }

    private static final boolean shouldClearEntity$lambda$0(Pokemon $pokemon, String species) {
        Intrinsics.checkNotNullParameter((Object)$pokemon, (String)"$pokemon");
        Intrinsics.checkNotNullParameter((Object)species, (String)"species");
        return StringsKt.equals((String)$pokemon.getSpecies().getResourceIdentifier().toString(), (String)species, (boolean)true);
    }

    private static final boolean shouldClearEntity$lambda$1(Function1 $tmp0, Object p0) {
        Intrinsics.checkNotNullParameter((Object)$tmp0, (String)"$tmp0");
        return (Boolean)$tmp0.invoke(p0);
    }

    private static final boolean shouldClearEntity$lambda$2(List $aspects, String pokemonAspect) {
        Intrinsics.checkNotNullParameter((Object)$aspects, (String)"$aspects");
        Intrinsics.checkNotNullParameter((Object)pokemonAspect, (String)"pokemonAspect");
        return $aspects.contains(pokemonAspect);
    }

    private static final boolean shouldClearEntity$lambda$3(Function1 $tmp0, Object p0) {
        Intrinsics.checkNotNullParameter((Object)$tmp0, (String)"$tmp0");
        return (Boolean)$tmp0.invoke(p0);
    }

    private static final boolean shouldClearEntity$lambda$4(Pokemon $pokemon, String species) {
        Intrinsics.checkNotNullParameter((Object)$pokemon, (String)"$pokemon");
        Intrinsics.checkNotNullParameter((Object)species, (String)"species");
        return StringsKt.equals((String)$pokemon.getSpecies().getResourceIdentifier().toString(), (String)species, (boolean)true);
    }

    private static final boolean shouldClearEntity$lambda$5(Function1 $tmp0, Object p0) {
        Intrinsics.checkNotNullParameter((Object)$tmp0, (String)"$tmp0");
        return (Boolean)$tmp0.invoke(p0);
    }

    private static final boolean shouldClearEntity$lambda$6(List $aspects, String pokemonAspect) {
        Intrinsics.checkNotNullParameter((Object)$aspects, (String)"$aspects");
        Intrinsics.checkNotNullParameter((Object)pokemonAspect, (String)"pokemonAspect");
        return $aspects.contains(pokemonAspect);
    }

    private static final boolean shouldClearEntity$lambda$7(Function1 $tmp0, Object p0) {
        Intrinsics.checkNotNullParameter((Object)$tmp0, (String)"$tmp0");
        return (Boolean)$tmp0.invoke(p0);
    }
}

