/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.brigadier.arguments.ArgumentType
 *  com.mojang.brigadier.arguments.StringArgumentType
 *  com.mojang.brigadier.builder.LiteralArgumentBuilder
 *  com.mojang.brigadier.context.CommandContext
 *  com.mojang.brigadier.suggestion.SuggestionsBuilder
 *  com.mojang.brigadier.tree.LiteralCommandNode
 *  kotlin.Metadata
 *  kotlin.Pair
 *  kotlin.TuplesKt
 *  kotlin.collections.MapsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.jvm.internal.SourceDebugExtension
 *  kotlin.text.StringsKt
 *  me.lucko.fabric.api.permissions.v0.Permissions
 *  net.kyori.adventure.text.ComponentLike
 *  net.minecraft.class_124
 *  net.minecraft.class_2168
 *  net.minecraft.class_2170
 *  net.minecraft.class_2172
 *  net.minecraft.class_2561
 *  net.minecraft.class_2583
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.commands.subcommands;

import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import com.mojang.brigadier.tree.LiteralCommandNode;
import com.pokeskies.skiesclear.ClearTask;
import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.config.ClearConfig;
import com.pokeskies.skiesclear.config.ConfigManager;
import com.pokeskies.skiesclear.utils.SubCommand;
import com.pokeskies.skiesclear.utils.Utils;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.TuplesKt;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import me.lucko.fabric.api.permissions.v0.Permissions;
import net.kyori.adventure.text.ComponentLike;
import net.minecraft.class_124;
import net.minecraft.class_2168;
import net.minecraft.class_2170;
import net.minecraft.class_2172;
import net.minecraft.class_2561;
import net.minecraft.class_2583;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \b2\u00020\u0001:\u0001\bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u0016\u00a2\u0006\u0004\b\u0006\u0010\u0007\u00a8\u0006\t"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/InfoCommand;", "Lcom/pokeskies/skiesclear/utils/SubCommand;", "<init>", "()V", "Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Lnet/minecraft/class_2168;", "build", "()Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Companion", "SkiesClear"})
public final class InfoCommand
implements SubCommand {
    @NotNull
    public static final Companion Companion = new Companion(null);

    @Override
    @NotNull
    public LiteralCommandNode<class_2168> build() {
        LiteralCommandNode literalCommandNode = ((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)class_2170.method_9247((String)"info").requires(Permissions.require((String)"skiesclear.command.info", (int)4))).then(class_2170.method_9244((String)"id", (ArgumentType)((ArgumentType)StringArgumentType.string())).suggests(InfoCommand::build$lambda$0).executes(InfoCommand::build$lambda$1))).executes(InfoCommand::build$lambda$2)).build();
        Intrinsics.checkNotNullExpressionValue((Object)literalCommandNode, (String)"build(...)");
        return literalCommandNode;
    }

    private static final CompletableFuture build$lambda$0(CommandContext ctx, SuggestionsBuilder builder) {
        return class_2172.method_9265((Iterable)ConfigManager.Companion.getCONFIG().getClears().keySet(), (SuggestionsBuilder)builder);
    }

    private static final int build$lambda$1(CommandContext ctx) {
        Intrinsics.checkNotNull((Object)ctx);
        return InfoCommand.Companion.execute((CommandContext<class_2168>)ctx, StringArgumentType.getString((CommandContext)ctx, (String)"id"));
    }

    private static final int build$lambda$2(CommandContext ctx) {
        Intrinsics.checkNotNull((Object)ctx);
        return InfoCommand.Companion.execute((CommandContext<class_2168>)ctx, null);
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J'\u0010\n\u001a\u00020\t2\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\b\u0010\b\u001a\u0004\u0018\u00010\u0007H\u0002\u00a2\u0006\u0004\b\n\u0010\u000b\u00a8\u0006\f"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/InfoCommand$Companion;", "", "<init>", "()V", "Lcom/mojang/brigadier/context/CommandContext;", "Lnet/minecraft/class_2168;", "ctx", "", "id", "", "execute", "(Lcom/mojang/brigadier/context/CommandContext;Ljava/lang/String;)I", "SkiesClear"})
    @SourceDebugExtension(value={"SMAP\nInfoCommand.kt\nKotlin\n*S Kotlin\n*F\n+ 1 InfoCommand.kt\ncom/pokeskies/skiesclear/commands/subcommands/InfoCommand$Companion\n+ 2 Maps.kt\nkotlin/collections/MapsKt__MapsKt\n*L\n1#1,88:1\n535#2:89\n520#2,6:90\n*S KotlinDebug\n*F\n+ 1 InfoCommand.kt\ncom/pokeskies/skiesclear/commands/subcommands/InfoCommand$Companion\n*L\n40#1:89\n40#1:90,6\n*E\n"})
    public static final class Companion {
        private Companion() {
        }

        /*
         * WARNING - void declaration
         */
        private final int execute(CommandContext<class_2168> ctx, String id) {
            void $this$filterTo$iv$iv;
            Map<String, ClearConfig> $this$filter$iv = ConfigManager.Companion.getCONFIG().getClears();
            boolean $i$f$filter = false;
            Map<String, ClearConfig> map = $this$filter$iv;
            Map destination$iv$iv = new LinkedHashMap();
            boolean $i$f$filterTo = false;
            Iterator<Object> iterator = $this$filterTo$iv$iv.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry element$iv$iv;
                Map.Entry it = element$iv$iv = iterator.next();
                boolean bl = false;
                if (!((ClearConfig)it.getValue()).getEnabled()) continue;
                destination$iv$iv.put(element$iv$iv.getKey(), element$iv$iv.getValue());
            }
            Map clearEntries = destination$iv$iv;
            if (id != null) {
                ClearConfig clearConfig = ConfigManager.Companion.getCONFIG().getClears().get(id);
                if (clearConfig == null) {
                    ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("Clear with id " + id + " not found.")).method_27694(Companion::execute$lambda$1));
                    return 0;
                }
                if (!clearConfig.getEnabled()) {
                    ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("The Clear entry " + id + " is disabled.")).method_27694(Companion::execute$lambda$2));
                    return 0;
                }
                ClearTask clearTask = SkiesClear.Companion.getINSTANCE().getClearManager().getClearTask(id);
                if (clearTask == null) {
                    ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("Clear Task for " + id + " not found. Is it disabled?")).method_27694(Companion::execute$lambda$3));
                    return 0;
                }
                clearEntries = MapsKt.mapOf((Pair)TuplesKt.to((Object)id, (Object)clearConfig));
            }
            if (clearEntries.isEmpty()) {
                ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)"No enabled clear entries found.").method_27694(Companion::execute$lambda$4));
                return 0;
            }
            for (Map.Entry entry : clearEntries.entrySet()) {
                String clearId = (String)entry.getKey();
                ClearConfig clearConfig = (ClearConfig)entry.getValue();
                if (SkiesClear.Companion.getINSTANCE().getClearManager().getClearTask(clearId) == null) continue;
                for (String line : clearConfig.getMessages().getInfo()) {
                    ClearTask clearTask;
                    ((class_2168)ctx.getSource()).sendMessage((ComponentLike)Utils.INSTANCE.deserializeText(StringsKt.replace$default((String)line, (String)"%time_remaining%", (String)Utils.INSTANCE.getFormattedTime(clearTask.getTimer()), (boolean)false, (int)4, null)));
                }
            }
            return 1;
        }

        private static final class_2583 execute$lambda$1(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 execute$lambda$2(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 execute$lambda$3(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 execute$lambda$4(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

