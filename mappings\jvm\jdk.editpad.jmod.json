{"md5": "c56862dfeccc491f69cfec3a0274f1b6", "sha2": "bade169335c028f2fb4b8d2098c1f0bf5e1c5099", "sha256": "92ba6a4054efa2492a669a812684d7cba65272b20dcfe76bfab88f5326228587", "contents": {"classes": {"classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/editpad/EditPad$1.class": {"ver": 65, "acc": 32, "nme": "jdk/editpad/EditPad$1", "super": "java/awt/event/WindowAdapter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/editpad/EditPad;Ljava/lang/Runnable;)V"}, {"nme": "windowClosing", "acc": 1, "dsc": "(Ljava/awt/event/WindowEvent;)V"}], "flds": [{"acc": 4112, "nme": "val$closer", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}]}, "classes/jdk/editpad/EditPad.class": {"ver": 65, "acc": 32, "nme": "jdk/editpad/EditPad", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljava/util/function/Consumer;Ljava/lang/String;Ljava/lang/Runnable;Ljava/util/function/Consumer;)V", "sig": "(Ljava/lang/String;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/lang/String;Ljava/lang/Runnable;Ljava/util/function/Consumer<Ljava/lang/String;>;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "buttons", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;Ljavax/swing/JTextArea;)Ljavax/swing/JPanel;"}, {"nme": "addButton", "acc": 2, "dsc": "(Ljavax/swing/JPanel;<PERSON><PERSON><PERSON>/lang/String;ILjava/awt/event/ActionListener;)V"}, {"nme": "getResourceString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "error", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "lambda$buttons$3", "acc": 4098, "dsc": "(Ljavax/swing/JTextArea;<PERSON><PERSON><PERSON>/lang/Runnable;Ljava/awt/event/ActionEvent;)V"}, {"nme": "lambda$buttons$2", "acc": 4098, "dsc": "(Ljavax/swing/JTextArea;Ljava/awt/event/ActionEvent;)V"}, {"nme": "lambda$buttons$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;Ljava/awt/event/ActionEvent;)V"}, {"nme": "lambda$run$0", "acc": 4098, "dsc": "(Ljavax/swing/JFrame;)V"}], "flds": [{"acc": 26, "nme": "L10N_RB_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.editpad.resources.l10n"}, {"acc": 2, "nme": "rb", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 18, "nme": "windowLabel", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 18, "nme": "initialText", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "closeMark", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 18, "nme": "saveHandler", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}]}, "classes/jdk/editpad/EditPadProvider.class": {"ver": 65, "acc": 33, "nme": "jdk/editpad/EditPadProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "rank", "acc": 1, "dsc": "()I"}, {"nme": "edit", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/function/Consumer;Ljava/util/function/Consumer;)V", "sig": "(Ljava/lang/String;Ljava/lang/String;Ljava/util/function/Consumer<Ljava/lang/String;>;Ljava/util/function/Consumer<Ljava/lang/String;>;)V"}], "flds": []}}}}