# 原版生物清理功能说明

## 功能概述

在原有的精灵清理功能基础上，新增了原版生物清理功能，可以同时清理 Minecraft 原版生物（如僵尸、骷髅、牛、羊等）和精灵。

## 新增配置项

### 基础配置

```yaml
# 是否启用原版生物清理功能
enable-vanilla-mob-cleaning: false

# 排除清理的原版生物列表
# 这些原版生物永远不会被清理
# 支持精确匹配和部分匹配
excluded-vanilla-mobs:
  - "villager"     # 排除村民
  - "iron_golem"   # 排除铁傀儡
  - "cat"          # 排除猫
  - "wolf"         # 排除狼
  - "horse"        # 排除马
  - "donkey"       # 排除驴
  - "mule"         # 排除骡子
  - "llama"        # 排除羊驼
  - "parrot"       # 排除鹦鹉
  - "axolotl"      # 排除美西螈
  - "bee"          # 排除蜜蜂

# 每次清理最多清理多少只原版生物
vanilla-mob-max-clean-per-run: 30

# 原版生物存在多少秒后才能被清理
vanilla-mob-min-age: 120
```

### 公告消息更新

```yaml
# 清理公告消息模板
# {pokemonCount} - 清理的精灵数量
# {vanillaCount} - 清理的原版生物数量
# {duration} - 清理用时（毫秒）
announcement-message: "§6[精灵清理] §f已清理 §e{pokemonCount} §f只精灵，§e{vanillaCount} §f只原版生物，用时 §e{duration}ms"
```

### BossBar 消息更新

BossBar 现在支持显示详细的清理信息：

```yaml
# 可用变量：
#   {count} - 清理的实体总数量
#   {pokemonCount} - 清理的精灵数量
#   {vanillaCount} - 清理的原版生物数量
bossbar-message-for-count:
  - "0;§a§l【清理完成】精灵: {pokemonCount} 只 | 原版生物: {vanillaCount} 只 | 总计: {count} 个;SOLID;GREEN"
```

## 功能特性

### 智能识别
- 自动识别精灵实体和原版生物实体
- 排除玩家、NPC、装甲架等特殊实体
- 支持自定义排除列表

### 安全保护
- 不清理有自定义名称的生物（通常表示被驯服或特殊）
- 不清理玩家附近的生物
- 不清理年龄不足的生物
- 支持分别设置精灵和原版生物的最小年龄

### 性能优化
- 分别限制精灵和原版生物的清理数量
- 总清理数量不超过配置的上限
- 支持按世界分别统计

### 详细统计
- 分别统计精灵和原版生物的清理数量
- 按世界显示详细清理结果
- 支持命令查看状态信息

## 命令更新

### `/pokemoncleaner status`
现在显示原版生物清理的配置信息：
- 是否启用原版生物清理
- 原版生物最小年龄设置
- 原版生物每次最大清理数量
- 排除的原版生物列表

### `/pokemoncleaner clean`
手动清理命令现在会显示：
- 精灵清理数量
- 原版生物清理数量
- 总清理数量
- 各世界的详细清理情况

## 使用建议

### 推荐配置
1. **首次使用**：建议先设置 `enable-vanilla-mob-cleaning: false`，测试精灵清理功能正常后再启用
2. **排除列表**：建议将重要的生物（如村民、宠物等）加入排除列表
3. **年龄设置**：原版生物建议设置较长的最小年龄（如120秒），避免误清理刚生成的生物
4. **数量限制**：根据服务器性能调整每次清理的数量上限

### 安全注意事项
- 启用原版生物清理前，请确保已正确配置排除列表
- 建议在测试服务器上先测试功能
- 定期检查清理日志，确保没有误清理重要生物

## 技术实现

### 实体识别逻辑
```kotlin
// 精灵实体识别
private fun isPokemonEntity(entity: Entity): Boolean {
    return entity.javaClass.simpleName.contains("Pokemon") ||
           entity.type.name.lowercase().contains("pokemon")
}

// 原版生物实体识别
private fun isVanillaMobEntity(entity: Entity): Boolean {
    return !isPokemonEntity(entity) && 
           !entity.javaClass.simpleName.contains("Player") &&
           !entity.javaClass.simpleName.contains("NPC") &&
           entity.type.isAlive &&
           // 排除特殊实体...
}
```

### 清理结果数据结构
```kotlin
data class CleanResult(
    val success: Boolean,
    val pokemonCleanedCount: Int,
    val vanillaMobCleanedCount: Int,
    val duration: Long,
    val worldResults: Map<String, WorldCleanResult>,
    val error: String? = null
)

data class WorldCleanResult(
    val pokemonCount: Int,
    val vanillaMobCount: Int
)
```

## 版本兼容性

此功能与现有的精灵清理功能完全兼容，不会影响原有功能的使用。如果不启用原版生物清理，插件行为与之前完全一致。
