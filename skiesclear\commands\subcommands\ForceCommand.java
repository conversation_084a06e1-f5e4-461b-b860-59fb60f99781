/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.brigadier.arguments.ArgumentType
 *  com.mojang.brigadier.arguments.BoolArgumentType
 *  com.mojang.brigadier.arguments.StringArgumentType
 *  com.mojang.brigadier.builder.LiteralArgumentBuilder
 *  com.mojang.brigadier.builder.RequiredArgumentBuilder
 *  com.mojang.brigadier.context.CommandContext
 *  com.mojang.brigadier.suggestion.SuggestionsBuilder
 *  com.mojang.brigadier.tree.LiteralCommandNode
 *  kotlin.Metadata
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  me.lucko.fabric.api.permissions.v0.Permissions
 *  net.minecraft.class_124
 *  net.minecraft.class_2168
 *  net.minecraft.class_2170
 *  net.minecraft.class_2172
 *  net.minecraft.class_2561
 *  net.minecraft.class_2583
 *  net.minecraft.server.MinecraftServer
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.commands.subcommands;

import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.builder.RequiredArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import com.mojang.brigadier.tree.LiteralCommandNode;
import com.pokeskies.skiesclear.ClearTask;
import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.config.ClearConfig;
import com.pokeskies.skiesclear.config.ConfigManager;
import com.pokeskies.skiesclear.utils.SubCommand;
import java.util.concurrent.CompletableFuture;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import me.lucko.fabric.api.permissions.v0.Permissions;
import net.minecraft.class_124;
import net.minecraft.class_2168;
import net.minecraft.class_2170;
import net.minecraft.class_2172;
import net.minecraft.class_2561;
import net.minecraft.class_2583;
import net.minecraft.server.MinecraftServer;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \b2\u00020\u0001:\u0001\bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u0016\u00a2\u0006\u0004\b\u0006\u0010\u0007\u00a8\u0006\t"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/ForceCommand;", "Lcom/pokeskies/skiesclear/utils/SubCommand;", "<init>", "()V", "Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Lnet/minecraft/class_2168;", "build", "()Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Companion", "SkiesClear"})
public final class ForceCommand
implements SubCommand {
    @NotNull
    public static final Companion Companion = new Companion(null);

    @Override
    @NotNull
    public LiteralCommandNode<class_2168> build() {
        LiteralCommandNode literalCommandNode = ((LiteralArgumentBuilder)((LiteralArgumentBuilder)class_2170.method_9247((String)"force").requires(Permissions.require((String)"skiesclear.command.force", (int)4))).then(((RequiredArgumentBuilder)class_2170.method_9244((String)"id", (ArgumentType)((ArgumentType)StringArgumentType.string())).suggests(ForceCommand::build$lambda$0).then(class_2170.method_9244((String)"announce", (ArgumentType)((ArgumentType)BoolArgumentType.bool())).executes(ForceCommand::build$lambda$1))).executes(ForceCommand::build$lambda$2))).build();
        Intrinsics.checkNotNullExpressionValue((Object)literalCommandNode, (String)"build(...)");
        return literalCommandNode;
    }

    private static final CompletableFuture build$lambda$0(CommandContext ctx, SuggestionsBuilder builder) {
        return class_2172.method_9265((Iterable)ConfigManager.Companion.getCONFIG().getClears().keySet(), (SuggestionsBuilder)builder);
    }

    private static final int build$lambda$1(CommandContext ctx) {
        Intrinsics.checkNotNull((Object)ctx);
        String string = StringArgumentType.getString((CommandContext)ctx, (String)"id");
        Intrinsics.checkNotNullExpressionValue((Object)string, (String)"getString(...)");
        return ForceCommand.Companion.give((CommandContext<class_2168>)ctx, string, BoolArgumentType.getBool((CommandContext)ctx, (String)"announce"));
    }

    private static final int build$lambda$2(CommandContext ctx) {
        Intrinsics.checkNotNull((Object)ctx);
        String string = StringArgumentType.getString((CommandContext)ctx, (String)"id");
        Intrinsics.checkNotNullExpressionValue((Object)string, (String)"getString(...)");
        return ForceCommand.Companion.give((CommandContext<class_2168>)ctx, string, true);
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J-\u0010\f\u001a\u00020\u000b2\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\b\f\u0010\r\u00a8\u0006\u000e"}, d2={"Lcom/pokeskies/skiesclear/commands/subcommands/ForceCommand$Companion;", "", "<init>", "()V", "Lcom/mojang/brigadier/context/CommandContext;", "Lnet/minecraft/class_2168;", "ctx", "", "id", "", "announce", "", "give", "(Lcom/mojang/brigadier/context/CommandContext;Ljava/lang/String;Z)I", "SkiesClear"})
    public static final class Companion {
        private Companion() {
        }

        private final int give(CommandContext<class_2168> ctx, String id, boolean announce) {
            ClearConfig clearConfig = ConfigManager.Companion.getCONFIG().getClears().get(id);
            if (clearConfig == null) {
                ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("Clear with id " + id + " not found.")).method_27694(Companion::give$lambda$0));
                return 0;
            }
            if (!clearConfig.getEnabled()) {
                ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("The Clear entry " + id + " is disabled.")).method_27694(Companion::give$lambda$1));
                return 0;
            }
            ClearTask clearTask = SkiesClear.Companion.getINSTANCE().getClearManager().getClearTask(id);
            if (clearTask == null) {
                ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)("Clear Task for " + id + " not found. Is it disabled?")).method_27694(Companion::give$lambda$2));
                return 0;
            }
            MinecraftServer minecraftServer = ((class_2168)ctx.getSource()).method_9211();
            Intrinsics.checkNotNullExpressionValue((Object)minecraftServer, (String)"getServer(...)");
            int entitiesCleared = clearTask.runClear(minecraftServer, announce);
            clearTask.resetTimer();
            ((class_2168)ctx.getSource()).method_45068((class_2561)class_2561.method_43470((String)("Successfully cleared " + entitiesCleared + " entities!")).method_27694(Companion::give$lambda$3));
            return 1;
        }

        private static final class_2583 give$lambda$0(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 give$lambda$1(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 give$lambda$2(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        private static final class_2583 give$lambda$3(class_2583 it) {
            return it.method_10977(class_124.field_1060);
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

