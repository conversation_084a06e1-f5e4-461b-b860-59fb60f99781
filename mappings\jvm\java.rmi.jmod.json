{"md5": "7e95664a38d585210c91613d4df9bf78", "sha2": "7c8d3974e092a51c11d6f8f0d7faec7997cbb241", "sha256": "77a2e39a1bbe56175da010647fb571e47c3f4d014e2cc62af50d3084b042422f", "contents": {"classes": {"classes/java/rmi/MarshalledObject.class": {"ver": 65, "acc": 49, "nme": "java/rmi/MarshalledObject", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TT;)V", "exs": ["java/io/IOException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "get", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TT;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 2, "nme": "objBytes", "dsc": "[B"}, {"acc": 2, "nme": "locBytes", "dsc": "[B"}, {"acc": 2, "nme": "hash", "dsc": "I"}, {"acc": 130, "nme": "objectInputFilter", "dsc": "Ljava/io/ObjectInputFilter;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8988374069173025854}]}, "classes/sun/rmi/transport/DGCAckHandler$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCAckHandler$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/DGCAckHandler;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/DGCAckHandler;"}]}, "classes/java/rmi/server/RMIClientSocketFactory.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/server/RMIClientSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "createSocket", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/net/Socket;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/java/rmi/server/UID.class": {"ver": 65, "acc": 49, "nme": "java/rmi/server/UID", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(S)V"}, {"nme": "<init>", "acc": 2, "dsc": "(IJS)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 9, "dsc": "(Ljava/io/DataInput;)Ljava/rmi/server/UID;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "hostUnique", "dsc": "I"}, {"acc": 10, "nme": "hostUniqueSet", "dsc": "Z"}, {"acc": 26, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "lastTime", "dsc": "J"}, {"acc": 10, "nme": "lastCount", "dsc": "S"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1086053664494604050}, {"acc": 18, "nme": "unique", "dsc": "I"}, {"acc": 18, "nme": "time", "dsc": "J"}, {"acc": 18, "nme": "count", "dsc": "S"}]}, "classes/sun/rmi/transport/ConnectionOutputStream.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/ConnectionOutputStream", "super": "sun/rmi/server/MarshalOutputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/Connection;Z)V", "exs": ["java/io/IOException"]}, {"nme": "writeID", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isResultStream", "acc": 0, "dsc": "()Z"}, {"nme": "saveObject", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getDGCAckHandler", "acc": 0, "dsc": "()Lsun/rmi/transport/DGCAckHandler;"}, {"nme": "done", "acc": 0, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "conn", "dsc": "Lsun/rmi/transport/Connection;"}, {"acc": 18, "nme": "resultStream", "dsc": "Z"}, {"acc": 18, "nme": "ackID", "dsc": "Ljava/rmi/server/UID;"}, {"acc": 2, "nme": "dgcAckHandler", "dsc": "Lsun/rmi/transport/DGCAckHandler;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/transport/DGCImpl$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/DGCImpl;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/DGCImpl;"}]}, "classes/java/rmi/server/RMISocketFactory.class": {"ver": 65, "acc": 1057, "nme": "java/rmi/server/RMISocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createSocket", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/net/Socket;", "exs": ["java/io/IOException"]}, {"nme": "createServerSocket", "acc": 1025, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}, {"nme": "setSocketFactory", "acc": 41, "dsc": "(Ljava/rmi/server/RMISocketFactory;)V", "exs": ["java/io/IOException"]}, {"nme": "getSocketFactory", "acc": 41, "dsc": "()Ljava/rmi/server/RMISocketFactory;"}, {"nme": "getDefaultSocketFactory", "acc": 41, "dsc": "()Ljava/rmi/server/RMISocketFactory;"}, {"nme": "setFailureHandler", "acc": 41, "dsc": "(Ljava/rmi/server/RMIFailureHandler;)V"}, {"nme": "getFailureHandler", "acc": 41, "dsc": "()Ljava/rmi/server/RMIFailureHandler;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "factory", "dsc": "Ljava/rmi/server/RMISocketFactory;"}, {"acc": 10, "nme": "defaultSocketFactory", "dsc": "Ljava/rmi/server/RMISocketFactory;"}, {"acc": 10, "nme": "handler", "dsc": "Ljava/rmi/server/RMIFailureHandler;"}]}, "classes/sun/rmi/transport/tcp/TCPEndpoint$FQDN.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/TCPEndpoint$FQDN", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "attemptFQDN", "acc": 8, "dsc": "(Lja<PERSON>/net/InetAddress;)Ljava/lang/String;", "exs": ["java/net/UnknownHostException"]}, {"nme": "getFQDN", "acc": 2, "dsc": "()V"}, {"nme": "getHost", "acc": 34, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "reverseLookup", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "host<PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/java/rmi/server/Skeleton.class": {"ver": 65, "acc": 132609, "nme": "java/rmi/server/Skeleton", "super": "java/lang/Object", "mthds": [{"nme": "dispatch", "acc": 132097, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;IJ)V", "exs": ["java/lang/Exception"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getOperations", "acc": 132097, "dsc": "()[Ljava/rmi/server/Operation;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/ServerRef.class": {"ver": 65, "acc": 132609, "nme": "java/rmi/server/ServerRef", "super": "java/lang/Object", "mthds": [{"nme": "exportObject", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Lja<PERSON>/lang/Object;)Ljava/rmi/server/RemoteStub;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getClientHost", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/server/ServerNotActiveException"]}], "flds": [{"acc": 131097, "nme": "serialVersionUID", "dsc": "J", "val": -4557750989390278438}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/LoaderHandler.class": {"ver": 65, "acc": 132609, "nme": "java/rmi/server/LoaderHandler", "super": "java/lang/Object", "mthds": [{"nme": "loadClass", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "loadClass", "acc": 132097, "dsc": "(Ljava/net/URL;Ljava/lang/String;)Ljava/lang/Class;", "sig": "(Ljava/net/URL;Ljava/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getSecurityContext", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON><PERSON><PERSON>/lang/Object;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 25, "nme": "packagePrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.rmi.server"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/RemoteRef.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/server/RemoteRef", "super": "java/lang/Object", "mthds": [{"nme": "invoke", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;J)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "newCall", "acc": 132097, "dsc": "(Ljava/rmi/server/RemoteObject;[Ljava/rmi/server/Operation;IJ)Ljava/rmi/server/RemoteCall;", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "invoke", "acc": 132097, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/lang/Exception"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "done", "acc": 132097, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getRefClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "remoteHashCode", "acc": 1025, "dsc": "()I"}, {"nme": "remoteEquals", "acc": 1025, "dsc": "(Ljava/rmi/server/RemoteRef;)Z"}, {"nme": "remoteToString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 131097, "nme": "serialVersionUID", "dsc": "J", "val": 3632638527362204081}, {"acc": 25, "nme": "packagePrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.rmi.server"}]}, "classes/sun/rmi/runtime/Log$LoggerLog.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LoggerLog", "super": "sun/rmi/runtime/Log", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/util/logging/Logger;Ljava/util/logging/Level;)V"}, {"nme": "isLoggable", "acc": 1, "dsc": "(Ljava/util/logging/Level;)Z"}, {"nme": "log", "acc": 1, "dsc": "(Ljava/util/logging/Level;Ljava/lang/String;)V"}, {"nme": "log", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setOutputStream", "acc": 33, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "getPrintStream", "acc": 33, "dsc": "()Ljava/io/PrintStream;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "alternateConsole", "dsc": "Ljava/util/logging/Handler;"}, {"acc": 2, "nme": "copyHandler", "dsc": "Lsun/rmi/runtime/Log$InternalStreamHandler;"}, {"acc": 18, "nme": "logger", "dsc": "Ljava/util/logging/Logger;"}, {"acc": 2, "nme": "loggerSandwich", "dsc": "Lsun/rmi/runtime/Log$LoggerPrintStream;"}]}, "classes/java/rmi/RMISecurityManager.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/RMISecurityManager", "super": "java/lang/SecurityManager", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "1.8", "forRemoval", true]}]}, "classes/java/rmi/MarshalException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/MarshalException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6223554758134037936}]}, "classes/sun/rmi/server/LoaderHandler$2.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/LoaderHandler$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/security/PermissionCollection;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/dgc/VMID.class": {"ver": 65, "acc": 49, "nme": "java/rmi/dgc/VMID", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isUnique", "acc": 131081, "dsc": "()Z", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "randomBytes", "dsc": "[B"}, {"acc": 2, "nme": "addr", "dsc": "[B"}, {"acc": 2, "nme": "uid", "dsc": "Ljava/rmi/server/UID;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -538642295484486218}]}, "classes/sun/rmi/transport/ObjectTable$Reaper.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/ObjectTable$Reaper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/sun/rmi/registry/RegistryImpl$5.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$5", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(I)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Lsun/rmi/registry/RegistryImpl;", "exs": ["java/rmi/RemoteException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$regPort", "dsc": "I"}]}, "classes/sun/rmi/transport/DGCClient$EndpointEntry$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry;)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/DGCClient$EndpointEntry;"}]}, "classes/java/rmi/server/Unreferenced.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/server/Unreferenced", "super": "java/lang/Object", "mthds": [{"nme": "unreferenced", "acc": 1025, "dsc": "()V"}], "flds": []}, "classes/java/rmi/ServerRuntimeException.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/ServerRuntimeException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 7054464920481467219}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps$1.class": {"ver": 65, "acc": 32, "nme": "java/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps$1", "super": "java/util/WeakHashMap", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps;)V"}, {"nme": "get", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "get", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/Naming$ParsedNamingURL.class": {"ver": 65, "acc": 32, "nme": "java/rmi/Naming$ParsedNamingURL", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 0, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "port", "dsc": "I"}, {"acc": 0, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/rmi/transport/ObjectEndpoint.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/ObjectEndpoint", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/rmi/server/ObjID;Lsun/rmi/transport/Transport;)V"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "id", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 18, "nme": "transport", "dsc": "Lsun/rmi/transport/Transport;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/runtime/NewThreadAction$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/NewThreadAction$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/lang/ThreadGroup;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/server/UnicastServerRef$HashToMethod_Maps.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/UnicastServerRef$HashToMethod_Maps", "super": "sun/rmi/server/WeakClassHashMap", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(L<PERSON><PERSON>/lang/Class;)Ljava/util/Map;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Map<Ljava/lang/Long;Ljava/lang/reflect/Method;>;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/server/UnicastServerRef.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/UnicastServerRef", "super": "sun/rmi/server/UnicastRef", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;Ljava/io/ObjectInputFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Z)V"}, {"nme": "exportObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Lja<PERSON>/lang/Object;)Ljava/rmi/server/RemoteStub;", "exs": ["java/rmi/RemoteException"]}, {"nme": "exportObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;<PERSON><PERSON><PERSON>/lang/Object;Z)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getClientHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/server/ServerNotActiveException"]}, {"nme": "setSkeleton", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "dispatch", "acc": 1, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;)V", "exs": ["java/io/IOException"]}, {"nme": "unmarshalCustomCallData", "acc": 4, "dsc": "(Ljava/io/ObjectInput;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "oldDispatch", "acc": 2, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;I)V", "exs": ["java/lang/Exception"]}, {"nme": "clearStackTraces", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "logCall", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "logCallException", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getRefClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "getClientRef", "acc": 4, "dsc": "()Ljava/rmi/server/RemoteRef;"}, {"nme": "writeExternal", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "readExternal", "acc": 1, "dsc": "(Ljava/io/ObjectInput;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "lambda$unmarshalCustomCallData$3", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectInputStream;)<PERSON>ja<PERSON>/lang/Void;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "logCalls", "dsc": "Z"}, {"acc": 25, "nme": "callLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -7384275867073752268}, {"acc": 26, "nme": "wantExceptionLog", "dsc": "Z"}, {"acc": 2, "nme": "forceStubUse", "dsc": "Z"}, {"acc": 26, "nme": "suppressStackTraces", "dsc": "Z"}, {"acc": 130, "nme": "skel", "dsc": "Ljava/rmi/server/Skeleton;"}, {"acc": 146, "nme": "filter", "dsc": "Ljava/io/ObjectInputFilter;"}, {"acc": 130, "nme": "hashToMethod_Map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Long;Ljava/lang/reflect/Method;>;"}, {"acc": 26, "nme": "hashToMethod_Maps", "dsc": "Lsun/rmi/server/WeakClassHashMap;", "sig": "Lsun/rmi/server/WeakClassHashMap<Ljava/util/Map<Ljava/lang/Long;Ljava/lang/reflect/Method;>;>;"}, {"acc": 26, "nme": "withoutSkeletons", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;*>;"}]}, "classes/sun/rmi/transport/Channel.class": {"ver": 65, "acc": 1537, "nme": "sun/rmi/transport/Channel", "super": "java/lang/Object", "mthds": [{"nme": "newConnection", "acc": 1025, "dsc": "()Lsun/rmi/transport/Connection;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getEndpoint", "acc": 1025, "dsc": "()Lsun/rmi/transport/Endpoint;"}, {"nme": "free", "acc": 1025, "dsc": "(Lsun/rmi/transport/Connection;Z)V", "exs": ["java/rmi/RemoteException"]}], "flds": []}, "classes/javax/rmi/ssl/SslRMIServerSocketFactory$1.class": {"ver": 65, "acc": 32, "nme": "javax/rmi/ssl/SslRMIServerSocketFactory$1", "super": "java/net/ServerSocket", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljavax/rmi/ssl/SslRMIServerSocketFactory;ILjavax/net/ssl/SSLSocketFactory;)V", "exs": ["java/io/IOException"]}, {"nme": "accept", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$sslSocketFactory", "dsc": "Ljavax/net/ssl/SSLSocketFactory;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljavax/rmi/ssl/SslRMIServerSocketFactory;"}]}, "classes/sun/rmi/server/MarshalOutputStream.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/MarshalOutputStream", "super": "java/io/ObjectOutputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/OutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "replaceObject", "acc": 20, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "annotateClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V", "exs": ["java/io/IOException"]}, {"nme": "annotateProxyClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)V", "exs": ["java/io/IOException"]}, {"nme": "writeLocation", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "access$000", "acc": 4104, "dsc": "(Lsun/rmi/server/MarshalOutputStream;Z)Z", "exs": ["java/lang/SecurityException"]}], "flds": []}, "classes/sun/rmi/server/Util.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/server/Util", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "createProxy", "acc": 9, "dsc": "(Lja<PERSON>/lang/Class;Ljava/rmi/server/RemoteRef;Z)Ljava/rmi/Remote;", "sig": "(Ljava/lang/Class<*>;Ljava/rmi/server/RemoteRef;Z)Ljava/rmi/Remote;", "exs": ["java/rmi/StubNotFoundException"]}, {"nme": "stubClassExists", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Z"}, {"nme": "getRemoteClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "getRemoteInterfaces", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Ljava/lang/Class;", "sig": "(Lja<PERSON>/lang/Class<*>;)[Ljava/lang/Class<*>;"}, {"nme": "getRemoteInterfaces", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/ArrayList;<PERSON><PERSON><PERSON>/lang/Class;)V", "sig": "(<PERSON><PERSON><PERSON>/util/ArrayList<Ljava/lang/Class<*>;>;Ljava/lang/Class<*>;)V"}, {"nme": "checkMethod", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)V"}, {"nme": "createStub", "acc": 10, "dsc": "(Ljava/lang/Class;Ljava/rmi/server/RemoteRef;)Ljava/rmi/server/RemoteStub;", "sig": "(Ljava/lang/Class<*>;Ljava/rmi/server/RemoteRef;)Ljava/rmi/server/RemoteStub;", "exs": ["java/rmi/StubNotFoundException"]}, {"nme": "createSkeleton", "acc": 8, "dsc": "(<PERSON>ja<PERSON>/rmi/Remote;)Ljava/rmi/server/Skeleton;", "exs": ["java/rmi/server/SkeletonNotFoundException"]}, {"nme": "computeMethodHash", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)J"}, {"nme": "getMethodNameAndDescriptor", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)Ljava/lang/String;"}, {"nme": "getTypeDescriptor", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "getUnqualifiedName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "logLevel", "dsc": "I"}, {"acc": 25, "nme": "serverRefLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "ignoreStubClasses", "dsc": "Z"}, {"acc": 26, "nme": "withoutStubs", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Ljava/lang/Void;>;"}, {"acc": 26, "nme": "stubConsParamTypes", "dsc": "[Ljava/lang/Class;", "sig": "[Ljava/lang/Class<*>;"}]}, "classes/sun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef", "super": "java/lang/ref/PhantomReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;Lsun/rmi/transport/LiveRef;)V"}, {"nme": "getRefEntry", "acc": 1, "dsc": "()Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;"}], "flds": [{"acc": 4112, "nme": "this$1", "dsc": "Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;"}]}, "classes/sun/rmi/transport/StreamRemoteCall.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/StreamRemoteCall", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/Connection;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/Connection;Ljava/rmi/server/ObjID;IJ)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "getConnection", "acc": 1, "dsc": "()Lsun/rmi/transport/Connection;"}, {"nme": "getOutputStream", "acc": 1, "dsc": "()Ljava/io/ObjectOutput;", "exs": ["java/io/IOException"]}, {"nme": "getOutputStream", "acc": 2, "dsc": "(Z)Ljava/io/ObjectOutput;", "exs": ["java/io/IOException"]}, {"nme": "releaseOutputStream", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "setObjectInputFilter", "acc": 1, "dsc": "(Ljava/io/ObjectInputFilter;)V"}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/ObjectInput;", "exs": ["java/io/IOException"]}, {"nme": "releaseInputStream", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "discardPendingRefs", "acc": 1, "dsc": "()V"}, {"nme": "getResultStream", "acc": 1, "dsc": "(Z)Ljava/io/ObjectOutput;", "exs": ["java/io/IOException"]}, {"nme": "executeCall", "acc": 1, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "exceptionReceivedFromServer", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V", "exs": ["java/lang/Exception"]}, {"nme": "getServerException", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Exception;"}, {"nme": "done", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "lambda$getInputStream$0", "acc": 4098, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}], "flds": [{"acc": 2, "nme": "in", "dsc": "Lsun/rmi/transport/ConnectionInputStream;"}, {"acc": 2, "nme": "out", "dsc": "Lsun/rmi/transport/ConnectionOutputStream;"}, {"acc": 2, "nme": "conn", "dsc": "Lsun/rmi/transport/Connection;"}, {"acc": 2, "nme": "filter", "dsc": "Ljava/io/ObjectInputFilter;"}, {"acc": 2, "nme": "resultStarted", "dsc": "Z"}, {"acc": 2, "nme": "serverException", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}]}, "classes/java/rmi/Remote.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/Remote", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/java/rmi/ServerError.class": {"ver": 65, "acc": 33, "nme": "java/rmi/ServerError", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Error;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8455284893909696482}]}, "classes/sun/rmi/registry/RegistryImpl$6.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/security/PermissionCollection;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/server/WeakClassHashMap.class": {"ver": 65, "acc": 1057, "nme": "sun/rmi/server/WeakClassHashMap", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "get", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)TV;"}, {"nme": "computeValue", "acc": 1028, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)TV;"}], "flds": [{"acc": 2, "nme": "internalMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Class<*>;Lsun/rmi/server/WeakClassHashMap$ValueCell<TV;>;>;"}]}, "classes/java/rmi/server/RMIClassLoaderSpi.class": {"ver": 65, "acc": 1057, "nme": "java/rmi/server/RMIClassLoaderSpi", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "loadClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;Lja<PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "getClassLoader", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/ClassLoader;", "exs": ["java/net/MalformedURLException"]}, {"nme": "getClassAnnotation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}], "flds": []}, "classes/java/rmi/server/ObjID.class": {"ver": 65, "acc": 49, "nme": "java/rmi/server/ObjID", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 2, "dsc": "(JLjava/rmi/server/UID;)V"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 9, "dsc": "(Ljava/io/ObjectInput;)Ljava/rmi/server/ObjID;", "exs": ["java/io/IOException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "useRandomIDs", "acc": 10, "dsc": "()Z"}, {"nme": "lambda$useRandomIDs$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "REGISTRY_ID", "dsc": "I", "val": 0}, {"acc": 25, "nme": "ACTIVATOR_ID", "dsc": "I", "val": 1}, {"acc": 25, "nme": "DGC_ID", "dsc": "I", "val": 2}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -6386392263968365220}, {"acc": 26, "nme": "nextObjNum", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 26, "nme": "mySpace", "dsc": "Ljava/rmi/server/UID;"}, {"acc": 26, "nme": "secureRandom", "dsc": "Ljava/security/SecureRandom;"}, {"acc": 18, "nme": "objNum", "dsc": "J"}, {"acc": 18, "nme": "space", "dsc": "Ljava/rmi/server/UID;"}]}, "classes/sun/rmi/log/LogInputStream.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/log/LogInputStream", "super": "java/io/InputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([B)I", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 1, "dsc": "([BII)I", "exs": ["java/io/IOException"]}, {"nme": "skip", "acc": 1, "dsc": "(J)J", "exs": ["java/io/IOException"]}, {"nme": "available", "acc": 1, "dsc": "()I", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "finalize", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "in", "dsc": "Ljava/io/InputStream;"}, {"acc": 2, "nme": "length", "dsc": "I"}]}, "classes/sun/rmi/server/LoaderHandler$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/LoaderHandler$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Ljava/net/URL;Ljava/lang/ClassLoader;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Lsun/rmi/server/LoaderHandler$Loader;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$urls", "dsc": "[Ljava/net/URL;"}, {"acc": 4112, "nme": "val$parent", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}]}, "classes/sun/rmi/transport/Transport$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/Transport$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/Transport;Ljava/security/AccessControlContext;Lsun/rmi/server/Dispatcher;Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/io/IOException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$acc", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 4112, "nme": "val$disp", "dsc": "Lsun/rmi/server/Dispatcher;"}, {"acc": 4112, "nme": "val$impl", "dsc": "<PERSON><PERSON><PERSON>/rmi/Remote;"}, {"acc": 4112, "nme": "val$call", "dsc": "Ljava/rmi/server/RemoteCall;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/Transport;"}]}, "classes/sun/rmi/transport/tcp/TCPTransport$ConnectionHandler.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/TCPTransport$ConnectionHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPTransport;Ljava/net/Socket;Ljava/lang/String;)V"}, {"nme": "getClientHost", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "checkAcceptPermission", "acc": 0, "dsc": "(Ljava/lang/SecurityManager;Ljava/security/AccessControlContext;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "run0", "acc": 2, "dsc": "()V"}, {"nme": "lambda$run$0", "acc": 4098, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}], "flds": [{"acc": 26, "nme": "POST", "dsc": "I", "val": 1347375956}, {"acc": 2, "nme": "okContext", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 2, "nme": "auth<PERSON><PERSON>", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/security/AccessControlContext;Ljava/lang/ref/Reference<Ljava/security/AccessControlContext;>;>;"}, {"acc": 2, "nme": "cacheSecurityManager", "dsc": "<PERSON><PERSON><PERSON>/lang/SecurityManager;"}, {"acc": 2, "nme": "socket", "dsc": "Ljava/net/Socket;"}, {"acc": 2, "nme": "remoteHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/tcp/TCPTransport;"}]}, "classes/sun/rmi/transport/GC$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/GC$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/transport/tcp/TCPTransport$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/TCPTransport$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "newThread", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)<PERSON><PERSON><PERSON>/lang/Thread;"}], "flds": []}, "classes/sun/rmi/transport/DGCClient$EndpointEntry.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry", "super": "java/lang/Object", "mthds": [{"nme": "lookup", "acc": 9, "dsc": "(Lsun/rmi/transport/Endpoint;)Lsun/rmi/transport/DGCClient$EndpointEntry;"}, {"nme": "<init>", "acc": 2, "dsc": "(Lsun/rmi/transport/Endpoint;)V"}, {"nme": "registerRefs", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Z", "sig": "(Ljava/util/List<Lsun/rmi/transport/LiveRef;>;)Z"}, {"nme": "removeRefEntry", "acc": 2, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;)V"}, {"nme": "makeDirtyCall", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;J)V", "sig": "(Ljava/util/Set<Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;>;J)V"}, {"nme": "setRenewTime", "acc": 2, "dsc": "(J)V"}, {"nme": "processPhantomRefs", "acc": 2, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef;)V"}, {"nme": "makeCleanCalls", "acc": 2, "dsc": "()V"}, {"nme": "createObjIDArray", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Set;)[Ljava/rmi/server/ObjID;", "sig": "(Ljava/util/Set<Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;>;)[Ljava/rmi/server/ObjID;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "endpoint", "dsc": "Lsun/rmi/transport/Endpoint;"}, {"acc": 2, "nme": "dgc", "dsc": "Ljava/rmi/dgc/DGC;"}, {"acc": 2, "nme": "refTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/LiveRef;Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;>;"}, {"acc": 2, "nme": "invalidRefs", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry;>;"}, {"acc": 2, "nme": "removed", "dsc": "Z"}, {"acc": 2, "nme": "renewTime", "dsc": "J"}, {"acc": 2, "nme": "expirationTime", "dsc": "J"}, {"acc": 2, "nme": "dirtyFailures", "dsc": "I"}, {"acc": 2, "nme": "dirtyFailureStartTime", "dsc": "J"}, {"acc": 2, "nme": "dirtyFailureDuration", "dsc": "J"}, {"acc": 2, "nme": "renewCleanThread", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 2, "nme": "interruptible", "dsc": "Z"}, {"acc": 2, "nme": "refQueue", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/ReferenceQueue;", "sig": "Ljava/lang/ref/ReferenceQueue<Lsun/rmi/transport/LiveRef;>;"}, {"acc": 2, "nme": "pendingCleans", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lsun/rmi/transport/DGCClient$EndpointEntry$CleanRequest;>;"}, {"acc": 10, "nme": "endpointTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/Endpoint;Lsun/rmi/transport/DGCClient$EndpointEntry;>;"}, {"acc": 10, "nme": "gcLatencyRequest", "dsc": "Lsun/rmi/transport/GC$LatencyRequest;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/registry/RegistryImpl.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/registry/RegistryImpl", "super": "java/rmi/server/RemoteServer", "mthds": [{"nme": "initRegistryFilter", "acc": 10, "dsc": "()Ljava/io/ObjectInputFilter;"}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "<init>", "acc": 1, "dsc": "(I)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "setup", "acc": 2, "dsc": "(Lsun/rmi/server/UnicastServerRef;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "lookup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/RemoteException", "java/rmi/NotBoundException"]}, {"nme": "bind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException", "java/rmi/AlreadyBoundException", "java/rmi/AccessException"]}, {"nme": "unbind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/RemoteException", "java/rmi/NotBoundException", "java/rmi/AccessException"]}, {"nme": "rebind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException", "java/rmi/AccessException"]}, {"nme": "list", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/RemoteException"]}, {"nme": "checkAccess", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/AccessException"]}, {"nme": "getID", "acc": 9, "dsc": "()Ljava/rmi/server/ObjID;"}, {"nme": "getTextResource", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "pathToURLs", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;)[Ljava/net/URL;"}, {"nme": "registryFilter", "acc": 10, "dsc": "(Ljava/io/ObjectInputFilter$FilterInfo;)Ljava/io/ObjectInputFilter$Status;"}, {"nme": "createRegistry", "acc": 9, "dsc": "(I)Lsun/rmi/registry/RegistryImpl;", "exs": ["java/rmi/RemoteException"]}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getAccessControlContext", "acc": 10, "dsc": "(I)Ljava/security/AccessControlContext;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4666870661827494597}, {"acc": 2, "nme": "bindings", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Lja<PERSON>/util/Hashtable<Ljava/lang/String;Ljava/rmi/Remote;>;"}, {"acc": 10, "nme": "allowedAccessCache", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Ljava/util/Hashtable<Ljava/net/InetAddress;Ljava/net/InetAddress;>;"}, {"acc": 10, "nme": "registry", "dsc": "Lsun/rmi/registry/RegistryImpl;"}, {"acc": 10, "nme": "id", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 10, "nme": "resources", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 26, "nme": "REGISTRY_FILTER_PROPNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.rmi.registry.registryFilter"}, {"acc": 26, "nme": "REGISTRY_MAX_DEPTH", "dsc": "I", "val": 20}, {"acc": 26, "nme": "REGISTRY_MAX_ARRAY_SIZE", "dsc": "I", "val": 1000000}, {"acc": 26, "nme": "registryFilter", "dsc": "Ljava/io/ObjectInputFilter;"}]}, "classes/sun/rmi/transport/DGCImpl$2$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCImpl$2$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/DGCImpl$2;Lsun/rmi/server/UnicastServerRef;Ljava/rmi/Remote;Ljava/rmi/server/ObjID;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Lsun/rmi/transport/Target;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$disp", "dsc": "Lsun/rmi/server/UnicastServerRef;"}, {"acc": 4112, "nme": "val$stub", "dsc": "<PERSON><PERSON><PERSON>/rmi/Remote;"}, {"acc": 4112, "nme": "val$dgcID", "dsc": "Ljava/rmi/server/ObjID;"}]}, "classes/sun/rmi/transport/DGCImpl$LeaseInfo.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCImpl$LeaseInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/rmi/dgc/VMID;J)V"}, {"nme": "renew", "acc": 32, "dsc": "(J)V"}, {"nme": "expired", "acc": 0, "dsc": "(J)Z"}], "flds": [{"acc": 0, "nme": "vmid", "dsc": "Ljava/rmi/dgc/VMID;"}, {"acc": 0, "nme": "expiration", "dsc": "J"}, {"acc": 0, "nme": "notifySet", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lsun/rmi/transport/Target;>;"}]}, "classes/sun/rmi/transport/DGCClient$EndpointEntry$CleanRequest.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry$CleanRequest", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([Ljava/rmi/server/ObjID;JZ)V"}], "flds": [{"acc": 16, "nme": "objIDs", "dsc": "[Ljava/rmi/server/ObjID;"}, {"acc": 16, "nme": "sequenceNum", "dsc": "J"}, {"acc": 16, "nme": "strong", "dsc": "Z"}, {"acc": 0, "nme": "failures", "dsc": "I"}]}, "classes/sun/rmi/transport/GC$LatencyLock.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/GC$LatencyLock", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}], "flds": []}, "classes/sun/rmi/transport/tcp/TCPTransport$AcceptLoop.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/TCPTransport$AcceptLoop", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPTransport;Ljava/net/ServerSocket;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "executeAcceptLoop", "acc": 2, "dsc": "()V"}, {"nme": "continueAfterAcceptFailure", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)Z"}, {"nme": "throttleLoopOnException", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "serverSocket", "dsc": "Ljava/net/ServerSocket;"}, {"acc": 2, "nme": "lastExceptionTime", "dsc": "J"}, {"acc": 2, "nme": "recentExceptionCount", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/tcp/TCPTransport;"}]}, "classes/javax/rmi/ssl/SslRMIClientSocketFactory.class": {"ver": 65, "acc": 33, "nme": "javax/rmi/ssl/SslRMIClientSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createSocket", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/net/Socket;", "exs": ["java/io/IOException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getDefaultClientSocketFactory", "acc": 42, "dsc": "()Ljavax/net/SocketFactory;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "defaultSocketFactory", "dsc": "Ljavax/net/SocketFactory;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -8310631444933958385}]}, "classes/sun/rmi/runtime/Log$LogStreamLogFactory.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LogStreamLogFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "createLog", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/logging/Level;)Lsun/rmi/runtime/Log;"}], "flds": []}, "classes/sun/rmi/server/UnicastServerRef2.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/UnicastServerRef2", "super": "sun/rmi/server/UnicastServerRef", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;Ljava/io/ObjectInputFilter;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)V"}, {"nme": "getRefClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "getClientRef", "acc": 4, "dsc": "()Ljava/rmi/server/RemoteRef;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -2289703812660767614}]}, "classes/java/rmi/dgc/DGC.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/dgc/DGC", "super": "java/lang/Object", "mthds": [{"nme": "dirty", "acc": 1025, "dsc": "([<PERSON><PERSON><PERSON>/rmi/server/ObjID;JLjava/rmi/dgc/Lease;)Ljava/rmi/dgc/Lease;", "exs": ["java/rmi/RemoteException"]}, {"nme": "clean", "acc": 1025, "dsc": "([Ljava/rmi/server/ObjID;JLjava/rmi/dgc/VMID;Z)V", "exs": ["java/rmi/RemoteException"]}], "flds": []}, "classes/sun/rmi/runtime/Log.class": {"ver": 65, "acc": 1057, "nme": "sun/rmi/runtime/Log", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "isLoggable", "acc": 1025, "dsc": "(Ljava/util/logging/Level;)Z"}, {"nme": "log", "acc": 1025, "dsc": "(Ljava/util/logging/Level;Ljava/lang/String;)V"}, {"nme": "log", "acc": 1025, "dsc": "(L<PERSON><PERSON>/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "setOutputStream", "acc": 1025, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "getPrintStream", "acc": 1025, "dsc": "()Ljava/io/PrintStream;"}, {"nme": "getLog", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)Lsun/rmi/runtime/Log;"}, {"nme": "getLog", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z)Lsun/rmi/runtime/Log;"}, {"nme": "getSource", "acc": 10, "dsc": "()Ljava/lang/StackWalker$StackFrame;"}, {"nme": "lambda$getSource$1", "acc": 4106, "dsc": "(Lja<PERSON>/util/stream/Stream;)Ljava/lang/StackWalker$StackFrame;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "BRIEF", "dsc": "Ljava/util/logging/Level;"}, {"acc": 25, "nme": "VERBOSE", "dsc": "Ljava/util/logging/Level;"}, {"acc": 26, "nme": "WALKER", "dsc": "<PERSON><PERSON><PERSON>/lang/<PERSON>ack<PERSON>;"}, {"acc": 26, "nme": "logFactory", "dsc": "Lsun/rmi/runtime/Log$LogFactory;"}]}, "classes/sun/rmi/server/UnicastRef2.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/UnicastRef2", "super": "sun/rmi/server/UnicastRef", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "getRefClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "writeExternal", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "readExternal", "acc": 1, "dsc": "(Ljava/io/ObjectInput;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1829537514995881838}]}, "classes/sun/rmi/runtime/Log$LogFactory.class": {"ver": 65, "acc": 1536, "nme": "sun/rmi/runtime/Log$LogFactory", "super": "java/lang/Object", "mthds": [{"nme": "createLog", "acc": 1025, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/logging/Level;)Lsun/rmi/runtime/Log;"}], "flds": []}, "classes/sun/rmi/transport/SequenceEntry.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/SequenceEntry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(J)V"}, {"nme": "retain", "acc": 0, "dsc": "(J)V"}, {"nme": "update", "acc": 0, "dsc": "(J)V"}], "flds": [{"acc": 0, "nme": "sequenceNum", "dsc": "J"}, {"acc": 0, "nme": "keep", "dsc": "Z"}]}, "classes/sun/rmi/transport/DGCClient$EndpointEntry$RefEntry.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry$RefEntry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry;Lsun/rmi/transport/LiveRef;)V"}, {"nme": "getRef", "acc": 1, "dsc": "()Lsun/rmi/transport/LiveRef;"}, {"nme": "addInstanceToRefSet", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "removeInstanceFromRefSet", "acc": 1, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef;)V"}, {"nme": "isRefSetEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "markDirtyFailed", "acc": 1, "dsc": "()V"}, {"nme": "hasDirtyFailed", "acc": 1, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "ref", "dsc": "Lsun/rmi/transport/LiveRef;"}, {"acc": 2, "nme": "refSet", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Lsun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef;>;"}, {"acc": 2, "nme": "dirtyFailed", "dsc": "Z"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/DGCClient$EndpointEntry;"}]}, "classes/sun/rmi/server/LoaderHandler$LoaderEntry.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/LoaderHandler$LoaderEntry", "super": "java/lang/ref/WeakReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/server/LoaderHandler$LoaderKey;Lsun/rmi/server/LoaderHandler$Loader;)V"}], "flds": [{"acc": 1, "nme": "key", "dsc": "Lsun/rmi/server/LoaderHandler$LoaderKey;"}, {"acc": 1, "nme": "removed", "dsc": "Z"}]}, "classes/sun/rmi/transport/tcp/ConnectionAcceptor.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/ConnectionAcceptor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/tcp/TCPTransport;)V"}, {"nme": "startNewAcceptor", "acc": 1, "dsc": "()V"}, {"nme": "accept", "acc": 1, "dsc": "(Lsun/rmi/transport/Connection;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "transport", "dsc": "Lsun/rmi/transport/tcp/TCPTransport;"}, {"acc": 2, "nme": "queue", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/rmi/transport/Connection;>;"}, {"acc": 10, "nme": "threadNum", "dsc": "I"}]}, "classes/java/rmi/server/RemoteStub.class": {"ver": 65, "acc": 132129, "nme": "java/rmi/server/RemoteStub", "super": "java/rmi/server/RemoteObject", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "setRef", "acc": 131084, "dsc": "(Ljava/rmi/server/RemoteStub;Ljava/rmi/server/RemoteRef;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -1585587260594494182}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/sun/rmi/registry/RegistryImpl_Stub.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/registry/RegistryImpl_Stub", "super": "java/rmi/server/RemoteStub", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "bind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/AccessException", "java/rmi/AlreadyBoundException", "java/rmi/RemoteException"]}, {"nme": "list", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/AccessException", "java/rmi/RemoteException"]}, {"nme": "lookup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/AccessException", "java/rmi/NotBoundException", "java/rmi/RemoteException"]}, {"nme": "rebind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/AccessException", "java/rmi/RemoteException"]}, {"nme": "unbind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/AccessException", "java/rmi/NotBoundException", "java/rmi/RemoteException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "operations", "dsc": "[Ljava/rmi/server/Operation;"}, {"acc": 26, "nme": "interfaceHash", "dsc": "J", "val": 4905912898345647071}]}, "classes/sun/rmi/runtime/Log$InternalStreamHandler.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$InternalStreamHandler", "super": "java/util/logging/StreamHandler", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "publish", "acc": 1, "dsc": "(Ljava/util/logging/LogRecord;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}], "flds": []}, "classes/sun/rmi/transport/DGCImpl.class": {"ver": 65, "acc": 48, "nme": "sun/rmi/transport/DGCImpl", "super": "java/lang/Object", "mthds": [{"nme": "getDGCImpl", "acc": 8, "dsc": "()Lsun/rmi/transport/DGCImpl;"}, {"nme": "initDgcFilter", "acc": 10, "dsc": "()Ljava/io/ObjectInputFilter;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "dirty", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/rmi/server/ObjID;JLjava/rmi/dgc/Lease;)Ljava/rmi/dgc/Lease;"}, {"nme": "clean", "acc": 1, "dsc": "([Ljava/rmi/server/ObjID;JLjava/rmi/dgc/VMID;Z)V"}, {"nme": "registerTarget", "acc": 0, "dsc": "(Ljava/rmi/dgc/VMID;Lsun/rmi/transport/Target;)V"}, {"nme": "unregisterTarget", "acc": 0, "dsc": "(Ljava/rmi/dgc/VMID;Lsun/rmi/transport/Target;)V"}, {"nme": "checkLeases", "acc": 2, "dsc": "()V"}, {"nme": "exportSingleton", "acc": 10, "dsc": "()V"}, {"nme": "checkInput", "acc": 10, "dsc": "(Ljava/io/ObjectInputFilter$FilterInfo;)Ljava/io/ObjectInputFilter$Status;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "dgcLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "leaseValue", "dsc": "J"}, {"acc": 26, "nme": "leaseCheckInterval", "dsc": "J"}, {"acc": 26, "nme": "scheduler", "dsc": "Ljava/util/concurrent/ScheduledExecutorService;"}, {"acc": 10, "nme": "dgc", "dsc": "Lsun/rmi/transport/DGCImpl;"}, {"acc": 2, "nme": "leaseTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/rmi/dgc/VMID;Lsun/rmi/transport/DGCImpl$LeaseInfo;>;"}, {"acc": 2, "nme": "checker", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Future;", "sig": "Ljava/util/concurrent/Future<*>;"}, {"acc": 26, "nme": "DGC_FILTER_PROPNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.rmi.transport.dgcFilter"}, {"acc": 10, "nme": "DGC_MAX_DEPTH", "dsc": "I"}, {"acc": 10, "nme": "DGC_MAX_ARRAY_SIZE", "dsc": "I"}, {"acc": 26, "nme": "dgc<PERSON><PERSON>er", "dsc": "Ljava/io/ObjectInputFilter;"}]}, "classes/sun/rmi/runtime/Log$LoggerLog$2.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LoggerLog$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/runtime/Log$LoggerLog;Ljava/util/logging/Logger;Ljava/util/logging/Level;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$logger", "dsc": "Ljava/util/logging/Logger;"}, {"acc": 4112, "nme": "val$level", "dsc": "Ljava/util/logging/Level;"}]}, "classes/java/rmi/registry/RegistryHandler.class": {"ver": 65, "acc": 132609, "nme": "java/rmi/registry/RegistryHandler", "super": "java/lang/Object", "mthds": [{"nme": "registryStub", "acc": 132097, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException", "java/rmi/UnknownHostException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "registryImpl", "acc": 132097, "dsc": "(I)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/sun/rmi/transport/GC$Daemon$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/GC$Daemon$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/server/WeakClassHashMap$ValueCell.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/WeakClassHashMap$ValueCell", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "ref", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/Reference;", "sig": "Ljava/lang/ref/Reference<TT;>;"}]}, "classes/sun/rmi/transport/ObjectTable$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/ObjectTable$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/server/Dispatcher.class": {"ver": 65, "acc": 1537, "nme": "sun/rmi/server/Dispatcher", "super": "java/lang/Object", "mthds": [{"nme": "dispatch", "acc": 1025, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;)V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/sun/rmi/transport/tcp/TCPChannel$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/tcp/TCPChannel$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPChannel;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/tcp/TCPChannel;"}]}, "classes/sun/rmi/transport/GC.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/GC", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "maxObjectInspectionAge", "acc": 265, "dsc": "()J"}, {"nme": "setLatencyTarget", "acc": 10, "dsc": "(J)V"}, {"nme": "requestLatency", "acc": 9, "dsc": "(J)Lsun/rmi/transport/GC$LatencyRequest;"}, {"nme": "currentLatencyTarget", "acc": 9, "dsc": "()J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "NO_TARGET", "dsc": "J", "val": 9223372036854775807}, {"acc": 10, "nme": "latencyTarget", "dsc": "J"}, {"acc": 10, "nme": "daemon", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 10, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/rmi/log/ReliableLog$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/log/ReliableLog$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/transport/ConnectionInputStream.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/ConnectionInputStream", "super": "sun/rmi/server/MarshalInputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "readID", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "saveRef", "acc": 0, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "discardRefs", "acc": 0, "dsc": "()V"}, {"nme": "registerRefs", "acc": 0, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON>ck<PERSON><PERSON>ed", "acc": 0, "dsc": "()V"}, {"nme": "done", "acc": 0, "dsc": "(Lsun/rmi/transport/Connection;)V"}], "flds": [{"acc": 2, "nme": "dgcAckNeeded", "dsc": "Z"}, {"acc": 2, "nme": "incomingRefTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/Endpoint;Ljava/util/List<Lsun/rmi/transport/LiveRef;>;>;"}, {"acc": 2, "nme": "ackID", "dsc": "Ljava/rmi/server/UID;"}]}, "classes/java/rmi/server/RemoteCall.class": {"ver": 65, "acc": 132609, "nme": "java/rmi/server/RemoteCall", "super": "java/lang/Object", "mthds": [{"nme": "getOutputStream", "acc": 132097, "dsc": "()Ljava/io/ObjectOutput;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "releaseOutputStream", "acc": 132097, "dsc": "()V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getInputStream", "acc": 132097, "dsc": "()Ljava/io/ObjectInput;", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "releaseInputStream", "acc": 132097, "dsc": "()V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getResultStream", "acc": 132097, "dsc": "(Z)Ljava/io/ObjectOutput;", "exs": ["java/io/IOException", "java/io/StreamCorruptedException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "executeCall", "acc": 132097, "dsc": "()V", "exs": ["java/lang/Exception"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "done", "acc": 132097, "dsc": "()V", "exs": ["java/io/IOException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/SkeletonMismatchException.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/server/SkeletonMismatchException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -7780460454818859281}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/ConnectIOException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/ConnectIOException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -8087809532704668744}]}, "classes/sun/rmi/transport/ObjectTable.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/transport/ObjectTable", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 8, "dsc": "(Lsun/rmi/transport/ObjectEndpoint;)Lsun/rmi/transport/Target;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Ljava/rmi/Remote;)Lsun/rmi/transport/Target;"}, {"nme": "getStub", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "unexportObject", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Z)Z", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 8, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/server/ExportException"]}, {"nme": "remove<PERSON>arget", "acc": 10, "dsc": "(Lsun/rmi/transport/Target;)V"}, {"nme": "referenced", "acc": 8, "dsc": "(Ljava/rmi/server/ObjID;JLjava/rmi/dgc/VMID;)V"}, {"nme": "unreferenced", "acc": 8, "dsc": "(Ljava/rmi/server/ObjID;JLjava/rmi/dgc/VMID;Z)V"}, {"nme": "incrementKeepAliveCount", "acc": 8, "dsc": "()V"}, {"nme": "decrementKeepAliveCount", "acc": 8, "dsc": "()V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "gcInterval", "dsc": "J"}, {"acc": 26, "nme": "tableLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "objTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/ObjectEndpoint;Lsun/rmi/transport/Target;>;"}, {"acc": 26, "nme": "implTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/WeakRef;Lsun/rmi/transport/Target;>;"}, {"acc": 26, "nme": "keepAliveLock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "keepAliveCount", "dsc": "I"}, {"acc": 10, "nme": "reaper", "dsc": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"acc": 24, "nme": "reapQueue", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/ReferenceQueue;", "sig": "Ljava/lang/ref/ReferenceQueue<Ljava/lang/Object;>;"}, {"acc": 10, "nme": "gcLatencyRequest", "dsc": "Lsun/rmi/transport/GC$LatencyRequest;"}]}, "classes/java/rmi/server/RemoteObjectInvocationHandler.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/RemoteObjectInvocationHandler", "super": "java/rmi/server/RemoteObject", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Throwable"]}, {"nme": "invokeObjectMethod", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "invokeRemoteMethod", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "proxyToString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "readObjectNoData", "acc": 2, "dsc": "()V", "exs": ["java/io/InvalidObjectException"]}, {"nme": "getMethodHash", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;)J"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 2}, {"acc": 26, "nme": "methodToHash_Maps", "dsc": "Ljava/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps;"}]}, "classes/java/rmi/NoSuchObjectException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/NoSuchObjectException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6619395951570472985}]}, "classes/sun/rmi/server/MarshalInputStream.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/MarshalInputStream", "super": "java/io/ObjectInputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V", "exs": ["java/io/IOException", "java/io/StreamCorruptedException"]}, {"nme": "getDoneCallback", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"nme": "setDoneCallback", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "done", "acc": 1, "dsc": "()V"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "resolveClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectStreamClass;)Ljava/lang/Class;", "sig": "(Ljava/io/ObjectStreamClass;)Ljava/lang/Class<*>;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "resolveProxyClass", "acc": 4, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "latestUserDefinedLoader", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "checkSunClass", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/security/AccessControlException;)Ljava/lang/Class;", "sig": "(Ljava/lang/String;Ljava/security/AccessControlException;)Ljava/lang/Class<*>;", "exs": ["java/security/AccessControlException"]}, {"nme": "readLocation", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "skipDefaultResolveClass", "acc": 0, "dsc": "()V"}, {"nme": "useCodebaseOnly", "acc": 0, "dsc": "()V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "useCodebaseOnlyProperty", "dsc": "Z"}, {"acc": 12, "nme": "permittedSunClasses", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Class<*>;>;"}, {"acc": 2, "nme": "skipDefaultResolveClass", "dsc": "Z"}, {"acc": 18, "nme": "doneCallbacks", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Object;Ljava/lang/Runnable;>;"}, {"acc": 2, "nme": "useCodebaseOnly", "dsc": "Z"}]}, "classes/java/rmi/MarshalledObject$MarshalledObjectInputStream.class": {"ver": 65, "acc": 32, "nme": "java/rmi/MarshalledObject$MarshalledObjectInputStream", "super": "sun/rmi/server/MarshalInputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/InputStream;Ljava/io/ObjectInputFilter;)V", "exs": ["java/io/IOException"]}, {"nme": "readLocation", "acc": 4, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectInputFilter;)<PERSON>ja<PERSON>/lang/Void;"}], "flds": [{"acc": 2, "nme": "locIn", "dsc": "Ljava/io/ObjectInputStream;"}]}, "classes/java/rmi/AlreadyBoundException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/AlreadyBoundException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 9218657361741657110}]}, "classes/sun/rmi/registry/RegistryImpl$4.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/net/InetAddress;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/io/IOException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$finalClientHost", "dsc": "Ljava/net/InetAddress;"}]}, "classes/sun/rmi/server/UnicastRef.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/server/UnicastRef", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Lsun/rmi/transport/LiveRef;)V"}, {"nme": "getLiveRef", "acc": 1, "dsc": "()Lsun/rmi/transport/LiveRef;"}, {"nme": "invoke", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/Object;J)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "marshalCustomCallData", "acc": 4, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Object;Lja<PERSON>/io/ObjectOutput;)V", "sig": "(L<PERSON><PERSON>/lang/Class<*>;Ljava/lang/Object;Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "unmarshalValue", "acc": 12, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/io/ObjectInput;)Ljava/lang/Object;", "sig": "(Lja<PERSON>/lang/Class<*>;Ljava/io/ObjectInput;)Ljava/lang/Object;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "newCall", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteObject;[Ljava/rmi/server/Operation;IJ)Ljava/rmi/server/RemoteCall;", "exs": ["java/rmi/RemoteException"]}, {"nme": "invoke", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/lang/Exception"]}, {"nme": "free", "acc": 2, "dsc": "(Ljava/rmi/server/RemoteCall;Z)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "done", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteCall;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "logClientCall", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getRefClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/ObjectOutput;)Ljava/lang/String;"}, {"nme": "writeExternal", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "readExternal", "acc": 1, "dsc": "(Ljava/io/ObjectInput;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "remoteToString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "remoteHashCode", "acc": 1, "dsc": "()I"}, {"nme": "remoteEquals", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)Z"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "clientRefLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 25, "nme": "clientCallLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 8258372400816541186}, {"acc": 4, "nme": "ref", "dsc": "Lsun/rmi/transport/LiveRef;"}]}, "classes/java/rmi/server/RMIFailureHandler.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/server/RMIFailureHandler", "super": "java/lang/Object", "mthds": [{"nme": "failure", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)Z"}], "flds": []}, "classes/sun/rmi/runtime/RuntimeUtil$GetInstanceAction.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/runtime/RuntimeUtil$GetInstanceAction", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Lsun/rmi/runtime/RuntimeUtil;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/server/SkeletonNotFoundException.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/server/SkeletonNotFoundException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -7860299673822761231}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/sun/rmi/transport/DGCAckHandler.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/DGCAckHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/rmi/server/UID;)V"}, {"nme": "add", "acc": 32, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "startTimer", "acc": 32, "dsc": "()V"}, {"nme": "release", "acc": 32, "dsc": "()V"}, {"nme": "received", "acc": 9, "dsc": "(Ljava/rmi/server/UID;)V"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "dgcAckTimeout", "dsc": "J"}, {"acc": 26, "nme": "scheduler", "dsc": "Ljava/util/concurrent/ScheduledExecutorService;"}, {"acc": 26, "nme": "idTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/rmi/server/UID;Lsun/rmi/transport/DGCAckHandler;>;"}, {"acc": 18, "nme": "id", "dsc": "Ljava/rmi/server/UID;"}, {"acc": 2, "nme": "objList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/Object;>;"}, {"acc": 2, "nme": "task", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Future;", "sig": "Ljava/util/concurrent/Future<*>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/transport/tcp/TCPTransport.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/tcp/TCPTransport", "super": "sun/rmi/transport/Transport", "mthds": [{"nme": "createNopermsAcc", "acc": 10, "dsc": "()Ljava/security/AccessControlContext;"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/LinkedList;)V", "sig": "(Ljava/util/LinkedList<Lsun/rmi/transport/tcp/TCPEndpoint;>;)V"}, {"nme": "shedConnectionCaches", "acc": 1, "dsc": "()V"}, {"nme": "getChannel", "acc": 1, "dsc": "(Lsun/rmi/transport/Endpoint;)Lsun/rmi/transport/tcp/TCPChannel;"}, {"nme": "free", "acc": 1, "dsc": "(Lsun/rmi/transport/Endpoint;)V"}, {"nme": "exportObject", "acc": 1, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "targetUnexported", "acc": 36, "dsc": "()V"}, {"nme": "decrementExportCount", "acc": 2, "dsc": "()V"}, {"nme": "checkAcceptPermission", "acc": 4, "dsc": "(Ljava/security/AccessControlContext;)V"}, {"nme": "getEndpoint", "acc": 2, "dsc": "()Lsun/rmi/transport/tcp/TCPEndpoint;"}, {"nme": "listen", "acc": 2, "dsc": "()V", "exs": ["java/rmi/RemoteException"]}, {"nme": "closeSocket", "acc": 10, "dsc": "(Ljava/net/Socket;)V"}, {"nme": "handleMessages", "acc": 0, "dsc": "(Lsun/rmi/transport/Connection;Z)V"}, {"nme": "getClientHost", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/server/ServerNotActiveException"]}, {"nme": "getChannel", "acc": 4161, "dsc": "(Lsun/rmi/transport/Endpoint;)Lsun/rmi/transport/Channel;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "tcpLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "maxConnectionThreads", "dsc": "I"}, {"acc": 26, "nme": "threadKeepAliveTime", "dsc": "J"}, {"acc": 26, "nme": "connectionThreadPool", "dsc": "Ljava/util/concurrent/ExecutorService;"}, {"acc": 26, "nme": "connectionCount", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 26, "nme": "threadConnectionHandler", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Lsun/rmi/transport/tcp/TCPTransport$ConnectionHandler;>;"}, {"acc": 26, "nme": "NOPERMS_ACC", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 18, "nme": "epList", "dsc": "<PERSON><PERSON><PERSON>/util/LinkedList;", "sig": "Ljava/util/LinkedList<Lsun/rmi/transport/tcp/TCPEndpoint;>;"}, {"acc": 2, "nme": "exportCount", "dsc": "I"}, {"acc": 2, "nme": "server", "dsc": "Ljava/net/ServerSocket;"}, {"acc": 18, "nme": "channelTable", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/tcp/TCPEndpoint;Ljava/lang/ref/Reference<Lsun/rmi/transport/tcp/TCPChannel;>;>;"}, {"acc": 24, "nme": "defaultSocketFactory", "dsc": "Ljava/rmi/server/RMISocketFactory;"}, {"acc": 26, "nme": "connectionReadTimeout", "dsc": "I"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/sun/rmi/transport/tcp/TCPConnection.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/tcp/TCPConnection", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPChannel;Ljava/net/Socket;Ljava/io/InputStream;Ljava/io/OutputStream;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPChannel;Ljava/io/InputStream;Ljava/io/OutputStream;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPChannel;Ljava/net/Socket;)V"}, {"nme": "getOutputStream", "acc": 1, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "releaseOutputStream", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "releaseInputStream", "acc": 1, "dsc": "()V"}, {"nme": "isReusable", "acc": 1, "dsc": "()Z"}, {"nme": "setExpiration", "acc": 0, "dsc": "(J)V"}, {"nme": "setLastUseTime", "acc": 0, "dsc": "(J)V"}, {"nme": "expired", "acc": 0, "dsc": "(J)Z"}, {"nme": "isDead", "acc": 1, "dsc": "()Z"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getChannel", "acc": 1, "dsc": "()Lsun/rmi/transport/Channel;"}], "flds": [{"acc": 2, "nme": "socket", "dsc": "Ljava/net/Socket;"}, {"acc": 2, "nme": "channel", "dsc": "Lsun/rmi/transport/Channel;"}, {"acc": 2, "nme": "in", "dsc": "Ljava/io/InputStream;"}, {"acc": 2, "nme": "out", "dsc": "Ljava/io/OutputStream;"}, {"acc": 2, "nme": "expiration", "dsc": "J"}, {"acc": 2, "nme": "lastuse", "dsc": "J"}, {"acc": 2, "nme": "roundtrip", "dsc": "J"}]}, "classes/sun/rmi/transport/WeakRef.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/WeakRef", "super": "java/lang/ref/WeakReference", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON>ja<PERSON>/lang/ref/ReferenceQueue;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/ref/ReferenceQueue<Ljava/lang/Object;>;)V"}, {"nme": "pin", "acc": 33, "dsc": "()V"}, {"nme": "unpin", "acc": 33, "dsc": "()V"}, {"nme": "setHashValue", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 2, "nme": "hashValue", "dsc": "I"}, {"acc": 2, "nme": "strongRef", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/sun/rmi/runtime/NewThreadAction$2.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/NewThreadAction$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/lang/ThreadGroup;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/server/RMIClassLoader.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/RMIClassLoader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "loadClass", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "loadClass", "acc": 9, "dsc": "(Ljava/net/URL;Ljava/lang/String;)Ljava/lang/Class;", "sig": "(Ljava/net/URL;Ljava/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;Lja<PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException", "java/net/MalformedURLException"]}, {"nme": "getClassLoader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/ClassLoader;", "exs": ["java/net/MalformedURLException", "java/lang/SecurityException"]}, {"nme": "getClassAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "getDefaultProviderInstance", "acc": 9, "dsc": "()Ljava/rmi/server/RMIClassLoaderSpi;"}, {"nme": "getSecurityContext", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON><PERSON><PERSON>/lang/Object;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "newDefaultProviderInstance", "acc": 10, "dsc": "()Ljava/rmi/server/RMIClassLoaderSpi;"}, {"nme": "initializeProvider", "acc": 10, "dsc": "()Ljava/rmi/server/RMIClassLoaderSpi;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "defaultProvider", "dsc": "Ljava/rmi/server/RMIClassLoaderSpi;"}, {"acc": 26, "nme": "provider", "dsc": "Ljava/rmi/server/RMIClassLoaderSpi;"}]}, "classes/java/rmi/RMISecurityException.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/RMISecurityException", "super": "java/lang/SecurityException", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -8433406075740433514}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/RemoteException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/RemoteException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCause", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -5148567311918794206}, {"acc": 1, "nme": "detail", "dsc": "<PERSON><PERSON><PERSON>/lang/Throwable;"}]}, "classes/sun/rmi/transport/LiveRef.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/LiveRef", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/ObjID;Lsun/rmi/transport/Endpoint;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/ObjID;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/ObjID;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V"}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "getClientSocketFactory", "acc": 1, "dsc": "()Ljava/rmi/server/RMIClientSocketFactory;"}, {"nme": "getServerSocketFactory", "acc": 1, "dsc": "()Ljava/rmi/server/RMIServerSocketFactory;"}, {"nme": "exportObject", "acc": 1, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "getChannel", "acc": 1, "dsc": "()Lsun/rmi/transport/Channel;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getObjID", "acc": 1, "dsc": "()Ljava/rmi/server/ObjID;"}, {"nme": "getEndpoint", "acc": 0, "dsc": "()Lsun/rmi/transport/Endpoint;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "remoteEquals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;Z)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 9, "dsc": "(Ljava/io/ObjectInput;Z)Lsun/rmi/transport/LiveRef;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 18, "nme": "ep", "dsc": "Lsun/rmi/transport/Endpoint;"}, {"acc": 18, "nme": "id", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 130, "nme": "ch", "dsc": "Lsun/rmi/transport/Channel;"}, {"acc": 18, "nme": "isLocal", "dsc": "Z"}]}, "classes/sun/rmi/registry/RegistryImpl_Skel.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/registry/RegistryImpl_Skel", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getOperations", "acc": 1, "dsc": "()[Ljava/rmi/server/Operation;"}, {"nme": "dispatch", "acc": 1, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;IJ)V", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "operations", "dsc": "[Ljava/rmi/server/Operation;"}, {"acc": 26, "nme": "interfaceHash", "dsc": "J", "val": 4905912898345647071}]}, "classes/java/rmi/ConnectException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/ConnectException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4863550261346652506}]}, "classes/java/rmi/server/RMIServerSocketFactory.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/server/RMIServerSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "createServerSocket", "acc": 1025, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/java/rmi/server/RMIClassLoader$1.class": {"ver": 65, "acc": 32, "nme": "java/rmi/server/RMIClassLoader$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/rmi/server/RMIClassLoaderSpi;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/server/Util$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/Util$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;[Ljava/lang/Class;Ljava/lang/reflect/InvocationHandler;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON>va/rmi/Remote;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$loader", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 4112, "nme": "val$interfaces", "dsc": "[Ljava/lang/Class;"}, {"acc": 4112, "nme": "val$handler", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/InvocationHandler;"}]}, "classes/sun/rmi/server/UnicastServerRef$HashToMethod_Maps$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/UnicastServerRef$HashToMethod_Maps$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/server/UnicastServerRef$HashToMethod_Maps;Ljava/lang/reflect/Method;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$m", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Method;"}]}, "classes/java/rmi/registry/Registry.class": {"ver": 65, "acc": 1537, "nme": "java/rmi/registry/Registry", "super": "java/lang/Object", "mthds": [{"nme": "lookup", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/RemoteException", "java/rmi/NotBoundException", "java/rmi/AccessException"]}, {"nme": "bind", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException", "java/rmi/AlreadyBoundException", "java/rmi/AccessException"]}, {"nme": "unbind", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/RemoteException", "java/rmi/NotBoundException", "java/rmi/AccessException"]}, {"nme": "rebind", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException", "java/rmi/AccessException"]}, {"nme": "list", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/RemoteException", "java/rmi/AccessException"]}], "flds": [{"acc": 25, "nme": "REGISTRY_PORT", "dsc": "I", "val": 1099}]}, "classes/sun/rmi/log/LogHandler.class": {"ver": 65, "acc": 1057, "nme": "sun/rmi/log/LogHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "initialSnapshot", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "snapshot", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/OutputStream;<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/lang/Exception"]}, {"nme": "recover", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "writeUpdate", "acc": 1, "dsc": "(Lsun/rmi/log/LogOutputStream;L<PERSON><PERSON>/lang/Object;)V", "exs": ["java/lang/Exception"]}, {"nme": "readUpdate", "acc": 1, "dsc": "(Lsun/rmi/log/LogInputStream;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "exs": ["java/lang/Exception"]}, {"nme": "applyUpdate", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/java/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps.class": {"ver": 65, "acc": 32, "nme": "java/rmi/server/RemoteObjectInvocationHandler$MethodToHash_Maps", "super": "sun/rmi/server/WeakClassHashMap", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "computeValue", "acc": 4, "dsc": "(L<PERSON><PERSON>/lang/Class;)Ljava/util/Map;", "sig": "(Ljava/lang/Class<*>;)Ljava/util/Map<Ljava/lang/reflect/Method;Ljava/lang/Long;>;"}, {"nme": "computeValue", "acc": 4164, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>ja<PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/dgc/Lease.class": {"ver": 65, "acc": 49, "nme": "java/rmi/dgc/Lease", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/dgc/VMID;J)V"}, {"nme": "getVMID", "acc": 1, "dsc": "()Ljava/rmi/dgc/VMID;"}, {"nme": "getValue", "acc": 1, "dsc": "()J"}], "flds": [{"acc": 2, "nme": "vmid", "dsc": "Ljava/rmi/dgc/VMID;"}, {"acc": 2, "nme": "value", "dsc": "J"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -5713411624328831948}]}, "classes/sun/rmi/registry/RegistryImpl$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/registry/RegistryImpl;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/rmi/RemoteException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$port", "dsc": "I"}, {"acc": 4112, "nme": "val$csf", "dsc": "Ljava/rmi/server/RMIClientSocketFactory;"}, {"acc": 4112, "nme": "val$ssf", "dsc": "Ljava/rmi/server/RMIServerSocketFactory;"}, {"acc": 4112, "nme": "val$serialFilter", "dsc": "Ljava/io/ObjectInputFilter;"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/registry/RegistryImpl;"}]}, "classes/sun/rmi/runtime/Log$LoggerLog$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LoggerLog$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/util/logging/Handler;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/rmi/runtime/Log$LogStreamLog.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LogStreamLog", "super": "sun/rmi/runtime/Log", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/rmi/server/LogStream;Ljava/util/logging/Level;)V"}, {"nme": "isLoggable", "acc": 33, "dsc": "(Ljava/util/logging/Level;)Z"}, {"nme": "log", "acc": 1, "dsc": "(Ljava/util/logging/Level;Ljava/lang/String;)V"}, {"nme": "log", "acc": 1, "dsc": "(L<PERSON><PERSON>/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V"}, {"nme": "getPrintStream", "acc": 1, "dsc": "()Ljava/io/PrintStream;"}, {"nme": "setOutputStream", "acc": 33, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "unqualifiedName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "stream", "dsc": "Ljava/rmi/server/LogStream;"}, {"acc": 2, "nme": "levelValue", "dsc": "I"}]}, "classes/sun/rmi/runtime/Log$LoggerLogFactory.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LoggerLogFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "createLog", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;Ljava/util/logging/Level;)Lsun/rmi/runtime/Log;"}], "flds": []}, "classes/sun/rmi/server/LoaderHandler$LoaderKey.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/LoaderHandler$LoaderKey", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([Ljava/net/URL;Ljava/lang/ClassLoader;)V"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 2, "nme": "urls", "dsc": "[Ljava/net/URL;"}, {"acc": 2, "nme": "parent", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "hashValue", "dsc": "I"}]}, "classes/java/rmi/server/ServerNotActiveException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/ServerNotActiveException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4687940720827538231}]}, "classes/sun/rmi/transport/Target.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/transport/Target", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/Remote;Lsun/rmi/server/Dispatcher;Ljava/rmi/Remote;Ljava/rmi/server/ObjID;Z)V"}, {"nme": "checkLoaderAncestry", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Z"}, {"nme": "getStub", "acc": 1, "dsc": "()<PERSON><PERSON>va/rmi/Remote;"}, {"nme": "getObjectEndpoint", "acc": 0, "dsc": "()Lsun/rmi/transport/ObjectEndpoint;"}, {"nme": "getWeakImpl", "acc": 0, "dsc": "()Lsun/rmi/transport/WeakRef;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Lsun/rmi/server/Dispatcher;"}, {"nme": "getAccessControlContext", "acc": 0, "dsc": "()Ljava/security/AccessControlContext;"}, {"nme": "getContextClassLoader", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "getImpl", "acc": 0, "dsc": "()<PERSON><PERSON>va/rmi/Remote;"}, {"nme": "isPermanent", "acc": 0, "dsc": "()Z"}, {"nme": "pinImpl", "acc": 32, "dsc": "()V"}, {"nme": "unpinImpl", "acc": 32, "dsc": "()V"}, {"nme": "setExportedTransport", "acc": 0, "dsc": "(Lsun/rmi/transport/Transport;)V"}, {"nme": "referenced", "acc": 32, "dsc": "(JLjava/rmi/dgc/VMID;)V"}, {"nme": "unreferenced", "acc": 32, "dsc": "(JLjava/rmi/dgc/VMID;Z)V"}, {"nme": "refSetRemove", "acc": 34, "dsc": "(<PERSON><PERSON><PERSON>/rmi/dgc/VMID;)V"}, {"nme": "unexport", "acc": 32, "dsc": "(Z)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 32, "dsc": "()V"}, {"nme": "incrementCallCount", "acc": 32, "dsc": "()V", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "decrementCallCount", "acc": 32, "dsc": "()V"}, {"nme": "isEmpty", "acc": 0, "dsc": "()Z"}, {"nme": "vmidDead", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/rmi/dgc/VMID;)V"}, {"nme": "lambda$refSetRemove$1", "acc": 4098, "dsc": "(Ljava/rmi/server/Unreferenced;)V"}, {"nme": "lambda$refSetRemove$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/rmi/server/Unreferenced;)Ljava/lang/Void;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "id", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 18, "nme": "permanent", "dsc": "Z"}, {"acc": 18, "nme": "weakImpl", "dsc": "Lsun/rmi/transport/WeakRef;"}, {"acc": 66, "nme": "disp", "dsc": "Lsun/rmi/server/Dispatcher;"}, {"acc": 18, "nme": "stub", "dsc": "<PERSON><PERSON><PERSON>/rmi/Remote;"}, {"acc": 18, "nme": "refSet", "dsc": "<PERSON><PERSON><PERSON>/util/Vector;", "sig": "Ljava/util/Vector<Ljava/rmi/dgc/VMID;>;"}, {"acc": 18, "nme": "sequenceTable", "dsc": "<PERSON><PERSON><PERSON>/util/Hashtable;", "sig": "Ljava/util/Hashtable<Ljava/rmi/dgc/VMID;Lsun/rmi/transport/SequenceEntry;>;"}, {"acc": 18, "nme": "acc", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 18, "nme": "ccl", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "callCount", "dsc": "I"}, {"acc": 2, "nme": "removed", "dsc": "Z"}, {"acc": 66, "nme": "exportedTransport", "dsc": "Lsun/rmi/transport/Transport;"}, {"acc": 10, "nme": "nextThreadNum", "dsc": "I"}]}, "classes/java/rmi/server/RemoteServer.class": {"ver": 65, "acc": 1057, "nme": "java/rmi/server/RemoteServer", "super": "java/rmi/server/RemoteObject", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "getClientHost", "acc": 9, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/rmi/server/ServerNotActiveException"]}, {"nme": "setLog", "acc": 9, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "getLog", "acc": 9, "dsc": "()Ljava/io/PrintStream;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -4100238210092549637}, {"acc": 10, "nme": "logNull", "dsc": "Z"}]}, "classes/java/rmi/UnmarshalException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/UnmarshalException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 594380845140740218}]}, "classes/java/rmi/server/ExportException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/ExportException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -9155485338494060170}]}, "classes/sun/rmi/transport/tcp/TCPChannel.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/tcp/TCPChannel", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/transport/tcp/TCPTransport;Lsun/rmi/transport/tcp/TCPEndpoint;)V"}, {"nme": "getEndpoint", "acc": 1, "dsc": "()Lsun/rmi/transport/Endpoint;"}, {"nme": "checkConnectPermission", "acc": 2, "dsc": "()V", "exs": ["java/lang/SecurityException"]}, {"nme": "newConnection", "acc": 1, "dsc": "()Lsun/rmi/transport/Connection;", "exs": ["java/rmi/RemoteException"]}, {"nme": "createConnection", "acc": 2, "dsc": "()Lsun/rmi/transport/Connection;", "exs": ["java/rmi/RemoteException"]}, {"nme": "free", "acc": 1, "dsc": "(Lsun/rmi/transport/Connection;Z)V"}, {"nme": "writeTransportHeader", "acc": 2, "dsc": "(Ljava/io/DataOutputStream;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "shedCache", "acc": 1, "dsc": "()V"}, {"nme": "freeCachedConnections", "acc": 2, "dsc": "()V"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "ep", "dsc": "Lsun/rmi/transport/tcp/TCPEndpoint;"}, {"acc": 18, "nme": "tr", "dsc": "Lsun/rmi/transport/tcp/TCPTransport;"}, {"acc": 18, "nme": "freeList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lsun/rmi/transport/tcp/TCPConnection;>;"}, {"acc": 2, "nme": "reaper", "dsc": "<PERSON><PERSON><PERSON>/util/concurrent/Future;", "sig": "Ljava/util/concurrent/Future<*>;"}, {"acc": 2, "nme": "acceptor", "dsc": "Lsun/rmi/transport/tcp/ConnectionAcceptor;"}, {"acc": 2, "nme": "okContext", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 2, "nme": "authcache", "dsc": "L<PERSON>va/util/WeakHash<PERSON>ap;", "sig": "Ljava/util/WeakHashMap<Ljava/security/AccessControlContext;Ljava/lang/ref/Reference<Ljava/security/AccessControlContext;>;>;"}, {"acc": 2, "nme": "cacheSecurityManager", "dsc": "<PERSON><PERSON><PERSON>/lang/SecurityManager;"}, {"acc": 26, "nme": "idleTimeout", "dsc": "J"}, {"acc": 26, "nme": "handshakeTimeout", "dsc": "I"}, {"acc": 26, "nme": "responseTimeout", "dsc": "I"}, {"acc": 26, "nme": "scheduler", "dsc": "Ljava/util/concurrent/ScheduledExecutorService;"}]}, "classes/sun/rmi/runtime/RuntimeUtil.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/runtime/RuntimeUtil", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getInstance", "acc": 10, "dsc": "()Lsun/rmi/runtime/RuntimeUtil;"}, {"nme": "getScheduler", "acc": 1, "dsc": "()Ljava/util/concurrent/ScheduledThreadPoolExecutor;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "runtimeLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "schedulerThreads", "dsc": "I"}, {"acc": 26, "nme": "GET_INSTANCE_PERMISSION", "dsc": "Ljava/security/Permission;"}, {"acc": 26, "nme": "instance", "dsc": "Lsun/rmi/runtime/RuntimeUtil;"}, {"acc": 18, "nme": "scheduler", "dsc": "Ljava/util/concurrent/ScheduledThreadPoolExecutor;"}]}, "classes/java/rmi/AccessException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/AccessException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6314925228044966088}]}, "classes/javax/rmi/ssl/SslRMIServerSocketFactory.class": {"ver": 65, "acc": 33, "nme": "javax/rmi/ssl/SslRMIServerSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Z)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/net/ssl/SSLContext;[Ljava/lang/String;[Ljava/lang/String;Z)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getEnabledCipherSuites", "acc": 17, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEnabledProtocols", "acc": 17, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getNeedClientAuth", "acc": 17, "dsc": "()Z"}, {"nme": "createServerSocket", "acc": 1, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "checkParameters", "acc": 2, "dsc": "(Ljavax/rmi/ssl/SslRMIServerSocketFactory;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "getDefaultSSLSocketFactory", "acc": 42, "dsc": "()Ljavax/net/ssl/SSLSocketFactory;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "defaultSSLSocketFactory", "dsc": "Ljavax/net/ssl/SSLSocketFactory;"}, {"acc": 18, "nme": "enabledCipherSuites", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "enabledProtocols", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "needClientAuth", "dsc": "Z"}, {"acc": 2, "nme": "enabledCipherSuitesList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "enabledProtocolsList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "context", "dsc": "Ljavax/net/ssl/SSLContext;"}]}, "classes/sun/rmi/transport/DGCImpl_Skel.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/transport/DGCImpl_Skel", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getOperations", "acc": 1, "dsc": "()[Ljava/rmi/server/Operation;"}, {"nme": "dispatch", "acc": 1, "dsc": "(Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;IJ)V", "exs": ["java/lang/Exception"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "operations", "dsc": "[Ljava/rmi/server/Operation;"}, {"acc": 26, "nme": "interfaceHash", "dsc": "J", "val": -669196253586618813}]}, "classes/sun/rmi/log/ReliableLog.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/log/ReliableLog", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lsun/rmi/log/LogHandler;Z)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Lsun/rmi/log/LogHandler;)V", "exs": ["java/io/IOException"]}, {"nme": "recover", "acc": 33, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "update", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "update", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Z)V", "exs": ["java/io/IOException"]}, {"nme": "getLogClassConstructor", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/reflect/Constructor;", "sig": "()Ljava/lang/reflect/Constructor<+Lsun/rmi/log/ReliableLog$LogFile;>;"}, {"nme": "snapshot", "acc": 33, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "snapshotSize", "acc": 1, "dsc": "()J"}, {"nme": "logSize", "acc": 1, "dsc": "()J"}, {"nme": "writeInt", "acc": 2, "dsc": "(Ljava/io/DataOutput;I)V", "exs": ["java/io/IOException"]}, {"nme": "fName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "versionName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "versionName", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "incrVersion", "acc": 2, "dsc": "()V"}, {"nme": "deleteFile", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "deleteNewVersionFile", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "deleteSnapshot", "acc": 2, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "deleteLogFile", "acc": 2, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "openLogFile", "acc": 2, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "initializeLogFile", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "writeVersionFile", "acc": 2, "dsc": "(Z)V", "exs": ["java/io/IOException"]}, {"nme": "createFirstVersion", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "commitToNewVersion", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "readVersion", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["java/io/IOException"]}, {"nme": "getVersion", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "recoverUpdates", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/io/IOException"]}, {"nme": "lambda$getLogClassConstructor$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "PreferredMajorVersion", "dsc": "I", "val": 0}, {"acc": 25, "nme": "PreferredMinorVersion", "dsc": "I", "val": 2}, {"acc": 2, "nme": "Debug", "dsc": "Z"}, {"acc": 10, "nme": "snapshotPrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "logfilePrefix", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "versionFile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "newVersionFile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "intBytes", "dsc": "I"}, {"acc": 10, "nme": "diskPageSize", "dsc": "J"}, {"acc": 2, "nme": "dir", "dsc": "Ljava/io/File;"}, {"acc": 2, "nme": "version", "dsc": "I"}, {"acc": 2, "nme": "logName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "log", "dsc": "Lsun/rmi/log/ReliableLog$LogFile;"}, {"acc": 2, "nme": "snapshotBytes", "dsc": "J"}, {"acc": 2, "nme": "logBytes", "dsc": "J"}, {"acc": 2, "nme": "logEntries", "dsc": "I"}, {"acc": 2, "nme": "lastSnapshot", "dsc": "J"}, {"acc": 2, "nme": "lastLog", "dsc": "J"}, {"acc": 2, "nme": "handler", "dsc": "Lsun/rmi/log/LogHandler;"}, {"acc": 18, "nme": "intBuf", "dsc": "[B"}, {"acc": 2, "nme": "majorFormatVersion", "dsc": "I"}, {"acc": 2, "nme": "minorFormatVersion", "dsc": "I"}, {"acc": 26, "nme": "logClassConstructor", "dsc": "<PERSON><PERSON><PERSON>/lang/reflect/Constructor;", "sig": "Ljava/lang/reflect/Constructor<+Lsun/rmi/log/ReliableLog$LogFile;>;"}]}, "classes/java/rmi/server/RMIClassLoader$2.class": {"ver": 65, "acc": 32, "nme": "java/rmi/server/RMIClassLoader$2", "super": "java/rmi/server/RMIClassLoaderSpi", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "loadClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;Lja<PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "getClassLoader", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/ClassLoader;", "exs": ["java/net/MalformedURLException"]}, {"nme": "getClassAnnotation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}], "flds": []}, "classes/sun/rmi/transport/GC$LatencyRequest.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/GC$LatencyRequest", "super": "java/lang/Object", "mthds": [{"nme": "adjustLatencyIfNeeded", "acc": 10, "dsc": "()V"}, {"nme": "<init>", "acc": 2, "dsc": "(J)V"}, {"nme": "cancel", "acc": 1, "dsc": "()V"}, {"nme": "compareTo", "acc": 1, "dsc": "(Lsun/rmi/transport/GC$LatencyRequest;)I"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "counter", "dsc": "J"}, {"acc": 10, "nme": "requests", "dsc": "Ljava/util/SortedSet;", "sig": "Ljava/util/SortedSet<Lsun/rmi/transport/GC$LatencyRequest;>;"}, {"acc": 2, "nme": "latency", "dsc": "J"}, {"acc": 2, "nme": "id", "dsc": "J"}]}, "classes/sun/rmi/transport/GC$Daemon.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/GC$Daemon", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "create", "acc": 9, "dsc": "()V"}], "flds": []}, "classes/java/rmi/StubNotFoundException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/StubNotFoundException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -7088199405468872373}]}, "classes/sun/rmi/transport/DGCImpl_Stub.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/transport/DGCImpl_Stub", "super": "java/rmi/server/RemoteStub", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "clean", "acc": 1, "dsc": "([Ljava/rmi/server/ObjID;JLjava/rmi/dgc/VMID;Z)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "dirty", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/rmi/server/ObjID;JLjava/rmi/dgc/Lease;)Ljava/rmi/dgc/Lease;", "exs": ["java/rmi/RemoteException"]}, {"nme": "leaseFilter", "acc": 10, "dsc": "(Ljava/io/ObjectInputFilter$FilterInfo;)Ljava/io/ObjectInputFilter$Status;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "operations", "dsc": "[Ljava/rmi/server/Operation;"}, {"acc": 26, "nme": "interfaceHash", "dsc": "J", "val": -669196253586618813}, {"acc": 10, "nme": "DGCCLIENT_MAX_DEPTH", "dsc": "I"}, {"acc": 10, "nme": "DGCCLIENT_MAX_ARRAY_SIZE", "dsc": "I"}]}, "classes/sun/rmi/transport/TransportConstants.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/TransportConstants", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "Magic", "dsc": "I", "val": 1246907721}, {"acc": 25, "nme": "Version", "dsc": "S", "val": 2}, {"acc": 25, "nme": "StreamProtocol", "dsc": "B", "val": 75}, {"acc": 25, "nme": "SingleOpProtocol", "dsc": "B", "val": 76}, {"acc": 25, "nme": "MultiplexProtocol", "dsc": "B", "val": 77}, {"acc": 25, "nme": "ProtocolAck", "dsc": "B", "val": 78}, {"acc": 25, "nme": "ProtocolNack", "dsc": "B", "val": 79}, {"acc": 25, "nme": "Call", "dsc": "B", "val": 80}, {"acc": 25, "nme": "Return", "dsc": "B", "val": 81}, {"acc": 25, "nme": "<PERSON>", "dsc": "B", "val": 82}, {"acc": 25, "nme": "PingAck", "dsc": "B", "val": 83}, {"acc": 25, "nme": "DGCAck", "dsc": "B", "val": 84}, {"acc": 25, "nme": "NormalReturn", "dsc": "B", "val": 1}, {"acc": 25, "nme": "ExceptionalReturn", "dsc": "B", "val": 2}]}, "classes/sun/rmi/server/LoaderHandler.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/server/LoaderHandler", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getDefaultCodebaseURLs", "acc": 42, "dsc": "()[Ljava/net/URL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "loadClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "getClassAnnotation", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/String;", "sig": "(<PERSON><PERSON><PERSON>/lang/Class<*>;)Ljava/lang/String;"}, {"nme": "getClassLoader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/ClassLoader;", "exs": ["java/net/MalformedURLException"]}, {"nme": "getSecurityContext", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "registerCodebaseLoader", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "loadClass", "acc": 10, "dsc": "([Ljava/net/URL;Ljava/lang/String;)Ljava/lang/Class;", "sig": "([Lja<PERSON>/net/URL;Ljava/lang/String;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;Lja<PERSON>/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/net/MalformedURLException", "java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/ClassLoader;Z)Ljava/lang/Class;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;<PERSON><PERSON><PERSON>/lang/ClassLoader;Z)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "loadProxyClass", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;[Ljava/lang/Class<*>;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "loadProxyInterfaces", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;[<PERSON>java/lang/Class;[Z)<PERSON>java/lang/ClassLoader;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ClassLoader;[Ljava/lang/Class<*>;[Z)Ljava/lang/ClassLoader;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "pathToURLs", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;)[Ljava/net/URL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "urlsToPath", "acc": 10, "dsc": "([Ljava/net/URL;)Ljava/lang/String;"}, {"nme": "getRMIContextClassLoader", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"nme": "lookup<PERSON><PERSON><PERSON>", "acc": 10, "dsc": "([Ljava/net/URL;Ljava/lang/ClassLoader;)Lsun/rmi/server/LoaderHandler$Loader;"}, {"nme": "getLoaderAccessControlContext", "acc": 10, "dsc": "([Ljava/net/URL;)Ljava/security/AccessControlContext;"}, {"nme": "addPermissionsForURLs", "acc": 10, "dsc": "([Ljava/net/URL;Ljava/security/PermissionCollection;Z)V"}, {"nme": "loadClassForName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/ClassLoader;)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Z<PERSON><PERSON>va/lang/ClassLoader;)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "logLevel", "dsc": "I"}, {"acc": 24, "nme": "loaderLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 10, "nme": "codebaseProperty", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "codebaseURLs", "dsc": "[Ljava/net/URL;"}, {"acc": 26, "nme": "codebaseLoaders", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/ClassLoader;Ljava/lang/Void;>;"}, {"acc": 26, "nme": "loaderTable", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "Ljava/util/HashMap<Lsun/rmi/server/LoaderHandler$LoaderKey;Lsun/rmi/server/LoaderHandler$LoaderEntry;>;"}, {"acc": 26, "nme": "refQueue", "dsc": "<PERSON><PERSON><PERSON>/lang/ref/ReferenceQueue;", "sig": "Ljava/lang/ref/ReferenceQueue<Lsun/rmi/server/LoaderHandler$Loader;>;"}, {"acc": 26, "nme": "pathToURLsCache", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;[Ljava/lang/Object;>;"}]}, "classes/java/rmi/NotBoundException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/NotBoundException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -1857741824849069317}]}, "classes/sun/rmi/server/LoaderHandler$Loader.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/LoaderHandler$Loader", "super": "java/net/URLClassLoader", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "([Ljava/net/URL;Ljava/lang/ClassLoader;)V"}, {"nme": "getClassAnnotation", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "checkPermissions", "acc": 2, "dsc": "()V"}, {"nme": "getPermissions", "acc": 4, "dsc": "(Ljava/security/CodeSource;)Ljava/security/PermissionCollection;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "loadClass", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class;", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/Class<*>;", "exs": ["java/lang/ClassNotFoundException"]}], "flds": [{"acc": 2, "nme": "parent", "dsc": "<PERSON><PERSON><PERSON>/lang/ClassLoader;"}, {"acc": 2, "nme": "annotation", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "permissions", "dsc": "Ljava/security/Permissions;"}]}, "classes/sun/rmi/runtime/NewThreadAction.class": {"ver": 65, "acc": 49, "nme": "sun/rmi/runtime/NewThreadAction", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/ThreadGroup;<PERSON><PERSON><PERSON>/lang/Runnable;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;<PERSON><PERSON><PERSON>/lang/String;ZZ)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Thread;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "systemThreadGroup", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadGroup;"}, {"acc": 24, "nme": "userThreadGroup", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadGroup;"}, {"acc": 18, "nme": "group", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadGroup;"}, {"acc": 18, "nme": "runnable", "dsc": "<PERSON><PERSON><PERSON>/lang/Runnable;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "daemon", "dsc": "Z"}]}, "classes/sun/rmi/runtime/RuntimeUtil$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/RuntimeUtil$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/runtime/RuntimeUtil;)V"}, {"nme": "newThread", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)<PERSON><PERSON><PERSON>/lang/Thread;"}], "flds": [{"acc": 18, "nme": "count", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}]}, "classes/sun/rmi/transport/DGCClient.class": {"ver": 65, "acc": 48, "nme": "sun/rmi/transport/DGCClient", "super": "java/lang/Object", "mthds": [{"nme": "createSocketAcc", "acc": 10, "dsc": "()Ljava/security/AccessControlContext;"}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "registerRefs", "acc": 8, "dsc": "(Lsun/rmi/transport/Endpoint;Ljava/util/List;)V", "sig": "(Lsun/rmi/transport/Endpoint;Ljava/util/List<Lsun/rmi/transport/LiveRef;>;)V"}, {"nme": "getNextSequenceNum", "acc": 42, "dsc": "()J"}, {"nme": "computeRenewTime", "acc": 10, "dsc": "(JJ)J"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "nextSequenceNum", "dsc": "J"}, {"acc": 10, "nme": "vmid", "dsc": "Ljava/rmi/dgc/VMID;"}, {"acc": 26, "nme": "leaseValue", "dsc": "J"}, {"acc": 26, "nme": "cleanInterval", "dsc": "J"}, {"acc": 26, "nme": "gcInterval", "dsc": "J"}, {"acc": 26, "nme": "dirtyFailureRetries", "dsc": "I", "val": 5}, {"acc": 26, "nme": "cleanFailureRetries", "dsc": "I", "val": 5}, {"acc": 26, "nme": "emptyObjIDArray", "dsc": "[Ljava/rmi/server/ObjID;"}, {"acc": 26, "nme": "dgcID", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 26, "nme": "SOCKET_ACC", "dsc": "Ljava/security/AccessControlContext;"}]}, "classes/sun/rmi/registry/RegistryImpl$2.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/registry/RegistryImpl;I)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;", "exs": ["java/rmi/RemoteException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$port", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/registry/RegistryImpl;"}]}, "classes/java/rmi/server/SocketSecurityException.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/server/SocketSecurityException", "super": "java/rmi/server/ExportException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -7622072999407781979}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/server/UnicastRemoteObject.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/UnicastRemoteObject", "super": "java/rmi/server/RemoteServer", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V", "exs": ["java/rmi/RemoteException"]}, {"nme": "<init>", "acc": 4, "dsc": "(I)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "<init>", "acc": 4, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "clone", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/CloneNotSupportedException"]}, {"nme": "reexport", "acc": 2, "dsc": "()V", "exs": ["java/rmi/RemoteException"]}, {"nme": "exportObject", "acc": 131081, "dsc": "(Ljava/rmi/Remote;)Ljava/rmi/server/RemoteStub;", "exs": ["java/rmi/RemoteException"], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "exportObject", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;I)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "exportObject", "acc": 9, "dsc": "(Ljava/rmi/Remote;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "exportObject", "acc": 9, "dsc": "(Ljava/rmi/Remote;ILjava/io/ObjectInputFilter;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "exportObject", "acc": 9, "dsc": "(Ljava/rmi/Remote;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "unexportObject", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Z)Z", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "exportObject", "acc": 10, "dsc": "(Ljava/rmi/Remote;Lsun/rmi/server/UnicastServerRef;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}], "flds": [{"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 2, "nme": "csf", "dsc": "Ljava/rmi/server/RMIClientSocketFactory;"}, {"acc": 2, "nme": "ssf", "dsc": "Ljava/rmi/server/RMIServerSocketFactory;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 4974527148936298033}]}, "classes/sun/rmi/log/LogOutputStream.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/log/LogOutputStream", "super": "java/io/OutputStream", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/io/RandomAccessFile;)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 1, "dsc": "([BII)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 17, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "raf", "dsc": "Ljava/io/RandomAccessFile;"}]}, "classes/sun/rmi/transport/tcp/TCPDirectSocketFactory.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/tcp/TCPDirectSocketFactory", "super": "java/rmi/server/RMISocketFactory", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createSocket", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/net/Socket;", "exs": ["java/io/IOException"]}, {"nme": "createServerSocket", "acc": 1, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/java/rmi/server/LogStream.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/server/LogStream", "super": "java/io/PrintStream", "mthds": [{"nme": "<init>", "acc": 131074, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/io/OutputStream;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "log", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/rmi/server/LogStream;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getDefaultStream", "acc": 131113, "dsc": "()Ljava/io/PrintStream;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setDefaultStream", "acc": 131113, "dsc": "(Ljava/io/PrintStream;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getOutputStream", "acc": 131105, "dsc": "()Ljava/io/OutputStream;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "setOutputStream", "acc": 131105, "dsc": "(Ljava/io/OutputStream;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "write", "acc": 131073, "dsc": "(I)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "write", "acc": 131073, "dsc": "([BII)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toString", "acc": 131073, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "parseLevel", "acc": 131081, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "known", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/rmi/server/LogStream;>;"}, {"acc": 10, "nme": "defaultStream", "dsc": "Ljava/io/PrintStream;"}, {"acc": 2, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "logOut", "dsc": "Ljava/io/OutputStream;"}, {"acc": 2, "nme": "logWriter", "dsc": "Ljava/io/OutputStreamWriter;"}, {"acc": 2, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuffer;"}, {"acc": 2, "nme": "bufOut", "dsc": "Ljava/io/ByteArrayOutputStream;"}, {"acc": 25, "nme": "SILENT", "dsc": "I", "val": 0}, {"acc": 25, "nme": "BRIEF", "dsc": "I", "val": 10}, {"acc": 25, "nme": "VERBOSE", "dsc": "I", "val": 20}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/sun/rmi/server/DeserializationChecker.class": {"ver": 65, "acc": 1537, "nme": "sun/rmi/server/DeserializationChecker", "super": "java/lang/Object", "mthds": [{"nme": "check", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;Ljava/io/ObjectStreamClass;II)V"}, {"nme": "checkProxyClass", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/reflect/Method;[Ljava/lang/String;II)V"}, {"nme": "end", "acc": 1, "dsc": "(I)V"}], "flds": []}, "classes/sun/rmi/transport/Connection.class": {"ver": 65, "acc": 1537, "nme": "sun/rmi/transport/Connection", "super": "java/lang/Object", "mthds": [{"nme": "getInputStream", "acc": 1025, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "releaseInputStream", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getOutputStream", "acc": 1025, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "releaseOutputStream", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "isReusable", "acc": 1025, "dsc": "()Z"}, {"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getChannel", "acc": 1025, "dsc": "()Lsun/rmi/transport/Channel;"}], "flds": []}, "classes/sun/rmi/transport/tcp/TCPEndpoint.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/transport/tcp/TCPEndpoint", "super": "java/lang/Object", "mthds": [{"nme": "getInt", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "getBoolean", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getHostnameProperty", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V"}, {"nme": "getLocalEndpoint", "acc": 9, "dsc": "(I)Lsun/rmi/transport/tcp/TCPEndpoint;"}, {"nme": "getLocalEndpoint", "acc": 9, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)Lsun/rmi/transport/tcp/TCPEndpoint;"}, {"nme": "resampleLocalHost", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setLocalHost", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setDefaultPort", "acc": 8, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)V"}, {"nme": "getOutboundTransport", "acc": 1, "dsc": "()Lsun/rmi/transport/Transport;"}, {"nme": "allKnownTransports", "acc": 10, "dsc": "()Ljava/util/Collection;", "sig": "()Ljava/util/Collection<Lsun/rmi/transport/tcp/TCPTransport;>;"}, {"nme": "shedConnectionCaches", "acc": 9, "dsc": "()V"}, {"nme": "exportObject", "acc": 1, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "getChannel", "acc": 1, "dsc": "()Lsun/rmi/transport/Channel;"}, {"nme": "getHost", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getPort", "acc": 1, "dsc": "()I"}, {"nme": "getListenPort", "acc": 1, "dsc": "()I"}, {"nme": "getInboundTransport", "acc": 1, "dsc": "()Lsun/rmi/transport/Transport;"}, {"nme": "getClientSocketFactory", "acc": 1, "dsc": "()Ljava/rmi/server/RMIClientSocketFactory;"}, {"nme": "getServerSocketFactory", "acc": 1, "dsc": "()Ljava/rmi/server/RMIServerSocketFactory;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/ObjectOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "read", "acc": 9, "dsc": "(Ljava/io/ObjectInput;)Lsun/rmi/transport/tcp/TCPEndpoint;", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}, {"nme": "writeHostPortFormat", "acc": 1, "dsc": "(Ljava/io/DataOutput;)V", "exs": ["java/io/IOException"]}, {"nme": "readHostPortFormat", "acc": 9, "dsc": "(Ljava/io/DataInput;)Lsun/rmi/transport/tcp/TCPEndpoint;", "exs": ["java/io/IOException"]}, {"nme": "chooseFactory", "acc": 10, "dsc": "()Ljava/rmi/server/RMISocketFactory;"}, {"nme": "newSocket", "acc": 0, "dsc": "()Ljava/net/Socket;", "exs": ["java/rmi/RemoteException"]}, {"nme": "newServerSocket", "acc": 0, "dsc": "()Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}, {"nme": "lambda$getHostnameProperty$2", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$getBoolean$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;"}, {"nme": "lambda$getInt$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "host", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "port", "dsc": "I"}, {"acc": 18, "nme": "csf", "dsc": "Ljava/rmi/server/RMIClientSocketFactory;"}, {"acc": 18, "nme": "ssf", "dsc": "Ljava/rmi/server/RMIServerSocketFactory;"}, {"acc": 2, "nme": "listenPort", "dsc": "I"}, {"acc": 2, "nme": "transport", "dsc": "Lsun/rmi/transport/tcp/TCPTransport;"}, {"acc": 10, "nme": "localHost", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 10, "nme": "localHostKnown", "dsc": "Z"}, {"acc": 26, "nme": "localEndpoints", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Lsun/rmi/transport/tcp/TCPEndpoint;Ljava/util/LinkedList<Lsun/rmi/transport/tcp/TCPEndpoint;>;>;"}, {"acc": 26, "nme": "FORMAT_HOST_PORT", "dsc": "I", "val": 0}, {"acc": 26, "nme": "FORMAT_HOST_PORT_FACTORY", "dsc": "I", "val": 1}]}, "classes/sun/rmi/transport/Endpoint.class": {"ver": 65, "acc": 1537, "nme": "sun/rmi/transport/Endpoint", "super": "java/lang/Object", "mthds": [{"nme": "getChannel", "acc": 1025, "dsc": "()Lsun/rmi/transport/Channel;"}, {"nme": "exportObject", "acc": 1025, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "getInboundTransport", "acc": 1025, "dsc": "()Lsun/rmi/transport/Transport;"}, {"nme": "getOutboundTransport", "acc": 1025, "dsc": "()Lsun/rmi/transport/Transport;"}], "flds": []}, "classes/sun/rmi/runtime/Log$LoggerPrintStream.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/runtime/Log$LoggerPrintStream", "super": "java/io/PrintStream", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/logging/Logger;)V"}, {"nme": "write", "acc": 1, "dsc": "(I)V"}, {"nme": "write", "acc": 1, "dsc": "([BII)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "logger", "dsc": "Ljava/util/logging/Logger;"}, {"acc": 2, "nme": "last", "dsc": "I"}, {"acc": 18, "nme": "bufOut", "dsc": "Ljava/io/ByteArrayOutputStream;"}]}, "classes/sun/rmi/transport/DGCImpl$2.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCImpl$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/java/rmi/Naming.class": {"ver": 65, "acc": 49, "nme": "java/rmi/Naming", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "lookup", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/NotBoundException", "java/net/MalformedURLException", "java/rmi/RemoteException"]}, {"nme": "bind", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/AlreadyBoundException", "java/net/MalformedURLException", "java/rmi/RemoteException"]}, {"nme": "unbind", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/RemoteException", "java/rmi/NotBoundException", "java/net/MalformedURLException"]}, {"nme": "rebind", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException", "java/net/MalformedURLException"]}, {"nme": "list", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;", "exs": ["java/rmi/RemoteException", "java/net/MalformedURLException"]}, {"nme": "getRegistry", "acc": 10, "dsc": "(Ljava/rmi/Naming$ParsedNamingURL;)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "parseURL", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/rmi/Naming$ParsedNamingURL;", "exs": ["java/net/MalformedURLException"]}, {"nme": "intParseURL", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/rmi/Naming$ParsedNamingURL;", "exs": ["java/net/MalformedURLException", "java/net/URISyntaxException"]}], "flds": []}, "classes/sun/rmi/server/MarshalOutputStream$1.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/server/MarshalOutputStream$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/rmi/server/MarshalOutputStream;)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/server/MarshalOutputStream;"}]}, "classes/sun/rmi/transport/Transport.class": {"ver": 65, "acc": 1057, "nme": "sun/rmi/transport/Transport", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getLogLevel", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getChannel", "acc": 1025, "dsc": "(Lsun/rmi/transport/Endpoint;)Lsun/rmi/transport/Channel;"}, {"nme": "free", "acc": 1025, "dsc": "(Lsun/rmi/transport/Endpoint;)V"}, {"nme": "exportObject", "acc": 1, "dsc": "(Lsun/rmi/transport/Target;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "targetUnexported", "acc": 4, "dsc": "()V"}, {"nme": "currentTransport", "acc": 8, "dsc": "()Lsun/rmi/transport/Transport;"}, {"nme": "checkAcceptPermission", "acc": 1028, "dsc": "(Ljava/security/AccessControlContext;)V"}, {"nme": "setContextClassLoader", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)V"}, {"nme": "serviceCall", "acc": 1, "dsc": "(Ljava/rmi/server/RemoteCall;)Z"}, {"nme": "lambda$setContextClassLoader$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/ClassLoader;)<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "lambda$getLogLevel$0", "acc": 4106, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "logLevel", "dsc": "I"}, {"acc": 24, "nme": "transportLog", "dsc": "Lsun/rmi/runtime/Log;"}, {"acc": 26, "nme": "currentTransport", "dsc": "<PERSON><PERSON><PERSON>/lang/ThreadLocal;", "sig": "Ljava/lang/ThreadLocal<Lsun/rmi/transport/Transport;>;"}, {"acc": 26, "nme": "dgcID", "dsc": "Ljava/rmi/server/ObjID;"}, {"acc": 26, "nme": "SETCCL_ACC", "dsc": "Ljava/security/AccessControlContext;"}]}, "classes/java/rmi/server/RemoteObject.class": {"ver": 65, "acc": 1057, "nme": "java/rmi/server/RemoteObject", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(Ljava/rmi/server/RemoteRef;)V"}, {"nme": "getRef", "acc": 1, "dsc": "()Ljava/rmi/server/RemoteRef;"}, {"nme": "toStub", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/NoSuchObjectException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "writeObject", "acc": 2, "dsc": "(Ljava/io/ObjectOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 132, "nme": "ref", "dsc": "Ljava/rmi/server/RemoteRef;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -3215090123894869218}]}, "classes/java/rmi/UnexpectedException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/UnexpectedException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1800467484195073863}]}, "classes/java/rmi/UnknownHostException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/UnknownHostException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -8152710247442114228}]}, "classes/java/rmi/registry/LocateRegistry.class": {"ver": 65, "acc": 49, "nme": "java/rmi/registry/LocateRegistry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getRegistry", "acc": 9, "dsc": "()Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getRegistry", "acc": 9, "dsc": "(I)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getRegistry", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getRegistry", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "getRegistry", "acc": 9, "dsc": "(Ljava/lang/String;ILjava/rmi/server/RMIClientSocketFactory;)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "createRegistry", "acc": 9, "dsc": "(I)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}, {"nme": "createRegistry", "acc": 9, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;)Ljava/rmi/registry/Registry;", "exs": ["java/rmi/RemoteException"]}], "flds": []}, "classes/java/rmi/MarshalledObject$MarshalledObjectOutputStream.class": {"ver": 65, "acc": 32, "nme": "java/rmi/MarshalledObject$MarshalledObjectOutputStream", "super": "sun/rmi/server/MarshalOutputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/io/OutputStream;Ljava/io/OutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "hadAnnotations", "acc": 0, "dsc": "()Z"}, {"nme": "writeLocation", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 2, "nme": "locOut", "dsc": "Ljava/io/ObjectOutputStream;"}, {"acc": 2, "nme": "hadAnnotations", "dsc": "Z"}]}, "classes/sun/rmi/registry/RegistryImpl$3.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/registry/RegistryImpl$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()Ljava/net/InetAddress;", "exs": ["java/net/UnknownHostException"]}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 4112, "nme": "val$clientHostName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/java/rmi/server/Operation.class": {"ver": 65, "acc": 131105, "nme": "java/rmi/server/Operation", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "getOperation", "acc": 131073, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, {"nme": "toString", "acc": 131073, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}], "flds": [{"acc": 2, "nme": "operation", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}], "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;"}]}, "classes/java/rmi/ServerException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/ServerException", "super": "java/rmi/RemoteException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -4775845313121906682}]}, "classes/sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread.class": {"ver": 65, "acc": 32, "nme": "sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lsun/rmi/transport/DGCClient$EndpointEntry;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "lambda$run$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/util/Set;J)<PERSON><PERSON><PERSON>/lang/Void;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Lsun/rmi/transport/DGCClient$EndpointEntry;"}]}, "classes/sun/rmi/log/ReliableLog$LogFile.class": {"ver": 65, "acc": 33, "nme": "sun/rmi/log/ReliableLog$LogFile", "super": "java/io/RandomAccessFile", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/FileNotFoundException", "java/io/IOException"]}, {"nme": "sync", "acc": 4, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "checkSpansBoundary", "acc": 4, "dsc": "(J)Z"}], "flds": [{"acc": 18, "nme": "fd", "dsc": "Ljava/io/FileDescriptor;"}]}, "classes/java/rmi/server/ServerCloneException.class": {"ver": 65, "acc": 33, "nme": "java/rmi/server/ServerCloneException", "super": "java/lang/CloneNotSupportedException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCause", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Throwable;"}], "flds": [{"acc": 1, "nme": "detail", "dsc": "<PERSON><PERSON><PERSON>/lang/Exception;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 6617456357664815945}]}}}}