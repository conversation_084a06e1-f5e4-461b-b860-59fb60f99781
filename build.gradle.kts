import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("org.jetbrains.kotlin.jvm") version "2.0.20"
    id("architectury-plugin") version "3.4-SNAPSHOT"
    id("dev.architectury.loom") version "1.7-SNAPSHOT"
}

group = "cn.acebrand"
version = "1.0.0"


allprojects{
    apply(plugin = "org.jetbrains.kotlin.jvm")
    apply(plugin = "dev.architectury.loom")
    apply(plugin = "dev.architectury.loom")



    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://server.pokemtd.top:31647/snapshots")
        }
        maven {
            url = uri("https://repo.extendedclip.com/releases/")
        }
        maven {
            name = "spigotmc-repo"
            url = uri("https://hub.spigotmc.org/nexus/content/repositories/snapshots/")
        }
        maven {
            url = uri("https://maven.impactdev.net/repository/development/")
            url = uri("https://artefacts.cobblemon.com/releases")
        }

    }

    dependencies {
        minecraft("com.mojang:minecraft:1.21.1")
        mappings(loom.officialMojangMappings())
        modCompileOnly("com.cobblemon:mod:1.6.1+1.21.1")
        compileOnly("org.spigotmc:spigot-api:1.21.1-R0.1-SNAPSHOT")
        compileOnly("org.jetbrains.kotlin:kotlin-reflect")
        implementation(fileTree("libs") {
            include("*.jar")
        })
        implementation(fileTree("libs") {
            include("*.jar")
        })



        testImplementation(kotlin("test"))
    }

    tasks.test {
        useJUnitPlatform()
    }

    tasks.withType<JavaCompile>{
        options.encoding = "UTF-8"
    }



}



