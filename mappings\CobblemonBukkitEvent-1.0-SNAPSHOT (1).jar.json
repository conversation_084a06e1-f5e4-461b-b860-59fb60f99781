{"md5": "e27393a1ec6cd0be2bea914ff1cae7e4", "sha2": "1d8f69264d039a252e218ec0e4051b7d385281d1", "sha256": "a2afe3d77b8a0a471c00cceea92f05826593b606bfef7f2130a0ab3ffba59006", "contents": {"classes": {"org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent.class": {"ver": 61, "acc": 49, "nme": "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent", "super": "org/bukkit/event/Event", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "getCobblemonEvent", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/Nullable;"}]}, {"nme": "getHandlers", "acc": 1, "dsc": "()Lorg/bukkit/event/HandlerList;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "getHandlerList", "acc": 25, "dsc": "()Lorg/bukkit/event/HandlerList;", "vanns": [{"dsc": "L<PERSON>lin/jvm/JvmStatic;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "access$getHandlers$cp", "acc": 4121, "dsc": "()Lorg/bukkit/event/HandlerList;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "Companion", "dsc": "Lorg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent$Companion;"}, {"acc": 18, "nme": "cobblemonEvent", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 26, "nme": "handlers", "dsc": "Lorg/bukkit/event/HandlerList;"}], "vanns": [{"dsc": "Lkotlin/Metadata;", "vals": ["mv", [2, 0, 0], "k", 1, "xi", 48, "d1", ["\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u0000 \f2\u00020\u0001:\u0001\fB\u0011\u0012\b\u0010\u0003\u001a\u0004\u0018\u00010\u0002¢\u0006\u0004\b\u0004\u0010\u0005J\u000f\u0010\u0007\u001a\u00020\u0006H\u0016¢\u0006\u0004\b\u0007\u0010\bR\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b\u0003\u0010\t\u001a\u0004\b\n\u0010\u000b¨\u0006\r"], "d2", ["Lorg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent;", "Lorg/bukkit/event/Event;", "", "cobblemonEvent", "<init>", "(<PERSON><PERSON><PERSON>/lang/Object;)V", "Lorg/bukkit/event/HandlerList;", "getHandlers", "()Lorg/bukkit/event/HandlerList;", "<PERSON><PERSON><PERSON>/lang/Object;", "getCobblemonEvent", "()<PERSON><PERSON><PERSON>/lang/Object;", "Companion", "CobblemonBukkitEvent"]]}]}, "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main$onEnable$1.class": {"ver": 61, "acc": 48, "nme": "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main$onEnable$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "invoke", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "invoke", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "INSTANCE", "dsc": "Lorg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main$onEnable$1;"}], "vanns": [{"dsc": "Lkotlin/Metadata;", "vals": ["mv", [2, 0, 0], "k", 3, "xi", 48]}]}, "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent$Companion.class": {"ver": 61, "acc": 49, "nme": "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent$Companion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getHandlers$annotations", "acc": 135178, "dsc": "()V", "vanns": [{"dsc": "L<PERSON>lin/jvm/JvmStatic;"}]}, {"nme": "getHandlerList", "acc": 17, "dsc": "()Lorg/bukkit/event/HandlerList;", "vanns": [{"dsc": "L<PERSON>lin/jvm/JvmStatic;"}], "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "<init>", "acc": 4097, "dsc": "(Lkotlin/jvm/internal/DefaultConstructorMarker;)V"}], "flds": [], "vanns": [{"dsc": "Lkotlin/Metadata;", "vals": ["mv", [2, 0, 0], "k", 1, "xi", 48, "d1", ["\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0007¢\u0006\u0004\b\u0005\u0010\u0006R\u001a\u0010\u0007\u001a\u00020\u00048\u0002X\u0004¢\u0006\f\n\u0004\b\u0007\u0010\b\u0012\u0004\b\t\u0010\u0003¨\u0006\n"], "d2", ["Lorg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/CobblemonEvent$Companion;", "", "<init>", "()V", "Lorg/bukkit/event/HandlerList;", "getHandlerList", "()Lorg/bukkit/event/HandlerList;", "handlers", "Lorg/bukkit/event/HandlerList;", "getHandlers$annotations", "CobblemonBukkitEvent"]]}]}, "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main.class": {"ver": 61, "acc": 49, "nme": "org/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main", "super": "org/bukkit/plugin/java/JavaPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getSubscribers", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lcom/cobblemon/mod/common/api/reactive/ObservableSubscription<*>;>;", "invanns": [{"dsc": "Lorg/jetbrains/annotations/NotNull;"}]}, {"nme": "onEnable", "acc": 1, "dsc": "()V"}, {"nme": "onDisable", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "subscribers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Lcom/cobblemon/mod/common/api/reactive/ObservableSubscription<*>;>;"}], "vanns": [{"dsc": "Lkotlin/Metadata;", "vals": ["mv", [2, 0, 0], "k", 1, "xi", 48, "d1", ["\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u00012\u00020\u0002B\u0007¢\u0006\u0004\b\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0016¢\u0006\u0004\b\u0006\u0010\u0004J\u000f\u0010\u0007\u001a\u00020\u0005H\u0016¢\u0006\u0004\b\u0007\u0010\u0004R!\u0010\n\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\t0\b8\u0006¢\u0006\f\n\u0004\b\n\u0010\u000b\u001a\u0004\b\f\u0010\r¨\u0006\u000e"], "d2", ["Lorg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main;", "Lorg/bukkit/plugin/java/JavaPlugin;", "Lorg/bukkit/event/Listener;", "<init>", "()V", "", "onEnable", "onDisable", "", "Lcom/cobblemon/mod/common/api/reactive/ObservableSubscription;", "subscribers", "<PERSON><PERSON><PERSON>/util/List;", "getSubscribers", "()<PERSON><PERSON><PERSON>/util/List;", "CobblemonBukkitEvent"]]}], "invanns": [{"dsc": "Lkotlin/jvm/internal/SourceDebugExtension;", "vals": ["value", ["SMAP\nMain.kt\nKotlin\n*S Kotlin\n*F\n+ 1 Main.kt\norg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,28:1\n1863#2,2:29\n*S KotlinDebug\n*F\n+ 1 Main.kt\norg/figsq/cobblemonbukkitevent/cobblemonbukkitevent/Main\n*L\n26#1:29,2\n*E\n"]]}]}}}}