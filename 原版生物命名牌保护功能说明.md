# 原版生物命名牌保护功能说明

## 功能概述

精灵清理插件现在支持原版生物命名牌保护功能。任何使用命名牌命名的原版生物都可以选择性地不被清理系统清理，通过配置开关控制此功能的启用状态。

## 功能特点

### 1. 可配置的命名牌保护
- 通过配置文件控制是否启用原版生物命名牌保护
- 配置项：`protect-named-vanilla-mobs`
- 默认值：`true`（启用保护）

### 2. 自动检测命名牌
- 插件会自动检测原版生物实体是否有自定义名称（命名牌）
- 检测基于 Bukkit API 的 `entity.customName` 属性
- 支持任何非空的自定义名称，包括空字符串

### 3. 优先级保护
- 命名牌检查具有高优先级
- 即使原版生物满足其他所有清理条件，有命名牌的生物也不会被清理（当功能启用时）
- 检查顺序：死亡检查 → 排除列表检查 → 命名牌检查 → 其他条件检查

### 4. 详细日志记录
- 当启用日志记录时，会记录命名牌保护的详细信息
- 日志会显示："原版生物有命名牌，不清理: [生物信息]"
- 支持空命名牌的特殊显示格式

## 配置说明

### config.yml 新增配置项

```yaml
# 是否保护被命名牌命名的原版生物
# 启用后，任何使用命名牌命名的原版生物都不会被清理
# 这包括被驯服的动物、特殊命名的生物等
protect-named-vanilla-mobs: true
```

### 配置选项说明
- `true`：启用命名牌保护，有命名牌的原版生物不会被清理
- `false`：禁用命名牌保护，有命名牌的原版生物也可能被清理

## 实现细节

### 代码修改位置

1. **ConfigManager.kt**
   - 添加配置变量：`var protectNamedVanillaMobs: Boolean = true`
   - 配置加载：`protectNamedVanillaMobs = config.getBoolean("protect-named-vanilla-mobs", true)`
   - 默认配置：`protectNamedVanillaMobs = true`

2. **PokemonCleaner.kt - shouldCleanVanillaMobEntity 方法**
   - 在排除列表检查后添加命名牌检查
   - 位置：第1551-1573行

```kotlin
// 检查是否有自定义名称（命名牌保护）
if (configManager.protectNamedVanillaMobs) {
    try {
        if (entity.customName != null) {
            if (configManager.enableLogging) {
                val customName = entity.customName
                val displayName = if (customName.isNullOrEmpty()) {
                    "${entity.type.name}（空命名牌）"
                } else {
                    "$customName (${entity.type.name})"
                }
                plugin.logger.info("原版生物有命名牌，不清理: $displayName")
            }
            return false
        }
    } catch (e: Exception) {
        // 如果无法检查，保守处理
        if (configManager.enableLogging) {
            plugin.logger.warning("检查原版生物命名牌时发生错误: ${e.message}")
        }
        return false
    }
}
```

3. **config.yml**
   - 添加新的配置项和说明

### 检测逻辑

```kotlin
// 检测实体是否有命名牌
val hasNameTag = entity.customName != null

// 命名牌类型：
// - null: 没有命名牌
// - "": 空字符串命名牌（仍被视为有命名牌）
// - "任何文本": 有效的命名牌
```

## 使用示例

### 场景1：保护被驯服的动物
```
玩家使用命名牌将牛命名为"我的奶牛"
→ 清理系统检测到命名牌（如果功能启用）
→ 该牛不会被清理
→ 日志显示："原版生物有命名牌，不清理: 我的奶牛 (COW)"
```

### 场景2：普通原版生物清理
```
野生僵尸没有命名牌
→ 清理系统跳过命名牌检查
→ 继续检查其他清理条件
→ 如果满足清理条件，僵尸会被清理
```

### 场景3：空命名牌保护
```
玩家使用命名牌但没有输入名称（空字符串）
→ 清理系统仍然检测到命名牌存在（如果功能启用）
→ 该生物不会被清理
→ 日志显示："原版生物有命名牌，不清理: COW（空命名牌）"
```

### 场景4：禁用保护功能
```
配置 protect-named-vanilla-mobs: false
→ 即使生物有命名牌也不会受到保护
→ 按照其他清理条件正常处理
```

## 兼容性

- 兼容所有现有的原版生物清理配置
- 不影响精灵和掉落物的清理逻辑
- 与排除列表、年龄限制等功能完全兼容
- 命名牌保护优先级高于年龄和距离检查，但低于排除列表

## 管理员使用指南

### 启用功能
1. 编辑 `config.yml`
2. 设置 `protect-named-vanilla-mobs: true`
3. 重载插件配置

### 禁用功能
1. 编辑 `config.yml`
2. 设置 `protect-named-vanilla-mobs: false`
3. 重载插件配置

### 监控功能
1. 启用日志记录：`enable-logging: true`
2. 查看控制台日志中的保护记录
3. 使用调试模式获取更详细信息

## 玩家使用指南

### 保护原版生物
1. 制作命名牌：`/give @s name_tag`
2. 在铁砧上重命名（可选）
3. 右键点击要保护的原版生物使用命名牌
4. 该生物将自动受到保护（如果功能启用）

### 适用场景
- 保护被驯服的宠物（猫、狼、马等）
- 保护特殊用途的生物（村民、铁傀儡等）
- 保护装饰性生物
- 保护农场动物

## 注意事项

1. **功能开关**：此功能可以通过配置完全禁用
2. **性能影响**：命名牌检查的性能开销极小
3. **优先级**：排除列表的优先级仍然高于命名牌保护
4. **错误处理**：如果检查命名牌时发生错误，系统会保守地不清理该生物
5. **日志记录**：建议启用日志记录以监控保护功能的工作状态

## 故障排除

### 常见问题
1. **命名牌不生效**：检查配置是否启用 `protect-named-vanilla-mobs: true`
2. **日志不显示**：检查日志配置 `enable-logging: true`
3. **生物仍被清理**：检查是否在排除列表中，排除列表优先级更高

### 调试方法
1. 启用详细日志记录
2. 检查配置文件语法
3. 查看控制台错误信息
4. 使用测试环境验证功能
