{"md5": "ff206a0a8f9e1a5ce2253e89f87bf944", "sha2": "9b36a804947a57305ba3ac19d89f217bac55136e", "sha256": "d24b1dd2a7d49864085354effd65a079abf80ccbd84117d6e3f818970bb7843b", "contents": {"classes": {"classes/jdk/management/jfr/FlightRecorderMXBeanImpl.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl", "super": "javax/management/StandardEmitterMBean", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "startRecording", "acc": 1, "dsc": "(J)V"}, {"nme": "stopRecording", "acc": 1, "dsc": "(J)Z"}, {"nme": "closeRecording", "acc": 1, "dsc": "(J)V"}, {"nme": "openStream", "acc": 1, "dsc": "(JLjava/util/Map;)J", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)J", "exs": ["java/io/IOException"]}, {"nme": "closeStream", "acc": 1, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "readStream", "acc": 1, "dsc": "(J)[B", "exs": ["java/io/IOException"]}, {"nme": "getRecordings", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/RecordingInfo;>;"}, {"nme": "getConfigurations", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/ConfigurationInfo;>;"}, {"nme": "getEventTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/EventTypeInfo;>;"}, {"nme": "getRecordingSettings", "acc": 1, "dsc": "(J)Ljava/util/Map;", "sig": "(J)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setRecordingSettings", "acc": 1, "dsc": "(JLjava/util/Map;)V", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "newRecording", "acc": 1, "dsc": "()J"}, {"nme": "takeSnapshot", "acc": 1, "dsc": "()J"}, {"nme": "setConfiguration", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setPredefinedConfiguration", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "copyTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "setRecordingOptions", "acc": 1, "dsc": "(JLjava/util/Map;)V", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getRecordingOptions", "acc": 1, "dsc": "(J)Ljava/util/Map;", "sig": "(J)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "cloneRecording", "acc": 1, "dsc": "(JZ)J", "exs": ["java/lang/IllegalStateException", "java/lang/SecurityException"]}, {"nme": "getObjectName", "acc": 1, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "getExistingRecording", "acc": 2, "dsc": "(J)Ljdk/jfr/Recording;"}, {"nme": "getRecording", "acc": 2, "dsc": "(J)Ljdk/jfr/Recording;"}, {"nme": "setOption", "acc": 10, "dsc": "(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;Ljava/util/function/Function;Ljava/util/function/Consumer;)V", "sig": "<T:Ljava/lang/Object;U:Ljava/lang/Object;>(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/String;Ljava/lang/String;Ljava/util/function/Function<Ljava/lang/String;TU;>;Ljava/util/function/Consumer<TU;>;)V"}, {"nme": "setOptionDestination", "acc": 10, "dsc": "(Ljdk/jfr/Recording;L<PERSON><PERSON>/lang/String;)V"}, {"nme": "validateOption", "acc": 10, "dsc": "(Lja<PERSON>/util/Map;Ljava/lang/String;Ljava/util/function/Function;)V", "sig": "<T:Ljava/lang/Object;U:Ljava/lang/Object;>(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/String;Ljava/util/function/Function<Ljava/lang/String;TU;>;)V"}, {"nme": "getRecorder", "acc": 2, "dsc": "()Ljdk/jfr/FlightRecorder;", "exs": ["java/lang/SecurityException"]}, {"nme": "createNotificationInfo", "acc": 10, "dsc": "()[Ljavax/management/MBeanNotificationInfo;"}, {"nme": "addNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V"}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "removeNotificationListener", "acc": 1, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V", "exs": ["javax/management/ListenerNotFoundException"]}, {"nme": "removeListeners", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)V", "sig": "(Ljava/util/function/Predicate<Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;>;)V"}, {"nme": "createNotification", "acc": 2, "dsc": "(Ljdk/jfr/Recording;)Ljavax/management/Notification;"}, {"nme": "lambda$removeNotificationListener$5", "acc": 4106, "dsc": "(Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;)Z"}, {"nme": "lambda$removeNotificationListener$4", "acc": 4106, "dsc": "(Ljavax/management/NotificationListener;Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;)Z"}, {"nme": "lambda$getRecording$3", "acc": 4106, "dsc": "(JLjdk/jfr/Recording;)Z"}, {"nme": "lambda$setRecordingOptions$2", "acc": 4106, "dsc": "(Ljdk/jfr/Recording;L<PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$setRecordingOptions$1", "acc": 4106, "dsc": "(Ljdk/jfr/Recording;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$setRecordingOptions$0", "acc": 4106, "dsc": "(Ljdk/jfr/Recording;Ljava/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ATTRIBUTE_RECORDINGS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "Recordings"}, {"acc": 26, "nme": "OPTION_MAX_SIZE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maxSize"}, {"acc": 26, "nme": "OPTION_MAX_AGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "maxAge"}, {"acc": 26, "nme": "OPTION_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "name"}, {"acc": 26, "nme": "OPTION_DISK", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "disk"}, {"acc": 26, "nme": "OPTION_DUMP_ON_EXIT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "dumpOnExit"}, {"acc": 26, "nme": "OPTION_DURATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "duration"}, {"acc": 26, "nme": "OPTION_DESTINATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "destination"}, {"acc": 26, "nme": "OPTIONS", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "stream<PERSON><PERSON><PERSON>", "dsc": "Ljdk/jfr/internal/management/StreamManager;"}, {"acc": 18, "nme": "changes", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Long;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "sequenceNumber", "dsc": "Ljava/util/concurrent/atomic/AtomicLong;"}, {"acc": 18, "nme": "listeners", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;>;"}, {"acc": 2, "nme": "recorder", "dsc": "Ljdk/jfr/FlightRecorder;"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$3.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$3", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl;)V"}, {"nme": "run", "acc": 1, "dsc": "()Ljdk/jfr/FlightRecorder;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/management/jfr/RemoteRecordingStream$ChunkConsumer.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/RemoteRecordingStream$ChunkConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/DiskRepository;)V"}, {"nme": "accept", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Long;)V"}, {"nme": "accept", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}], "flds": [{"acc": 18, "nme": "repository", "dsc": "Ljdk/management/jfr/DiskRepository;"}]}, "classes/jdk/management/jfr/internal/FlightRecorderMXBeanProvider.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/internal/FlightRecorderMXBeanProvider", "super": "sun/management/spi/PlatformMBeanProvider", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getFlightRecorderMXBean", "acc": 10, "dsc": "()Ljdk/management/jfr/FlightRecorderMXBean;"}, {"nme": "setFlightRecorderMXBeanFactory", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/concurrent/Callable;)V", "sig": "(Ljava/util/concurrent/Callable<Ljdk/management/jfr/FlightRecorderMXBean;>;)V"}, {"nme": "getPlatformComponentList", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Lsun/management/spi/PlatformMBeanProvider$PlatformComponent<*>;>;"}], "flds": [{"acc": 10, "nme": "flightRecorderMXBeanFactory", "dsc": "Ljava/util/concurrent/Callable;", "sig": "Ljava/util/concurrent/Callable<Ljdk/management/jfr/FlightRecorderMXBean;>;"}, {"acc": 74, "nme": "flightRecorderMXBean", "dsc": "Ljdk/management/jfr/FlightRecorderMXBean;"}]}, "classes/jdk/management/jfr/EventTypeInfo.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/EventTypeInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jfr/EventType;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "createCategoryNames", "acc": 10, "dsc": "([<PERSON><PERSON><PERSON>/lang/Object;)Lja<PERSON>/util/List;", "sig": "([<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "creatingSettingDescriptorInfos", "acc": 10, "dsc": "(Ljdk/jfr/EventType;)Ljava/util/List;", "sig": "(Ljdk/jfr/EventType;)Ljava/util/List<Ljdk/management/jfr/SettingDescriptorInfo;>;"}, {"nme": "createSettingDescriptors", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>java/util/List;", "sig": "(Ljava/lang/Object;)Ljava/util/List<Ljdk/management/jfr/SettingDescriptorInfo;>;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getCategoryNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getId", "acc": 1, "dsc": "()J"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSettingDescriptors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/SettingDescriptorInfo;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljdk/management/jfr/EventTypeInfo;"}], "flds": [{"acc": 18, "nme": "settingDescriptors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/management/jfr/SettingDescriptorInfo;>;"}, {"acc": 18, "nme": "id", "dsc": "J"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "categoryNames", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$1.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl;)V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/jfr/EventType;>;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/management/jfr/Stringifier.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/Stringifier", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "sb", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}, {"acc": 2, "nme": "first", "dsc": "Z"}]}, "classes/jdk/management/jfr/DiskRepository$DiskChunk.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/DiskRepository$DiskChunk", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/DiskRepository;Ljava/nio/file/Path;J)V"}, {"nme": "acquire", "acc": 1, "dsc": "()V"}, {"nme": "release", "acc": 1, "dsc": "()V"}, {"nme": "destroy", "acc": 2, "dsc": "()V"}, {"nme": "isDead", "acc": 1, "dsc": "()Z"}, {"nme": "path", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}], "flds": [{"acc": 16, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 16, "nme": "startTimeNanos", "dsc": "J"}, {"acc": 16, "nme": "repository", "dsc": "Ljdk/management/jfr/DiskRepository;"}, {"acc": 0, "nme": "referenceCount", "dsc": "I"}, {"acc": 0, "nme": "endTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 0, "nme": "size", "dsc": "J"}, {"acc": 0, "nme": "endTimeNanos", "dsc": "J"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl;Ljavax/management/NotificationListener;Ljavax/management/NotificationFilter;Ljava/lang/Object;)V"}, {"nme": "recordingStateChanged", "acc": 1, "dsc": "(Ljdk/jfr/Recording;)V"}], "flds": [{"acc": 18, "nme": "listener", "dsc": "Ljavax/management/NotificationListener;"}, {"acc": 18, "nme": "filter", "dsc": "Ljavax/management/NotificationFilter;"}, {"acc": 18, "nme": "handback", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 18, "nme": "context", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/management/jfr/FlightRecorderMXBeanImpl;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/Class;)V", "sig": "(Ljava/lang/String;Ljava/lang/Class<Ljdk/management/jfr/FlightRecorderMXBean;>;)V"}, {"nme": "mbeanInterfaceNames", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "nameToMBeanMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljdk/management/jfr/FlightRecorderMXBean;>;"}, {"nme": "getObjectNamePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "mbeanInterfaces", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/Class<+Ljdk/management/jfr/FlightRecorderMXBean;>;>;"}], "flds": [{"acc": 18, "nme": "objectName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "mbeanInterface", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<Ljdk/management/jfr/FlightRecorderMXBean;>;"}]}, "classes/jdk/management/jfr/RemoteRecordingStream.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/RemoteRecordingStream", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/MBeanServerConnection;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/MBeanServerConnection;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/MBeanServerConnection;Ljava/nio/file/Path;Z)V", "exs": ["java/io/IOException"]}, {"nme": "configurations", "acc": 2, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBean;)Ljava/util/List;", "sig": "(Ljdk/management/jfr/FlightRecorderMXBean;)Ljava/util/List<Ljdk/jfr/Configuration;>;"}, {"nme": "onMetadata", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Ljdk/jfr/consumer/MetadataEvent;>;)V"}, {"nme": "checkFileAccess", "acc": 10, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "closeSilently", "acc": 10, "dsc": "(Ljava/io/RandomAccessFile;)V"}, {"nme": "createProxy", "acc": 10, "dsc": "(Ljavax/management/MBeanServerConnection;)Ljdk/management/jfr/FlightRecorderMXBean;", "exs": ["java/io/IOException"]}, {"nme": "createRecording", "acc": 2, "dsc": "()J", "exs": ["java/io/IOException"]}, {"nme": "setSettings", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "disable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jfr/EventSettings;"}, {"nme": "enable", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/jfr/EventSettings;"}, {"nme": "setMaxAge", "acc": 1, "dsc": "(<PERSON><PERSON>va/time/Duration;)V"}, {"nme": "setMaxSize", "acc": 1, "dsc": "(J)V"}, {"nme": "onEvent", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Ljdk/jfr/consumer/RecordedEvent;>;)V"}, {"nme": "onEvent", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/function/Consumer;)V", "sig": "(Ljava/lang/String;Ljava/util/function/Consumer<Ljdk/jfr/consumer/RecordedEvent;>;)V"}, {"nme": "onFlush", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "onError", "acc": 1, "dsc": "(Ljava/util/function/Consumer;)V", "sig": "(Ljava/util/function/Consumer<Ljava/lang/Throwable;>;)V"}, {"nme": "onClose", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Runnable;)V"}, {"nme": "close", "acc": 1, "dsc": "()V"}, {"nme": "remove", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "setReuse", "acc": 1, "dsc": "(Z)V"}, {"nme": "setOrdered", "acc": 1, "dsc": "(Z)V"}, {"nme": "setStartTime", "acc": 1, "dsc": "(<PERSON>java/time/Instant;)V"}, {"nme": "setEndTime", "acc": 1, "dsc": "(<PERSON>java/time/Instant;)V"}, {"nme": "start", "acc": 1, "dsc": "()V"}, {"nme": "startAsync", "acc": 1, "dsc": "()V"}, {"nme": "stop", "acc": 1, "dsc": "()Z"}, {"nme": "ensureStartable", "acc": 2, "dsc": "()V"}, {"nme": "dump", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "getRecordingInfo", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;J)Ljdk/management/jfr/RecordingInfo;", "sig": "(Ljava/util/List<Ljdk/management/jfr/RecordingInfo;>;J)Ljdk/management/jfr/RecordingInfo;", "exs": ["java/io/IOException"]}, {"nme": "awaitTermination", "acc": 1, "dsc": "(<PERSON><PERSON>va/time/Duration;)V", "exs": ["java/lang/InterruptedException"]}, {"nme": "awaitTermination", "acc": 1, "dsc": "()V", "exs": ["java/lang/InterruptedException"]}, {"nme": "makeTempDirectory", "acc": 10, "dsc": "()Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "updateOnCompleteHandler", "acc": 2, "dsc": "()V"}, {"nme": "startDownload", "acc": 2, "dsc": "()V"}, {"nme": "isClosed", "acc": 0, "dsc": "()Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "ENABLED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "enabled"}, {"acc": 26, "nme": "OBJECT_NAME", "dsc": "Ljavax/management/ObjectName;"}, {"acc": 16, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 16, "nme": "mbean", "dsc": "Ljdk/management/jfr/FlightRecorderMXBean;"}, {"acc": 16, "nme": "recordingId", "dsc": "J"}, {"acc": 16, "nme": "stream", "dsc": "Ljdk/jfr/consumer/EventStream;"}, {"acc": 16, "nme": "accessControllerContext", "dsc": "Ljava/security/AccessControlContext;"}, {"acc": 16, "nme": "repository", "dsc": "Ljdk/management/jfr/DiskRepository;"}, {"acc": 16, "nme": "creationTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 16, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 64, "nme": "startTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 64, "nme": "endTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 64, "nme": "closed", "dsc": "Z"}, {"acc": 2, "nme": "started", "dsc": "Z"}, {"acc": 2, "nme": "maxAge", "dsc": "<PERSON><PERSON><PERSON>/time/Duration;"}, {"acc": 2, "nme": "maxSize", "dsc": "J"}]}, "classes/jdk/management/jfr/ConfigurationInfo.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/ConfigurationInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jfr/Configuration;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "createMap", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getContents", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSettings", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljdk/management/jfr/ConfigurationInfo;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "settings", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "provider", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "contents", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/management/jfr/RemoteRecordingStream$RemoteSettings.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/RemoteRecordingStream$RemoteSettings", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBean;J)V"}, {"nme": "with", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "toMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getEventSettings", "acc": 2, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}], "flds": [{"acc": 18, "nme": "mbean", "dsc": "Ljdk/management/jfr/FlightRecorderMXBean;"}, {"acc": 18, "nme": "recordingId", "dsc": "J"}]}, "classes/jdk/management/jfr/DownLoadThread.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/DownLoadThread", "super": "java/lang/Thread", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/RemoteRecordingStream;Ljava/lang/String;)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "takeNap", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "stream", "dsc": "Ljdk/management/jfr/RemoteRecordingStream;"}, {"acc": 18, "nme": "startTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 18, "nme": "endTime", "dsc": "<PERSON><PERSON>va/time/Instant;"}, {"acc": 18, "nme": "diskRepository", "dsc": "Ljdk/management/jfr/DiskRepository;"}]}, "classes/jdk/management/jfr/MBeanUtils.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/MBeanUtils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "createObjectName", "acc": 8, "dsc": "()Ljavax/management/ObjectName;"}, {"nme": "checkControl", "acc": 8, "dsc": "()V"}, {"nme": "checkMonitor", "acc": 8, "dsc": "()V"}, {"nme": "transformList", "acc": 8, "dsc": "(Lja<PERSON>/util/List;Ljava/util/function/Function;)Ljava/util/List;", "sig": "<T:Ljava/lang/Object;R:Ljava/lang/Object;>(Ljava/util/List<TT;>;Ljava/util/function/Function<TT;TR;>;)Ljava/util/List<TR;>;"}, {"nme": "booleanValue", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "duration", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/time/Duration;", "exs": ["java/lang/NumberFormatException"]}, {"nme": "parseTimestamp", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/time/Instant;)Lja<PERSON>/time/Instant;"}, {"nme": "size", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Long;", "exs": ["java/lang/NumberFormatException"]}, {"nme": "parseBlockSize", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"nme": "destination", "acc": 9, "dsc": "(Ljdk/jfr/Recording;Ljava/lang/String;)Ljava/lang/String;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "monitor", "dsc": "Ljava/security/Permission;"}, {"acc": 26, "nme": "control", "dsc": "Ljava/security/Permission;"}]}, "classes/jdk/management/jfr/RecordingInfo.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/RecordingInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jfr/Recording;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getId", "acc": 1, "dsc": "()J"}, {"nme": "getDumpOnExit", "acc": 1, "dsc": "()Z"}, {"nme": "getMaxAge", "acc": 1, "dsc": "()J"}, {"nme": "getMaxSize", "acc": 1, "dsc": "()J"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStartTime", "acc": 1, "dsc": "()J"}, {"nme": "getStopTime", "acc": 1, "dsc": "()J"}, {"nme": "getSettings", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getDestination", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getSize", "acc": 1, "dsc": "()J"}, {"nme": "isToDisk", "acc": 1, "dsc": "()Z"}, {"nme": "getDuration", "acc": 1, "dsc": "()J"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljdk/management/jfr/RecordingInfo;"}], "flds": [{"acc": 18, "nme": "id", "dsc": "J"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "state", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "dumpOnExit", "dsc": "Z"}, {"acc": 18, "nme": "size", "dsc": "J"}, {"acc": 18, "nme": "toDisk", "dsc": "Z"}, {"acc": 18, "nme": "maxAge", "dsc": "J"}, {"acc": 18, "nme": "maxSize", "dsc": "J"}, {"acc": 18, "nme": "startTime", "dsc": "J"}, {"acc": 18, "nme": "stopTime", "dsc": "J"}, {"acc": 18, "nme": "destination", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "durationInSeconds", "dsc": "J"}, {"acc": 18, "nme": "settings", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/jdk/management/jfr/DiskRepository$State.class": {"ver": 65, "acc": 16432, "nme": "jdk/management/jfr/DiskRepository$State", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/management/jfr/DiskRepository$State;"}, {"nme": "valueOf", "acc": 9, "dsc": "(L<PERSON><PERSON>/lang/String;)Ljdk/management/jfr/DiskRepository$State;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "next", "acc": 1, "dsc": "()Ljdk/management/jfr/DiskRepository$State;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/management/jfr/DiskRepository$State;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "HEADER", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "EVENT_SIZE", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "EVENT_TYPE", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_TIMESTAMP", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_DURATION", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_DELTA", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_FLUSH_TYPE", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_POOL_COUNT", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_HEADER_TYPE", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_HEADER_ITEM_COUNT", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_HEADER_KEY", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_HEADER_BYTE_ARRAY_LENGTH", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "CHECKPOINT_EVENT_HEADER_BYTE_ARRAY_CONTENT", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 16409, "nme": "EVENT_PAYLOAD", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/management/jfr/DiskRepository$State;"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$2.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl;)V"}, {"nme": "run", "acc": 1, "dsc": "()Ljdk/jfr/Recording;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/management/jfr/SettingDescriptorInfo.class": {"ver": 65, "acc": 49, "nme": "jdk/management/jfr/SettingDescriptorInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/jfr/SettingDescriptor;)V"}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/management/openmbean/CompositeData;)V"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getTypeName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getContentType", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDefaultValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "from", "acc": 9, "dsc": "(Ljavax/management/openmbean/CompositeData;)Ljdk/management/jfr/SettingDescriptorInfo;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "label", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "description", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "typeName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "contentType", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "defaultValue", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$4.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$4", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl;Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$mxbeanListener", "dsc": "Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;"}]}, "classes/jdk/management/jfr/FlightRecorderMXBean.class": {"ver": 65, "acc": 1537, "nme": "jdk/management/jfr/FlightRecorderMXBean", "super": "java/lang/Object", "mthds": [{"nme": "newRecording", "acc": 1025, "dsc": "()J", "exs": ["java/lang/IllegalStateException", "java/lang/SecurityException"]}, {"nme": "takeSnapshot", "acc": 1025, "dsc": "()J"}, {"nme": "cloneRecording", "acc": 1025, "dsc": "(JZ)J", "exs": ["java/lang/IllegalArgumentException", "java/lang/SecurityException"]}, {"nme": "startRecording", "acc": 1025, "dsc": "(J)V", "exs": ["java/lang/IllegalStateException", "java/lang/SecurityException"]}, {"nme": "stopRecording", "acc": 1025, "dsc": "(J)Z", "exs": ["java/lang/IllegalArgumentException", "java/lang/IllegalStateException", "java/lang/SecurityException"]}, {"nme": "closeRecording", "acc": 1025, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "openStream", "acc": 1025, "dsc": "(JLjava/util/Map;)J", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)J", "exs": ["java/io/IOException"]}, {"nme": "closeStream", "acc": 1025, "dsc": "(J)V", "exs": ["java/io/IOException"]}, {"nme": "readStream", "acc": 1025, "dsc": "(J)[B", "exs": ["java/io/IOException"]}, {"nme": "getRecordingOptions", "acc": 1025, "dsc": "(J)Ljava/util/Map;", "sig": "(J)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getRecordingSettings", "acc": 1025, "dsc": "(J)Ljava/util/Map;", "sig": "(J)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setConfiguration", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setPredefinedConfiguration", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setRecordingSettings", "acc": 1025, "dsc": "(JLjava/util/Map;)V", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "setRecordingOptions", "acc": 1025, "dsc": "(JLjava/util/Map;)V", "sig": "(JLjava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "getRecordings", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/RecordingInfo;>;"}, {"nme": "getConfigurations", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/ConfigurationInfo;>;"}, {"nme": "getEventTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/management/jfr/EventTypeInfo;>;"}, {"nme": "copyTo", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException", "java/lang/SecurityException"]}], "flds": [{"acc": 25, "nme": "MXBEAN_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.management.jfr:type=FlightRecorder"}]}, "classes/jdk/management/jfr/SettingDescriptorInfo$1.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/SettingDescriptorInfo$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "call", "acc": 1, "dsc": "()Ljdk/management/jfr/FlightRecorderMXBean;", "exs": ["java/lang/Exception"]}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/jdk/management/jfr/FileDump.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/FileDump", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(J)V"}, {"nme": "add", "acc": 33, "dsc": "(Ljdk/management/jfr/DiskRepository$DiskChunk;)V"}, {"nme": "isComplete", "acc": 33, "dsc": "()Z"}, {"nme": "setComplete", "acc": 33, "dsc": "()V"}, {"nme": "close", "acc": 33, "dsc": "()V"}, {"nme": "oldestChunk", "acc": 2, "dsc": "()Ljdk/management/jfr/DiskRepository$DiskChunk;", "exs": ["java/lang/InterruptedException"]}, {"nme": "write", "acc": 1, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException", "java/lang/InterruptedException"]}], "flds": [{"acc": 18, "nme": "chunks", "dsc": "<PERSON><PERSON><PERSON>/util/Deque;", "sig": "Ljava/util/Deque<Ljdk/management/jfr/DiskRepository$DiskChunk;>;"}, {"acc": 18, "nme": "stopTimeMillis", "dsc": "J"}, {"acc": 2, "nme": "complete", "dsc": "Z"}]}, "classes/jdk/management/jfr/DiskRepository.class": {"ver": 65, "acc": 48, "nme": "jdk/management/jfr/DiskRepository", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;Z)V", "exs": ["java/io/IOException"]}, {"nme": "write", "acc": 33, "dsc": "([B)V", "exs": ["java/io/IOException"]}, {"nme": "processFlush", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "processNumericValueInEvent", "acc": 2, "dsc": "()V"}, {"nme": "processEvent", "acc": 2, "dsc": "()V"}, {"nme": "processEventTypeId", "acc": 2, "dsc": "()V"}, {"nme": "processEventSize", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "processInitialHeader", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "processCheckpointHeader", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "writeInitialHeader", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "completePrevious", "acc": 2, "dsc": "(Ljdk/management/jfr/DiskRepository$DiskChunk;)V", "exs": ["java/io/IOException"]}, {"nme": "writeCheckpointHeader", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 2, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "nextByte", "acc": 2, "dsc": "(Z)B"}, {"nme": "nextChunk", "acc": 2, "dsc": "()Ljdk/management/jfr/DiskRepository$DiskChunk;", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 33, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "setMaxAge", "acc": 33, "dsc": "(<PERSON><PERSON>va/time/Duration;)V"}, {"nme": "setMaxSize", "acc": 33, "dsc": "(J)V"}, {"nme": "trimToSize", "acc": 2, "dsc": "()V"}, {"nme": "trimToAge", "acc": 2, "dsc": "(<PERSON>java/time/Instant;)V"}, {"nme": "removeOldestChunk", "acc": 2, "dsc": "()V"}, {"nme": "onChunkComplete", "acc": 33, "dsc": "(J)V"}, {"nme": "addChunk", "acc": 2, "dsc": "(Ljdk/management/jfr/DiskRepository$DiskChunk;)V"}, {"nme": "cleanUpDeadChunk", "acc": 2, "dsc": "(I)V"}, {"nme": "complete", "acc": 33, "dsc": "()V"}, {"nme": "newDump", "acc": 33, "dsc": "(J)Ljdk/management/jfr/FileDump;"}, {"nme": "activateStreamBarrier", "acc": 1, "dsc": "()Ljdk/jfr/internal/management/StreamBarrier;"}], "flds": [{"acc": 24, "nme": "CHECKPOINT_WITH_HEADER", "dsc": "B", "val": 2}, {"acc": 24, "nme": "MODIFYING_STATE", "dsc": "B", "val": -1}, {"acc": 24, "nme": "COMPLETE_STATE", "dsc": "B", "val": 0}, {"acc": 24, "nme": "HEADER_FILE_STATE_POSITION", "dsc": "I", "val": 64}, {"acc": 24, "nme": "HEADER_START_NANOS_POSITION", "dsc": "I", "val": 32}, {"acc": 24, "nme": "HEADER_SIZE", "dsc": "I", "val": 68}, {"acc": 24, "nme": "HEADER_FILE_DURATION", "dsc": "I", "val": 40}, {"acc": 18, "nme": "chunks", "dsc": "<PERSON><PERSON><PERSON>/util/Deque;", "sig": "Ljava/util/Deque<Ljdk/management/jfr/DiskRepository$DiskChunk;>;"}, {"acc": 18, "nme": "deadChunks", "dsc": "<PERSON><PERSON><PERSON>/util/Deque;", "sig": "Ljava/util/Deque<Ljdk/management/jfr/DiskRepository$DiskChunk;>;"}, {"acc": 18, "nme": "fileDumps", "dsc": "<PERSON><PERSON><PERSON>/util/Deque;", "sig": "Ljava/util/Deque<Ljdk/management/jfr/FileDump;>;"}, {"acc": 18, "nme": "deleteDirectory", "dsc": "Z"}, {"acc": 18, "nme": "buffer", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}, {"acc": 18, "nme": "directory", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "chunkFilename", "dsc": "Ljdk/jfr/internal/management/ChunkFilename;"}, {"acc": 18, "nme": "barrier", "dsc": "Ljdk/jfr/internal/management/StreamBarrier;"}, {"acc": 2, "nme": "raf", "dsc": "Ljava/io/RandomAccessFile;"}, {"acc": 2, "nme": "previousRAF", "dsc": "Ljava/io/RandomAccessFile;"}, {"acc": 2, "nme": "previousRAFstate", "dsc": "B"}, {"acc": 2, "nme": "index", "dsc": "I"}, {"acc": 2, "nme": "bufferIndex", "dsc": "I"}, {"acc": 2, "nme": "state", "dsc": "Ljdk/management/jfr/DiskRepository$State;"}, {"acc": 2, "nme": "currentByteArray", "dsc": "[B"}, {"acc": 2, "nme": "typeId", "dsc": "J"}, {"acc": 2, "nme": "typeIdshift", "dsc": "I"}, {"acc": 2, "nme": "sizeShift", "dsc": "I"}, {"acc": 2, "nme": "payLoadSize", "dsc": "J"}, {"acc": 2, "nme": "longValueshift", "dsc": "I"}, {"acc": 2, "nme": "eventFieldSize", "dsc": "I"}, {"acc": 2, "nme": "lastFlush", "dsc": "I"}, {"acc": 2, "nme": "currentChunk", "dsc": "Ljdk/management/jfr/DiskRepository$DiskChunk;"}, {"acc": 2, "nme": "maxAge", "dsc": "<PERSON><PERSON><PERSON>/time/Duration;"}, {"acc": 2, "nme": "maxSize", "dsc": "J"}, {"acc": 2, "nme": "size", "dsc": "J"}]}, "classes/jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener$1.class": {"ver": 65, "acc": 32, "nme": "jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;Ljdk/jfr/Recording;)V", "sig": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 4112, "nme": "val$recording", "dsc": "Ljdk/jfr/Recording;"}, {"acc": 4112, "nme": "this$1", "dsc": "Ljdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener;"}]}}}}