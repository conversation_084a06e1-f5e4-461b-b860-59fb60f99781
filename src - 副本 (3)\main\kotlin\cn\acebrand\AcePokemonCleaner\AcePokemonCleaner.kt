package cn.acebrand.AcePokemonCleaner

import org.bukkit.plugin.java.JavaPlugin
import org.bukkit.scheduler.BukkitRunnable
import org.bukkit.Bukkit
import org.bukkit.boss.BossBar
import java.util.logging.Level
import cn.acebrand.AcePokemonCleaner.license.LicenseManager

/**
 * AcePokemonCleaner - 精灵定时清理插件
 *
 * 功能：
 * - 定时清理指定世界中的野生精灵
 * - 可配置清理间隔、指定世界、排除精灵列表
 * - 智能清理逻辑，不清理玩家拥有的、正在战斗的精灵
 */
class AcePokemonCleaner : JavaPlugin() {

    private lateinit var configManager: ConfigManager
    private lateinit var pokemonCleaner: PokemonCleaner
    private lateinit var itemExclusionGUI: ItemExclusionGUI
    private lateinit var licenseManager: LicenseManager
    private var cleanerRunnable: BukkitRunnable? = null
    private var countdownTask: BukkitRunnable? = null
    private var nextCleanTime: Long = 0
    private var currentBossBar: BossBar? = null

    override fun onEnable() {
        logger.info("AcePokemonCleaner 正在启动...")

        // 初始化配置管理器
        configManager = ConfigManager(this)
        configManager.loadConfig()

        // 初始化许可证系统
        if (!initializeLicense()) {
            return
        }

        // 初始化精灵清理器
        pokemonCleaner = PokemonCleaner(this, configManager)

        // 初始化掉落物排除GUI
        itemExclusionGUI = ItemExclusionGUI(this, configManager.itemExclusionManager)
        server.pluginManager.registerEvents(itemExclusionGUI, this)

        // 注册命令
        getCommand("pokemoncleaner")?.setExecutor(Commands(this, configManager, pokemonCleaner, itemExclusionGUI))

        // 启动定时清理任务
        startCleanerTask()

        // 启动倒计时任务
        if (configManager.enableCountdown) {
            startCountdownTask()
        }

        // 启动 BossBar 任务
        if (configManager.bossBarFlag) {
            startBossBarTask()
        }

        logger.info("AcePokemonCleaner 已成功启动！")
        logger.info("清理间隔: ${configManager.cleanInterval} 秒")
        logger.info("启用的世界: ${configManager.enabledWorlds}")
        logger.info("注意：插件将通过实体类型检测 Pokemon，适用于 Arclight 混合服务端")
    }

    override fun onDisable() {
        logger.info("AcePokemonCleaner 正在关闭...")

        // 停止定时任务
        stopCleanerTask()

        // 清理资源
        if (::pokemonCleaner.isInitialized) {
            pokemonCleaner.cleanup()
        }

        // 清理许可证管理器
        if (::licenseManager.isInitialized) {
            licenseManager.cleanup()
        }

        logger.info("AcePokemonCleaner 已关闭。")
    }

    /**
     * 初始化许可证系统
     */
    private fun initializeLicense(): Boolean {
        // 初始化许可证系统
        logger.info("╔══════════════════════════════════════════════════════════════╗")
        logger.info("║                    🔐 AceBrand 许可证系统 🔐                 ║")
        logger.info("╠══════════════════════════════════════════════════════════════╣")
        logger.info("║  🚀 正在启动 AceBrand 许可证验证...                          ║")
        logger.info("║  🌐 验证服务器: cn.AceBrand.com                              ║")
        logger.info("║  📞 技术支持 QQ: 337871509                                   ║")
        logger.info("╚══════════════════════════════════════════════════════════════╝")

        return try {
            // 检查许可证密钥是否为空
            if (configManager.licenseKey.isBlank()) {
                logger.severe("╔══════════════════════════════════════════════════════════════╗")
                logger.severe("║                    🚫 许可证验证失败 🚫                      ║")
                logger.severe("╠══════════════════════════════════════════════════════════════╣")
                logger.severe("║  ❌ 错误：未配置许可证密钥！                                  ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📝 解决方案：                                               ║")
                logger.severe("║     请在 config.yml 中的 license-key 字段填写您的许可证密钥  ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📋 示例配置：                                               ║")
                logger.severe("║     license-key: \"857ff611-618f-40db-8fe2-291b388f0cf0\"     ║")
                logger.severe("║                                                              ║")
                logger.severe("║  ⚠️  插件将被禁用，直到配置有效的许可证密钥                   ║")
                logger.severe("╚══════════════════════════════════════════════════════════════╝")
                server.pluginManager.disablePlugin(this)
                return false
            }

            // 初始化许可证管理器
            licenseManager = LicenseManager(this)
            val isValid = licenseManager.validateLicenseKey(configManager.licenseKey)

            if (!isValid) {
                logger.severe("╔══════════════════════════════════════════════════════════════╗")
                logger.severe("║                    🚫 许可证验证失败 🚫                      ║")
                logger.severe("╠══════════════════════════════════════════════════════════════╣")
                logger.severe("║  ❌ 错误：提供的许可证密钥无效或已过期                        ║")
                logger.severe("║                                                              ║")
                logger.severe("║  🔍 可能的原因：                                             ║")
                logger.severe("║     • 许可证密钥输入错误                                     ║")
                logger.severe("║     • 许可证已过期                                           ║")
                logger.severe("║     • 网络连接问题                                           ║")
                logger.severe("║     • 服务器无法访问 AceBrand 许可证服务                     ║")
                logger.severe("║                                                              ║")
                logger.severe("║  📞 联系支持：                                               ║")
                logger.severe("║     QQ: 337871509 (AceBrand 技术支持)                       ║")
                logger.severe("║                                                              ║")
                logger.severe("║  ⚠️  插件将被禁用                                            ║")
                logger.severe("╚══════════════════════════════════════════════════════════════╝")
                server.pluginManager.disablePlugin(this)
                return false
            }

            logger.info("╔══════════════════════════════════════════════════════════════╗")
            logger.info("║                    ✅ 许可证验证成功 ✅                      ║")
            logger.info("╠══════════════════════════════════════════════════════════════╣")
            logger.info("║  🎉 恭喜！您的许可证已通过 AceBrand 服务器验证                ║")
            logger.info("║  🔒 插件功能已解锁，可以正常使用                              ║")
            logger.info("║  💓 心跳监控已启动，每15分钟自动验证                          ║")
            logger.info("╚══════════════════════════════════════════════════════════════╝")
            true

        } catch (e: Exception) {
            logger.severe("╔══════════════════════════════════════════════════════════════╗")
            logger.severe("║                    💥 系统初始化错误 💥                      ║")
            logger.severe("╠══════════════════════════════════════════════════════════════╣")
            logger.severe("║  ❌ 初始化许可证系统时发生错误: ${e.message}")
            logger.severe("║                                                              ║")
            logger.severe("║  🔧 可能的解决方案：                                         ║")
            logger.severe("║     • 检查网络连接                                           ║")
            logger.severe("║     • 重启服务器                                             ║")
            logger.severe("║     • 联系技术支持 QQ: 337871509                             ║")
            logger.severe("║                                                              ║")
            logger.severe("║  ⚠️  插件将被禁用                                            ║")
            logger.severe("╚══════════════════════════════════════════════════════════════╝")
            e.printStackTrace()
            server.pluginManager.disablePlugin(this)
            false
        }
    }

    /**
     * 启动定时清理任务
     */
    fun startCleanerTask() {
        stopCleanerTask() // 先停止现有任务

        if (configManager.cleanInterval <= 0) {
            logger.warning("清理间隔设置无效，跳过启动定时任务")
            return
        }

        cleanerRunnable = object : BukkitRunnable() {
            override fun run() {
                try {
                    pokemonCleaner.performClean()
                    // 更新下次清理时间
                    nextCleanTime = System.currentTimeMillis() + (configManager.cleanInterval * 1000L)
                } catch (e: Exception) {
                    logger.log(Level.SEVERE, "执行清理任务时发生错误", e)
                }
            }
        }

        // 转换为 ticks (20 ticks = 1 second)
        val intervalTicks = configManager.cleanInterval * 20L
        cleanerRunnable?.runTaskTimer(this, intervalTicks, intervalTicks)

        // 设置初始的下次清理时间
        nextCleanTime = System.currentTimeMillis() + (configManager.cleanInterval * 1000L)

        logger.info("定时清理任务已启动，间隔: ${configManager.cleanInterval} 秒")
    }

    /**
     * 停止定时清理任务
     */
    fun stopCleanerTask() {
        cleanerRunnable?.cancel()
        cleanerRunnable = null
        countdownTask?.cancel()
        countdownTask = null

        // 清理 BossBar
        currentBossBar?.removeAll()
        currentBossBar = null
    }

    /**
     * 启动倒计时任务
     */
    private fun startCountdownTask() {
        if (!configManager.enableCountdown) {
            return
        }

        countdownTask = object : BukkitRunnable() {
            override fun run() {
                try {
                    val currentTime = System.currentTimeMillis()
                    val remainingSeconds = ((nextCleanTime - currentTime) / 1000).toInt()

                    // 检查是否需要发送倒计时公告
                    if (remainingSeconds > 0 && configManager.countdownTimes.contains(remainingSeconds)) {
                        sendCountdownAnnouncement(remainingSeconds)
                    }

                    // 更新 BossBar
                    if (configManager.bossBarFlag) {
                        updateBossBar(remainingSeconds)
                    }
                } catch (e: Exception) {
                    logger.log(Level.WARNING, "执行倒计时任务时发生错误", e)
                }
            }
        }

        // 每秒检查一次
        countdownTask?.runTaskTimer(this, 20L, 20L)
        logger.info("倒计时任务已启动")
    }

    /**
     * 发送倒计时公告
     */
    private fun sendCountdownAnnouncement(remainingSeconds: Int) {
        try {
            val message = configManager.countdownMessage
                .replace("{time}", remainingSeconds.toString())

            // 向所有在线玩家发送消息
            for (player in Bukkit.getOnlinePlayers()) {
                player.sendMessage(message)
            }

            // 同时在控制台输出
            logger.info("倒计时公告: 距离下次清理还有 $remainingSeconds 秒")

        } catch (e: Exception) {
            logger.log(Level.WARNING, "发送倒计时公告时发生错误", e)
        }
    }

    /**
     * 重载配置
     */
    fun reloadConfiguration() {
        configManager.loadConfig()

        // 重新验证许可证
        if (::licenseManager.isInitialized) {
            if (configManager.licenseKey.isBlank()) {
                logger.severe("许可证密钥为空，重新加载失败")
                return
            }

            if (!licenseManager.validateLicenseKey(configManager.licenseKey)) {
                logger.severe("许可证验证失败，重新加载失败")
                return
            }
        }

        startCleanerTask() // 重新启动任务以应用新配置
        if (configManager.enableCountdown) {
            startCountdownTask()
        }
        if (configManager.bossBarFlag) {
            startBossBarTask()
        }
        logger.info("配置已重载")
    }

    /**
     * 检查许可证是否有效
     */
    fun isLicenseValid(): Boolean {
        if (!::licenseManager.isInitialized) return false
        return licenseManager.isValid()
    }

    /**
     * 启动 BossBar 任务
     */
    private fun startBossBarTask() {
        if (!configManager.bossBarFlag) {
            return
        }
        logger.info("BossBar 任务已启动")
    }

    /**
     * 更新 BossBar
     */
    private fun updateBossBar(remainingSeconds: Int) {
        try {
            val bossBarConfig = configManager.bossBarMessageForCount[remainingSeconds]
            if (bossBarConfig != null) {
                // 清理旧的 BossBar
                currentBossBar?.removeAll()

                // 创建新的 BossBar
                val message = if (remainingSeconds == 0) {
                    // 清理完成时显示清理结果
                    bossBarConfig.message.replace("{count}", "0") // 这里会在清理完成后更新
                } else {
                    bossBarConfig.message
                }

                currentBossBar = Bukkit.createBossBar(
                    message,
                    bossBarConfig.color,
                    bossBarConfig.style
                )

                // 设置进度条进度（基于剩余时间，倒计时减少）
                val maxTime = configManager.cleanInterval
                val progress = if (maxTime > 0) {
                    remainingSeconds.toDouble() / maxTime.toDouble()
                } else {
                    0.0
                }
                currentBossBar?.progress = progress.coerceIn(0.0, 1.0)

                // 添加所有在线玩家
                for (player in Bukkit.getOnlinePlayers()) {
                    currentBossBar?.addPlayer(player)
                }

                // 如果是清理完成（0秒），3秒后移除 BossBar
                if (remainingSeconds == 0) {
                    Bukkit.getScheduler().runTaskLater(this, Runnable {
                        currentBossBar?.removeAll()
                        currentBossBar = null
                    }, 60L) // 3秒后移除
                }
            }
        } catch (e: Exception) {
            logger.log(Level.WARNING, "更新 BossBar 时发生错误", e)
        }
    }

    /**
     * 显示清理完成的 BossBar
     */
    fun showCleanupCompleteBossBar(totalCount: Int, pokemonCount: Int = 0, vanillaCount: Int = 0, itemCount: Int = 0) {
        if (!configManager.bossBarFlag) {
            return
        }

        try {
            val bossBarConfig = configManager.bossBarMessageForCount[0]
            if (bossBarConfig != null) {
                // 清理旧的 BossBar
                currentBossBar?.removeAll()

                // 创建清理完成的 BossBar，支持多种变量替换
                val message = bossBarConfig.message
                    .replace("{count}", totalCount.toString())
                    .replace("{pokemonCount}", pokemonCount.toString())
                    .replace("{vanillaCount}", vanillaCount.toString())
                    .replace("{itemCount}", itemCount.toString())

                currentBossBar = Bukkit.createBossBar(
                    message,
                    bossBarConfig.color,
                    bossBarConfig.style
                )

                currentBossBar?.progress = 1.0

                // 添加所有在线玩家
                for (player in Bukkit.getOnlinePlayers()) {
                    currentBossBar?.addPlayer(player)
                }

                // 5秒后移除 BossBar（延长显示时间以便查看详细信息）
                Bukkit.getScheduler().runTaskLater(this, Runnable {
                    currentBossBar?.removeAll()
                    currentBossBar = null
                }, 100L) // 5秒后移除
            }
        } catch (e: Exception) {
            logger.log(Level.WARNING, "显示清理完成 BossBar 时发生错误", e)
        }
    }

    /**
     * 获取配置管理器
     */
    fun getConfigManager(): ConfigManager = configManager

    /**
     * 获取精灵清理器
     */
    fun getPokemonCleaner(): PokemonCleaner = pokemonCleaner
}
