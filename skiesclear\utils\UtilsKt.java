/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.serialization.Codec
 *  com.mojang.serialization.codecs.RecordCodecBuilder
 *  kotlin.Metadata
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.utils;

import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.function.Function;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=2, xi=48, d1={"\u0000\u001c\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u001aK\u0010\b\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00028\u00000\u0007\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00028\u00000\u0005\u00a2\u0006\u0004\b\b\u0010\t\u001aS\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00028\u00000\u0007\"\u0004\b\u0000\u0010\u0000\"\u0004\b\u0001\u0010\u0001*\b\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00028\u0001\u0012\u0004\u0012\u00028\u00000\u00052\u0006\u0010\n\u001a\u00028\u0000\u00a2\u0006\u0004\b\u000b\u0010\f\u00a8\u0006\r"}, d2={"A", "B", "Lcom/mojang/serialization/Codec;", "", "id", "Ljava/util/function/Function;", "getter", "Lcom/mojang/serialization/codecs/RecordCodecBuilder;", "recordCodec", "(Lcom/mojang/serialization/Codec;Ljava/lang/String;Ljava/util/function/Function;)Lcom/mojang/serialization/codecs/RecordCodecBuilder;", "default", "optionalRecordCodec", "(Lcom/mojang/serialization/Codec;Ljava/lang/String;Ljava/util/function/Function;Ljava/lang/Object;)Lcom/mojang/serialization/codecs/RecordCodecBuilder;", "SkiesClear"})
public final class UtilsKt {
    @NotNull
    public static final <A, B> RecordCodecBuilder<B, A> recordCodec(@NotNull Codec<A> $this$recordCodec, @NotNull String id, @NotNull Function<B, A> getter) {
        Intrinsics.checkNotNullParameter($this$recordCodec, (String)"<this>");
        Intrinsics.checkNotNullParameter((Object)id, (String)"id");
        Intrinsics.checkNotNullParameter(getter, (String)"getter");
        RecordCodecBuilder recordCodecBuilder = $this$recordCodec.fieldOf(id).forGetter(getter);
        Intrinsics.checkNotNullExpressionValue((Object)recordCodecBuilder, (String)"forGetter(...)");
        return recordCodecBuilder;
    }

    @NotNull
    public static final <A, B> RecordCodecBuilder<B, A> optionalRecordCodec(@NotNull Codec<A> $this$optionalRecordCodec, @NotNull String id, @NotNull Function<B, A> getter, A a) {
        Intrinsics.checkNotNullParameter($this$optionalRecordCodec, (String)"<this>");
        Intrinsics.checkNotNullParameter((Object)id, (String)"id");
        Intrinsics.checkNotNullParameter(getter, (String)"getter");
        RecordCodecBuilder recordCodecBuilder = $this$optionalRecordCodec.fieldOf(id).orElse(a).forGetter(getter);
        Intrinsics.checkNotNullExpressionValue((Object)recordCodecBuilder, (String)"forGetter(...)");
        return recordCodecBuilder;
    }
}

