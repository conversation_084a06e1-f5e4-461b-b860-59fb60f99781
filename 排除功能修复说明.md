# 精灵清理排除功能修复说明 (Cobblemon API版本)

## 问题分析

原来的排除功能没有生效的主要原因：

1. **混淆问题**：Cobblemon mod的包是混淆的，反射无法正常工作
2. **API使用错误**：没有使用正确的Cobblemon API来检查精灵属性
3. **检查方式错误**：依赖反射和名称匹配而不是官方API

## 新的修复方案

### 1. 使用正确的Cobblemon API

参考SkiesClear mod的实现，使用官方的Cobblemon API：

```kotlin
// 导入正确的Cobblemon类
import com.cobblemon.mod.common.entity.pokemon.PokemonEntity
import com.cobblemon.mod.common.pokemon.Pokemon

// 正确的实体检查
entity is PokemonEntity

// 正确的属性检查
val pokemon = entity.pokemon
pokemon.shiny              // 检查闪光
pokemon.isLegendary()      // 检查传说
pokemon.isPlayerOwned()    // 检查是否被玩家拥有
entity.isBattling()        // 检查是否在战斗
entity.isBusy()           // 检查是否繁忙
```

### 2. 完整的排除检查机制

现在支持以下排除类型：

1. **闪光精灵**：`pokemon.shiny` - 使用官方API检查
2. **传说精灵**：`pokemon.isLegendary()` - 使用官方API检查
3. **幻之精灵**：基于种类名称的已知列表检查
4. **被拥有精灵**：`pokemon.isPlayerOwned()` - 检查是否被玩家拥有
5. **战斗中精灵**：`entity.isBattling()` - 检查是否在战斗
6. **繁忙精灵**：`entity.isBusy()` - 检查是否繁忙（如进化中）
7. **种类名称匹配**：`pokemon.species.resourceIdentifier` - 精确的种类匹配

### 3. 增强的调试功能

新增了 `/pokemoncleaner debug` 命令，可以：

- 扫描附近的精灵实体
- 显示每个精灵的详细信息
- 显示排除状态和原因
- 提供详细的API调试信息
- 显示实体类型Key和完整类名
- 显示名称匹配检查结果

## 使用方法

### 1. 配置文件设置

在 `config.yml` 中确保排除配置正确：

```yaml
excluded-pokemon:
  - "legendary"  # 排除所有传说精灵
  - "mythical"   # 排除所有幻之精灵
  - "shiny"      # 排除所有闪光精灵

# 启用调试模式获取更多信息
debug-mode: true
enable-logging: true
```

### 2. 测试步骤

1. **重载配置**：
   ```
   /pokemoncleaner reload
   ```

2. **调试检查**：
   ```
   /pokemoncleaner debug
   ```
   这会显示附近精灵的详细信息，包括是否被正确识别为闪光/传说/幻之精灵

3. **手动清理测试**：
   ```
   /pokemoncleaner clean
   ```
   观察是否正确排除了配置中的精灵类型

### 3. 日志检查

启用调试模式后，插件会在日志中输出详细信息：

- 精灵被排除的原因
- 反射检查的结果
- 属性识别的过程

## 预期效果

修复后的插件应该能够：

1. **正确识别闪光精灵**：通过多种方式检测闪光状态
2. **正确识别传说精灵**：基于种类名称和已知列表
3. **正确识别幻之精灵**：基于种类名称和已知列表
4. **提供详细调试信息**：帮助诊断问题

## 故障排除

如果排除功能仍然不工作：

1. **检查配置**：确保 `excluded-pokemon` 列表格式正确
2. **使用调试命令**：`/pokemoncleaner debug` 查看精灵属性识别情况
3. **查看日志**：启用 `debug-mode: true` 查看详细日志
4. **检查权限**：确保有 `pokemoncleaner.admin` 权限

## 技术细节

### 新的检查逻辑

```kotlin
// 通过Bukkit API检查闪光状态
private fun isShinyByName(entity: Entity): Boolean {
    // 1. 实体名称关键词检查
    // 2. 自定义名称关键词检查
    // 3. 实体类型名称检查
    // 4. 实体类型Key检查
    // 5. 记分板标签检查
    // 6. 持久化数据检查
}
```

### 实体识别逻辑

```kotlin
private fun isPokemonEntity(entity: Entity): Boolean {
    // 1. 检查entity.type.key是否包含"cobblemon:pokemon"
    // 2. 检查类名是否包含"Pokemon"和"Entity"
    // 3. 检查类型名称是否包含"pokemon"
    // 4. 检查完整类名是否包含"cobblemon"和"pokemon"
}
```

### 传说精灵列表

插件内置了常见的传说精灵名称列表，包括：
- 第一世代：Articuno, Zapdos, Moltres, Mewtwo, Mew
- 第二世代：Raikou, Entei, Suicune, Lugia, Ho-Oh, Celebi
- 等等...

可以根据需要扩展这个列表。
