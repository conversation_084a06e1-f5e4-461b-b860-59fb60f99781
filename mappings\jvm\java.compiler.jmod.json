{"md5": "d9a18aabd98a0ab1c50492debfb83604", "sha2": "dc0fa5c162964854c2e4fe50345daac9899a8f35", "sha256": "e32b96e6be68b9d2c1ef7f5c69bb3e50dd2ed20c1c3f8381fbecc8cd73f7173d", "contents": {"classes": {"classes/javax/annotation/processing/FilerException.class": {"ver": 65, "acc": 33, "nme": "javax/annotation/processing/FilerException", "super": "java/io/IOException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": 8426423106453163293}]}, "classes/javax/lang/model/util/ElementScanner6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementScanner6", "super": "javax/lang/model/util/AbstractElementVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "scan", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;TP;)TR;"}, {"nme": "scan", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "scan", "acc": 17, "dsc": "(Ljavax/lang/model/element/Element;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;)TR;"}, {"nme": "visitPackage", "acc": 1, "dsc": "(Ljavax/lang/model/element/PackageElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/PackageElement;TP;)TR;"}, {"nme": "visitType", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitTypeParameter", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeParameterElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeParameterElement;TP;)TR;"}], "flds": [{"acc": 20, "nme": "DEFAULT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TR;"}], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/type/NullType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/NullType", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/javax/tools/FileObject.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/FileObject", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljava/net/URI;"}, {"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "openInputStream", "acc": 1025, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "openOutputStream", "acc": 1025, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "openReader", "acc": 1025, "dsc": "(Z)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Z)Ljava/lang/CharSequence;", "exs": ["java/io/IOException"]}, {"nme": "openWriter", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "getLastModified", "acc": 1025, "dsc": "()J"}, {"nme": "delete", "acc": 1025, "dsc": "()Z"}], "flds": []}, "classes/javax/tools/StandardLocation$1.class": {"ver": 65, "acc": 32, "nme": "javax/tools/StandardLocation$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isOutputLocation", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 4112, "nme": "val$name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/javax/annotation/processing/ProcessingEnvironment.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/ProcessingEnvironment", "super": "java/lang/Object", "mthds": [{"nme": "getOptions", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "getMessager", "acc": 1025, "dsc": "()Ljavax/annotation/processing/Messager;"}, {"nme": "getFiler", "acc": 1025, "dsc": "()Ljavax/annotation/processing/Filer;"}, {"nme": "getElementUtils", "acc": 1025, "dsc": "()Ljavax/lang/model/util/Elements;"}, {"nme": "getTypeUtils", "acc": 1025, "dsc": "()Ljavax/lang/model/util/Types;"}, {"nme": "getSourceVersion", "acc": 1025, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "getLocale", "acc": 1025, "dsc": "()Ljava/util/Locale;"}, {"nme": "isPreviewEnabled", "acc": 1, "dsc": "()Z"}], "flds": []}, "classes/javax/lang/model/util/AbstractAnnotationValueVisitor14.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractAnnotationValueVisitor14", "super": "javax/lang/model/util/AbstractAnnotationValueVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/tools/Tool.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/Tool", "super": "java/lang/Object", "mthds": [{"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "run", "acc": 1153, "dsc": "(<PERSON><PERSON><PERSON>/io/InputStream;<PERSON><PERSON><PERSON>/io/OutputStream;L<PERSON><PERSON>/io/OutputStream;[<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getSourceVersions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljavax/lang/model/SourceVersion;>;"}], "flds": []}, "classes/javax/lang/model/util/AbstractTypeVisitor8.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractTypeVisitor8", "super": "javax/lang/model/util/AbstractTypeVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "visitIntersection", "acc": 1025, "dsc": "(Ljavax/lang/model/type/IntersectionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/IntersectionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/javax/annotation/processing/Completions.class": {"ver": 65, "acc": 33, "nme": "javax/annotation/processing/Completions", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "of", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/lang/String;)Ljavax/annotation/processing/Completion;"}, {"nme": "of", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/annotation/processing/Completion;"}], "flds": []}, "classes/javax/lang/model/util/AbstractElementVisitor8.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractElementVisitor8", "super": "javax/lang/model/util/AbstractElementVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/lang/model/type/NoType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/NoType", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/javax/lang/model/util/SimpleTypeVisitor8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleTypeVisitor8", "super": "javax/lang/model/util/SimpleTypeVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitIntersection", "acc": 1, "dsc": "(Ljavax/lang/model/type/IntersectionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/IntersectionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/lang/model/AnnotatedConstruct.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/AnnotatedConstruct", "super": "java/lang/Object", "mthds": [{"nme": "getAnnotationMirrors", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/AnnotationMirror;>;"}, {"nme": "getAnnotation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)TA;"}, {"nme": "getAnnotationsByType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)[TA;"}], "flds": []}, "classes/javax/lang/model/type/DeclaredType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/DeclaredType", "super": "java/lang/Object", "mthds": [{"nme": "asElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getEnclosingType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getTypeArguments", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}], "flds": []}, "classes/javax/lang/model/util/ElementScanner9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementScanner9", "super": "javax/lang/model/util/ElementScanner8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/element/ModuleElement$UsesDirective.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$UsesDirective", "super": "java/lang/Object", "mthds": [{"nme": "getService", "acc": 1025, "dsc": "()Ljavax/lang/model/element/TypeElement;"}], "flds": []}, "classes/javax/lang/model/util/ElementKindVisitor7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementKindVisitor7", "super": "javax/lang/model/util/ElementKindVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitVariableAsResourceVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/util/TypeKindVisitor14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/TypeKindVisitor14", "super": "javax/lang/model/util/TypeKindVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/lang/model/util/SimpleElementVisitor14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleElementVisitor14", "super": "javax/lang/model/util/SimpleElementVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitRecordComponent", "acc": 1, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/tools/ForwardingJavaFileObject.class": {"ver": 65, "acc": 33, "nme": "javax/tools/ForwardingJavaFileObject", "super": "javax/tools/ForwardingFileObject", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljavax/tools/JavaFileObject;)V", "sig": "(TF;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "isNameCompatible", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;)Z"}, {"nme": "getNestingKind", "acc": 1, "dsc": "()Ljavax/lang/model/element/NestingKind;"}, {"nme": "getAccessLevel", "acc": 1, "dsc": "()Ljavax/lang/model/element/Modifier;"}], "flds": []}, "classes/javax/lang/model/UnknownEntityException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/UnknownEntityException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}]}, "classes/javax/lang/model/type/PrimitiveType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/PrimitiveType", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/javax/lang/model/element/AnnotationMirror.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/AnnotationMirror", "super": "java/lang/Object", "mthds": [{"nme": "getAnnotationType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/DeclaredType;"}, {"nme": "getElement<PERSON><PERSON>ues", "acc": 1025, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<+Ljavax/lang/model/element/ExecutableElement;+Ljavax/lang/model/element/AnnotationValue;>;"}], "flds": []}, "classes/javax/lang/model/element/VariableElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/VariableElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getConstantValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "is<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z", "invanns": [{"dsc": "Ljdk/internal/javac/PreviewFeature;", "vals": ["feature", ["Ljdk/internal/javac/PreviewFeature$Feature;", "UNNAMED"], "reflective", true]}]}], "flds": []}, "classes/javax/tools/StandardJavaFileManager.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/StandardJavaFileManager", "super": "java/lang/Object", "mthds": [{"nme": "isSameFile", "acc": 1025, "dsc": "(Ljavax/tools/FileObject;Ljavax/tools/FileObject;)Z"}, {"nme": "getJavaFileObjectsFromFiles", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(Ljava/lang/Iterable<+Ljava/io/File;>;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "getJavaFileObjectsFromPaths", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/Iterable;", "sig": "(Ljava/util/Collection<+Ljava/nio/file/Path;>;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "getJavaFileObjectsFromPaths", "acc": 131073, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(Ljava/lang/Iterable<+Ljava/nio/file/Path;>;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "13"]}]}, {"nme": "getJavaFileObjects", "acc": 1153, "dsc": "([<PERSON><PERSON><PERSON>/io/File;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "([Ljava/io/File;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "getJavaFileObjects", "acc": 129, "dsc": "([<PERSON><PERSON><PERSON>/nio/file/Path;)Ljava/lang/Iterable;", "sig": "([Ljava/nio/file/Path;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "getJavaFileObjectsFromStrings", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(Ljava/lang/Iterable<Ljava/lang/String;>;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "getJavaFileObjects", "acc": 1153, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)<PERSON>ja<PERSON>/lang/Iterable;", "sig": "([Ljava/lang/String;)Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;"}, {"nme": "setLocation", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Iterable;)V", "sig": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Iterable<+Ljava/io/File;>;)V", "exs": ["java/io/IOException"]}, {"nme": "setLocationFromPaths", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/util/Collection;)V", "sig": "(Ljavax/tools/JavaFileManager$Location;Ljava/util/Collection<+Ljava/nio/file/Path;>;)V", "exs": ["java/io/IOException"]}, {"nme": "setLocationForModule", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Collection;)V", "sig": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Collection<+Ljava/nio/file/Path;>;)V", "exs": ["java/io/IOException"]}, {"nme": "getLocation", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable<+Ljava/io/File;>;"}, {"nme": "getLocationAsPaths", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable<+Ljava/nio/file/Path;>;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/tools/FileObject;)Ljava/nio/file/Path;"}, {"nme": "setPathFactory", "acc": 1, "dsc": "(Ljavax/tools/StandardJavaFileManager$PathFactory;)V"}, {"nme": "asPaths", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(Ljava/lang/Iterable<+Ljava/io/File;>;)Ljava/lang/Iterable<Ljava/nio/file/Path;>;"}, {"nme": "asFiles", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/lang/Iterable;", "sig": "(Ljava/lang/Iterable<+Ljava/nio/file/Path;>;)Ljava/lang/Iterable<Ljava/io/File;>;"}, {"nme": "asCollection", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)Ljava/util/Collection;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Iterable<TT;>;)Ljava/util/Collection<TT;>;"}, {"nme": "lambda$asFiles$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/util/Iterator;"}, {"nme": "lambda$asPaths$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON><PERSON><PERSON>/util/Iterator;"}], "flds": []}, "classes/javax/lang/model/element/ModuleElement$RequiresDirective.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$RequiresDirective", "super": "java/lang/Object", "mthds": [{"nme": "isStatic", "acc": 1025, "dsc": "()Z"}, {"nme": "isTransitive", "acc": 1025, "dsc": "()Z"}, {"nme": "getDependency", "acc": 1025, "dsc": "()Ljavax/lang/model/element/ModuleElement;"}], "flds": []}, "classes/javax/lang/model/util/ElementScanner14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementScanner14", "super": "javax/lang/model/util/ElementScanner9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitType", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "createScanningList", "acc": 2, "dsc": "(Ljavax/lang/model/element/Parameterizable;Ljava/util/List;)Ljava/util/List;", "sig": "(Ljavax/lang/model/element/Parameterizable;Ljava/util/List<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "visitRecordComponent", "acc": 1, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/lang/model/util/SimpleAnnotationValueVisitor14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleAnnotationValueVisitor14", "super": "javax/lang/model/util/SimpleAnnotationValueVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/tools/DiagnosticListener.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/DiagnosticListener", "super": "java/lang/Object", "mthds": [{"nme": "report", "acc": 1025, "dsc": "(Ljavax/tools/Diagnostic;)V", "sig": "(Ljavax/tools/Diagnostic<+TS;>;)V"}], "flds": []}, "classes/javax/lang/model/util/Elements$Origin.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/util/Elements$Origin", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "isDeclared", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "EXPLICIT", "dsc": "Ljavax/lang/model/util/Elements$Origin;"}, {"acc": 16409, "nme": "MANDATED", "dsc": "Ljavax/lang/model/util/Elements$Origin;"}, {"acc": 16409, "nme": "SYNTHETIC", "dsc": "Ljavax/lang/model/util/Elements$Origin;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/util/Elements$Origin;"}]}, "classes/javax/annotation/processing/Completion.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/Completion", "super": "java/lang/Object", "mthds": [{"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/javax/lang/model/util/AbstractAnnotationValueVisitor7.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractAnnotationValueVisitor7", "super": "javax/lang/model/util/AbstractAnnotationValueVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/tools/SimpleJavaFileObject.class": {"ver": 65, "acc": 33, "nme": "javax/tools/SimpleJavaFileObject", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljava/net/URI;Ljavax/tools/JavaFileObject$Kind;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "openInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "openOutputStream", "acc": 1, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "openReader", "acc": 1, "dsc": "(Z)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Z)Ljava/lang/CharSequence;", "exs": ["java/io/IOException"]}, {"nme": "openWriter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "getLastModified", "acc": 1, "dsc": "()J"}, {"nme": "delete", "acc": 1, "dsc": "()Z"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "isNameCompatible", "acc": 1, "dsc": "(Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;)Z"}, {"nme": "getNestingKind", "acc": 1, "dsc": "()Ljavax/lang/model/element/NestingKind;"}, {"nme": "getAccessLevel", "acc": 1, "dsc": "()Ljavax/lang/model/element/Modifier;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 20, "nme": "uri", "dsc": "Ljava/net/URI;"}, {"acc": 20, "nme": "kind", "dsc": "Ljavax/tools/JavaFileObject$Kind;"}]}, "classes/javax/tools/DocumentationTool$Location.class": {"ver": 65, "acc": 16433, "nme": "javax/tools/DocumentationTool$Location", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/tools/DocumentationTool$Location;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/tools/DocumentationTool$Location;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isOutputLocation", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/tools/DocumentationTool$Location;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DOCUMENTATION_OUTPUT", "dsc": "Ljavax/tools/DocumentationTool$Location;"}, {"acc": 16409, "nme": "DOCLET_PATH", "dsc": "Ljavax/tools/DocumentationTool$Location;"}, {"acc": 16409, "nme": "TAGLET_PATH", "dsc": "Ljavax/tools/DocumentationTool$Location;"}, {"acc": 16409, "nme": "SNIPPET_PATH", "dsc": "Ljavax/tools/DocumentationTool$Location;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/tools/DocumentationTool$Location;"}]}, "classes/javax/lang/model/element/AnnotationValueVisitor.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/AnnotationValueVisitor", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1025, "dsc": "(Ljavax/lang/model/element/AnnotationValue;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;TP;)TR;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljavax/lang/model/element/AnnotationValue;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;)TR;"}, {"nme": "visitBoolean", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(ZTP;)TR;"}, {"nme": "visitByte", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(BTP;)TR;"}, {"nme": "visitChar", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(CTP;)TR;"}, {"nme": "visitDouble", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(DTP;)TR;"}, {"nme": "visitFloat", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(FTP;)TR;"}, {"nme": "visitInt", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(ITP;)TR;"}, {"nme": "visitLong", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(JTP;)TR;"}, {"nme": "visitShort", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(STP;)TR;"}, {"nme": "visitString", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/String;TP;)TR;"}, {"nme": "visitType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visitEnumConstant", "acc": 1025, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitAnnotation", "acc": 1025, "dsc": "(Ljavax/lang/model/element/AnnotationMirror;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationMirror;TP;)TR;"}, {"nme": "visitArray", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Ljava/util/List<+Ljavax/lang/model/element/AnnotationValue;>;TP;)TR;"}, {"nme": "visitUnknown", "acc": 1025, "dsc": "(Ljavax/lang/model/element/AnnotationValue;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/SimpleElementVisitor8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleElementVisitor8", "super": "javax/lang/model/util/SimpleElementVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/annotation/processing/AbstractProcessor.class": {"ver": 65, "acc": 1057, "nme": "javax/annotation/processing/AbstractProcessor", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "getSupportedOptions", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getSupportedAnnotationTypes", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getSupportedSourceVersion", "acc": 1, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "init", "acc": 33, "dsc": "(Ljavax/annotation/processing/ProcessingEnvironment;)V"}, {"nme": "process", "acc": 1025, "dsc": "(Ljava/util/Set;Ljavax/annotation/processing/RoundEnvironment;)Z", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/TypeElement;>;Ljavax/annotation/processing/RoundEnvironment;)Z"}, {"nme": "getCompletions", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;Ljavax/lang/model/element/ExecutableElement;Ljava/lang/String;)Ljava/lang/Iterable;", "sig": "(Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;Ljavax/lang/model/element/ExecutableElement;Ljava/lang/String;)Ljava/lang/Iterable<+Ljavax/annotation/processing/Completion;>;"}, {"nme": "isInitialized", "acc": 36, "dsc": "()Z"}, {"nme": "arrayToSet", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "([<PERSON><PERSON><PERSON>/lang/String;Z<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4, "nme": "processingEnv", "dsc": "Ljavax/annotation/processing/ProcessingEnvironment;"}, {"acc": 2, "nme": "initialized", "dsc": "Z"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/javax/lang/model/element/ModuleElement$DirectiveVisitor.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$DirectiveVisitor", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement$Directive;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$Directive;)TR;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement$Directive;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$Directive;TP;)TR;"}, {"nme": "visitRequires", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$RequiresDirective;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$RequiresDirective;TP;)TR;"}, {"nme": "visitExports", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$ExportsDirective;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$ExportsDirective;TP;)TR;"}, {"nme": "visitOpens", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$OpensDirective;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$OpensDirective;TP;)TR;"}, {"nme": "visitUses", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$UsesDirective;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$UsesDirective;TP;)TR;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$ProvidesDirective;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$ProvidesDirective;TP;)TR;"}, {"nme": "visitUnknown", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement$Directive;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement$Directive;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/element/Modifier.class": {"ver": 65, "acc": 16417, "nme": "javax/lang/model/element/Modifier", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/element/Modifier;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljavax/lang/model/element/Modifier;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/element/Modifier;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PUBLIC", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "PROTECTED", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "PRIVATE", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "ABSTRACT", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "DEFAULT", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "STATIC", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "SEALED", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "NON_SEALED", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "FINAL", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "TRANSIENT", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "VOLATILE", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "SYNCHRONIZED", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "NATIVE", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 16409, "nme": "STRICTFP", "dsc": "Ljavax/lang/model/element/Modifier;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/element/Modifier;"}]}, "classes/javax/lang/model/element/TypeElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/TypeElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getEnclosedElements", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getNestingKind", "acc": 1025, "dsc": "()Ljavax/lang/model/element/NestingKind;"}, {"nme": "getQualifiedName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "is<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Z", "invanns": [{"dsc": "Ljdk/internal/javac/PreviewFeature;", "vals": ["feature", ["Ljdk/internal/javac/PreviewFeature$Feature;", "UNNAMED_CLASSES"], "reflective", true]}]}, {"nme": "getSuperclass", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getInterfaces", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "getTypeParameters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/TypeParameterElement;>;"}, {"nme": "getRecordComponents", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/RecordComponentElement;>;"}, {"nme": "getPermittedSubclasses", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}], "flds": []}, "classes/javax/lang/model/util/ElementScanner8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementScanner8", "super": "javax/lang/model/util/ElementScanner7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/lang/model/type/ExecutableType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/ExecutableType", "super": "java/lang/Object", "mthds": [{"nme": "getTypeVariables", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeVariable;>;"}, {"nme": "getReturnType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getParameterTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "getReceiverType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getThrownTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}], "flds": []}, "classes/javax/lang/model/util/AbstractElementVisitor6.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractElementVisitor6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/element/Element;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;)TR;"}, {"nme": "visitUnknown", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "visitModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}, {"nme": "visitRecordComponent", "acc": 1, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/util/SimpleAnnotationValueVisitor7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleAnnotationValueVisitor7", "super": "javax/lang/model/util/SimpleAnnotationValueVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/tools/Diagnostic.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/Diagnostic", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljavax/tools/Diagnostic$Kind;"}, {"nme": "getSource", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TS;"}, {"nme": "getPosition", "acc": 1025, "dsc": "()J"}, {"nme": "getStartPosition", "acc": 1025, "dsc": "()J"}, {"nme": "getEndPosition", "acc": 1025, "dsc": "()J"}, {"nme": "getLineNumber", "acc": 1025, "dsc": "()J"}, {"nme": "getColumnNumber", "acc": 1025, "dsc": "()J"}, {"nme": "getCode", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/Locale;)Ljava/lang/String;"}], "flds": [{"acc": 25, "nme": "NOPOS", "dsc": "J", "val": -1}]}, "classes/javax/lang/model/type/ReferenceType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/ReferenceType", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/javax/tools/OptionChecker.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/OptionChecker", "super": "java/lang/Object", "mthds": [{"nme": "isSupportedOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}], "flds": []}, "classes/javax/lang/model/element/QualifiedNameable.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/QualifiedNameable", "super": "java/lang/Object", "mthds": [{"nme": "getQualifiedName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}], "flds": []}, "classes/javax/tools/JavaFileManager.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/JavaFileManager", "super": "java/lang/Object", "mthds": [{"nme": "getClassLoader", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/ClassLoader;"}, {"nme": "list", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Set;Z)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Set<Ljavax/tools/JavaFileObject$Kind;>;Z)Ljava/lang/Iterable<Ljavax/tools/JavaFileObject;>;", "exs": ["java/io/IOException"]}, {"nme": "inferBinaryName", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/JavaFileObject;)Ljava/lang/String;"}, {"nme": "isSameFile", "acc": 1025, "dsc": "(Ljavax/tools/FileObject;Ljavax/tools/FileObject;)Z"}, {"nme": "handleOption", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Iterator;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/util/Iterator<Ljava/lang/String;>;)Z"}, {"nme": "hasLocation", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Z"}, {"nme": "getJavaFileForInput", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getJavaFileForOutput", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;Ljavax/tools/FileObject;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getJavaFileForOutputForOriginatingFiles", "acc": 129, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;[Ljavax/tools/FileObject;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForInput", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForOutput", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;Ljavax/tools/FileObject;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForOutputForOriginatingFiles", "acc": 129, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;[Ljavax/tools/FileObject;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getLocationForModule", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;)Ljavax/tools/JavaFileManager$Location;", "exs": ["java/io/IOException"]}, {"nme": "getLocationForModule", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/JavaFileObject;)Ljavax/tools/JavaFileManager$Location;", "exs": ["java/io/IOException"]}, {"nme": "getServiceLoader", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Class;)Ljava/util/ServiceLoader;", "sig": "<S:Ljava/lang/Object;>(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Class<TS;>;)Ljava/util/ServiceLoader<TS;>;", "exs": ["java/io/IOException"]}, {"nme": "inferModuleName", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "listLocationsForModules", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable<Ljava/util/Set<Ljavax/tools/JavaFileManager$Location;>;>;", "exs": ["java/io/IOException"]}, {"nme": "contains", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/FileObject;)Z", "exs": ["java/io/IOException"]}, {"nme": "siblingFrom", "acc": 10, "dsc": "([Ljavax/tools/FileObject;)Ljavax/tools/FileObject;"}], "flds": []}, "classes/javax/tools/Diagnostic$Kind.class": {"ver": 65, "acc": 16433, "nme": "javax/tools/Diagnostic$Kind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/tools/Diagnostic$Kind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/tools/Diagnostic$Kind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/tools/Diagnostic$Kind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "ERROR", "dsc": "Ljavax/tools/Diagnostic$Kind;"}, {"acc": 16409, "nme": "WARNING", "dsc": "Ljavax/tools/Diagnostic$Kind;"}, {"acc": 16409, "nme": "MANDATORY_WARNING", "dsc": "Ljavax/tools/Diagnostic$Kind;"}, {"acc": 16409, "nme": "NOTE", "dsc": "Ljavax/tools/Diagnostic$Kind;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Ljavax/tools/Diagnostic$Kind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/tools/Diagnostic$Kind;"}]}, "classes/javax/lang/model/element/ModuleElement$ProvidesDirective.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$ProvidesDirective", "super": "java/lang/Object", "mthds": [{"nme": "getService", "acc": 1025, "dsc": "()Ljavax/lang/model/element/TypeElement;"}, {"nme": "getImplementations", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/TypeElement;>;"}], "flds": []}, "classes/javax/lang/model/util/TypeKindVisitor6$1.class": {"ver": 65, "acc": 4128, "nme": "javax/lang/model/util/TypeKindVisitor6$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$javax$lang$model$type$TypeKind", "dsc": "[I"}]}, "classes/javax/tools/JavaFileObject$Kind.class": {"ver": 65, "acc": 16433, "nme": "javax/tools/JavaFileObject$Kind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SOURCE", "dsc": "Ljavax/tools/JavaFileObject$Kind;"}, {"acc": 16409, "nme": "CLASS", "dsc": "Ljavax/tools/JavaFileObject$Kind;"}, {"acc": 16409, "nme": "HTML", "dsc": "Ljavax/tools/JavaFileObject$Kind;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Ljavax/tools/JavaFileObject$Kind;"}, {"acc": 17, "nme": "extension", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/tools/JavaFileObject$Kind;"}]}, "classes/javax/lang/model/util/AbstractAnnotationValueVisitor8.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractAnnotationValueVisitor8", "super": "javax/lang/model/util/AbstractAnnotationValueVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/lang/model/util/ElementKindVisitor9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementKindVisitor9", "super": "javax/lang/model/util/ElementKindVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/util/ElementScanner7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementScanner7", "super": "javax/lang/model/util/ElementScanner6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/element/RecordComponentElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/RecordComponentElement", "super": "java/lang/Object", "mthds": [{"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getAccessor", "acc": 1025, "dsc": "()Ljavax/lang/model/element/ExecutableElement;"}], "flds": []}, "classes/javax/lang/model/type/TypeMirror.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/TypeMirror", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeKind;"}, {"nme": "equals", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1025, "dsc": "()I"}, {"nme": "toString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getAnnotationMirrors", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/AnnotationMirror;>;"}, {"nme": "getAnnotation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)TA;"}, {"nme": "getAnnotationsByType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)[TA;"}, {"nme": "accept", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeVisitor;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;P:Ljava/lang/Object;>(Ljavax/lang/model/type/TypeVisitor<TR;TP;>;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/type/TypeVariable.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/TypeVariable", "super": "java/lang/Object", "mthds": [{"nme": "asElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getUpperBound", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getLowerBound", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}], "flds": []}, "classes/javax/tools/StandardJavaFileManager$PathFactory.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/StandardJavaFileManager$PathFactory", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1153, "dsc": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;"}], "flds": []}, "classes/javax/lang/model/element/UnknownElementException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/element/UnknownElementException", "super": "javax/lang/model/UnknownEntityException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)V"}, {"nme": "getUnknownElement", "acc": 1, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getArgument", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 130, "nme": "element", "dsc": "Ljavax/lang/model/element/Element;"}, {"acc": 130, "nme": "parameter", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/javax/tools/StandardJavaFileManager$2.class": {"ver": 65, "acc": 32, "nme": "javax/tools/StandardJavaFileManager$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Ljava/io/File;"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 16, "nme": "iter", "dsc": "<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "Ljava/util/Iterator<+Ljava/nio/file/Path;>;"}, {"acc": 4112, "nme": "val$paths", "dsc": "<PERSON><PERSON><PERSON>/lang/Iterable;"}]}, "classes/javax/lang/model/util/AbstractElementVisitor14.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractElementVisitor14", "super": "javax/lang/model/util/AbstractElementVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "visitRecordComponent", "acc": 1025, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/tools/DocumentationTool$DocumentationTask.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/DocumentationTool$DocumentationTask", "super": "java/lang/Object", "mthds": [{"nme": "addModules", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(L<PERSON><PERSON>/lang/Iterable<Ljava/lang/String;>;)V"}, {"nme": "setLocale", "acc": 1025, "dsc": "(Ljava/util/Locale;)V"}, {"nme": "call", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/javax/tools/JavaFileObject.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/JavaFileObject", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljavax/tools/JavaFileObject$Kind;"}, {"nme": "isNameCompatible", "acc": 1025, "dsc": "(Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;)Z"}, {"nme": "getNestingKind", "acc": 1025, "dsc": "()Ljavax/lang/model/element/NestingKind;"}, {"nme": "getAccessLevel", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Modifier;"}], "flds": []}, "classes/javax/lang/model/util/SimpleAnnotationValueVisitor6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleAnnotationValueVisitor6", "super": "javax/lang/model/util/AbstractAnnotationValueVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "defaultAction", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(<PERSON><PERSON><PERSON>/lang/Object;TP;)TR;"}, {"nme": "visitBoolean", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(ZTP;)TR;"}, {"nme": "visitByte", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(BTP;)TR;"}, {"nme": "visitChar", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(CTP;)TR;"}, {"nme": "visitDouble", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(DTP;)TR;"}, {"nme": "visitFloat", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(FTP;)TR;"}, {"nme": "visitInt", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(ITP;)TR;"}, {"nme": "visitLong", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(JTP;)TR;"}, {"nme": "visitShort", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(STP;)TR;"}, {"nme": "visitString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(L<PERSON><PERSON>/lang/String;TP;)TR;"}, {"nme": "visitType", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visitEnumConstant", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitAnnotation", "acc": 1, "dsc": "(Ljavax/lang/model/element/AnnotationMirror;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationMirror;TP;)TR;"}, {"nme": "visitArray", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/lang/Object;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "(Ljava/util/List<+Ljavax/lang/model/element/AnnotationValue;>;TP;)TR;"}], "flds": [{"acc": 20, "nme": "DEFAULT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TR;"}], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/annotation/processing/SupportedSourceVersion.class": {"ver": 65, "acc": 9729, "nme": "javax/annotation/processing/SupportedSourceVersion", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()Ljavax/lang/model/SourceVersion;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "classes/javax/lang/model/element/ElementKind.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/element/ElementKind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/element/ElementKind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljavax/lang/model/element/ElementKind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "isClass", "acc": 1, "dsc": "()Z"}, {"nme": "isInterface", "acc": 1, "dsc": "()Z"}, {"nme": "isDeclaredType", "acc": 1, "dsc": "()Z"}, {"nme": "isField", "acc": 1, "dsc": "()Z"}, {"nme": "isExecutable", "acc": 1, "dsc": "()Z"}, {"nme": "isInitializer", "acc": 1, "dsc": "()Z"}, {"nme": "isVariable", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/element/ElementKind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "PACKAGE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "ENUM", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "CLASS", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "ANNOTATION_TYPE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "INTERFACE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "ENUM_CONSTANT", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "FIELD", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "PARAMETER", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "LOCAL_VARIABLE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "EXCEPTION_PARAMETER", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "METHOD", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "CONSTRUCTOR", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "STATIC_INIT", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "INSTANCE_INIT", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "TYPE_PARAMETER", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "RESOURCE_VARIABLE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "MODULE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "RECORD", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "RECORD_COMPONENT", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 16409, "nme": "BINDING_VARIABLE", "dsc": "Ljavax/lang/model/element/ElementKind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/element/ElementKind;"}]}, "classes/javax/lang/model/type/TypeVisitor.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/TypeVisitor", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;)TR;"}, {"nme": "visitPrimitive", "acc": 1025, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitNull", "acc": 1025, "dsc": "(Ljavax/lang/model/type/NullType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NullType;TP;)TR;"}, {"nme": "visitArray", "acc": 1025, "dsc": "(Ljavax/lang/model/type/ArrayType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ArrayType;TP;)TR;"}, {"nme": "visitDeclared", "acc": 1025, "dsc": "(Ljavax/lang/model/type/DeclaredType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/DeclaredType;TP;)TR;"}, {"nme": "visitError", "acc": 1025, "dsc": "(Ljavax/lang/model/type/ErrorType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ErrorType;TP;)TR;"}, {"nme": "visitTypeVariable", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeVariable;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeVariable;TP;)TR;"}, {"nme": "visitWildcard", "acc": 1025, "dsc": "(Ljavax/lang/model/type/WildcardType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/WildcardType;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1025, "dsc": "(Ljavax/lang/model/type/ExecutableType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ExecutableType;TP;)TR;"}, {"nme": "visitNoType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}, {"nme": "visitUnknown", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visitUnion", "acc": 1025, "dsc": "(Ljavax/lang/model/type/UnionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/UnionType;TP;)TR;"}, {"nme": "visitIntersection", "acc": 1025, "dsc": "(Ljavax/lang/model/type/IntersectionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/IntersectionType;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/AbstractElementVisitor7.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractElementVisitor7", "super": "javax/lang/model/util/AbstractElementVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/util/AbstractTypeVisitor14.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractTypeVisitor14", "super": "javax/lang/model/util/AbstractTypeVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/lang/model/util/AbstractAnnotationValueVisitor9.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractAnnotationValueVisitor9", "super": "javax/lang/model/util/AbstractAnnotationValueVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/element/Parameterizable.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/Parameterizable", "super": "java/lang/Object", "mthds": [{"nme": "getTypeParameters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/TypeParameterElement;>;"}], "flds": []}, "classes/javax/tools/DocumentationTool.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/DocumentationTool", "super": "java/lang/Object", "mthds": [{"nme": "getTask", "acc": 1025, "dsc": "(Ljava/io/Writer;Ljavax/tools/JavaFileManager;Ljavax/tools/DiagnosticListener;Ljava/lang/Class;Ljava/lang/Iterable;Ljava/lang/Iterable;)Ljavax/tools/DocumentationTool$DocumentationTask;", "sig": "(Ljava/io/Writer;Ljavax/tools/JavaFileManager;Ljavax/tools/DiagnosticListener<-Ljavax/tools/JavaFileObject;>;Ljava/lang/Class<*>;Ljava/lang/Iterable<Ljava/lang/String;>;Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;)Ljavax/tools/DocumentationTool$DocumentationTask;"}, {"nme": "getStandardFileManager", "acc": 1025, "dsc": "(Ljavax/tools/DiagnosticListener;Ljava/util/Locale;Ljava/nio/charset/Charset;)Ljavax/tools/StandardJavaFileManager;", "sig": "(Ljavax/tools/DiagnosticListener<-Ljavax/tools/JavaFileObject;>;Ljava/util/Locale;Ljava/nio/charset/Charset;)Ljavax/tools/StandardJavaFileManager;"}], "flds": []}, "classes/javax/tools/JavaCompiler.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/JavaCompiler", "super": "java/lang/Object", "mthds": [{"nme": "getTask", "acc": 1025, "dsc": "(Ljava/io/Writer;Ljavax/tools/JavaFileManager;Ljavax/tools/DiagnosticListener;Ljava/lang/Iterable;Ljava/lang/Iterable;Ljava/lang/Iterable;)Ljavax/tools/JavaCompiler$CompilationTask;", "sig": "(Ljava/io/Writer;Ljavax/tools/JavaFileManager;Ljavax/tools/DiagnosticListener<-Ljavax/tools/JavaFileObject;>;Ljava/lang/Iterable<Ljava/lang/String;>;Ljava/lang/Iterable<Ljava/lang/String;>;Ljava/lang/Iterable<+Ljavax/tools/JavaFileObject;>;)Ljavax/tools/JavaCompiler$CompilationTask;"}, {"nme": "getStandardFileManager", "acc": 1025, "dsc": "(Ljavax/tools/DiagnosticListener;Ljava/util/Locale;Ljava/nio/charset/Charset;)Ljavax/tools/StandardJavaFileManager;", "sig": "(Ljavax/tools/DiagnosticListener<-Ljavax/tools/JavaFileObject;>;Ljava/util/Locale;Ljava/nio/charset/Charset;)Ljavax/tools/StandardJavaFileManager;"}], "flds": []}, "classes/javax/lang/model/element/ModuleElement$Directive.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$Directive", "super": "java/lang/Object", "mthds": [{"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"nme": "accept", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement$DirectiveVisitor;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;P:Ljava/lang/Object;>(Ljavax/lang/model/element/ModuleElement$DirectiveVisitor<TR;TP;>;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/ElementKindVisitor8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementKindVisitor8", "super": "javax/lang/model/util/ElementKindVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/tools/ForwardingFileObject.class": {"ver": 65, "acc": 33, "nme": "javax/tools/ForwardingFileObject", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljavax/tools/FileObject;)V", "sig": "(TF;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/net/URI;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "openInputStream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}, {"nme": "openOutputStream", "acc": 1, "dsc": "()Ljava/io/OutputStream;", "exs": ["java/io/IOException"]}, {"nme": "openReader", "acc": 1, "dsc": "(Z)<PERSON><PERSON><PERSON>/io/Reader;", "exs": ["java/io/IOException"]}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Z)Ljava/lang/CharSequence;", "exs": ["java/io/IOException"]}, {"nme": "openWriter", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/io/Writer;", "exs": ["java/io/IOException"]}, {"nme": "getLastModified", "acc": 1, "dsc": "()J"}, {"nme": "delete", "acc": 1, "dsc": "()Z"}], "flds": [{"acc": 20, "nme": "fileObject", "dsc": "Ljavax/tools/FileObject;", "sig": "TF;"}]}, "classes/javax/lang/model/util/SimpleTypeVisitor9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleTypeVisitor9", "super": "javax/lang/model/util/SimpleTypeVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/type/IntersectionType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/IntersectionType", "super": "java/lang/Object", "mthds": [{"nme": "getBounds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}], "flds": []}, "classes/javax/lang/model/element/ExecutableElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ExecutableElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getTypeParameters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/TypeParameterElement;>;"}, {"nme": "getReturnType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getParameters", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/VariableElement;>;"}, {"nme": "getReceiverType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "isVarArgs", "acc": 1025, "dsc": "()Z"}, {"nme": "isDefault", "acc": 1025, "dsc": "()Z"}, {"nme": "getThrownTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "getDefaultValue", "acc": 1025, "dsc": "()Ljavax/lang/model/element/AnnotationValue;"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}], "flds": []}, "classes/javax/lang/model/element/ElementVisitor.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ElementVisitor", "super": "java/lang/Object", "mthds": [{"nme": "visit", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "visit", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;)TR;"}, {"nme": "visitPackage", "acc": 1025, "dsc": "(Ljavax/lang/model/element/PackageElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/PackageElement;TP;)TR;"}, {"nme": "visitType", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitVariable", "acc": 1025, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitTypeParameter", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeParameterElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeParameterElement;TP;)TR;"}, {"nme": "visitUnknown", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "visitModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}, {"nme": "visitRecordComponent", "acc": 1, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/AbstractAnnotationValueVisitor6.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractAnnotationValueVisitor6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/element/AnnotationValue;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;TP;)TR;"}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/element/AnnotationValue;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;)TR;"}, {"nme": "visitUnknown", "acc": 1, "dsc": "(Ljavax/lang/model/element/AnnotationValue;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/AnnotationValue;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/util/Elements.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/util/Elements", "super": "java/lang/Object", "mthds": [{"nme": "getPackageElement", "acc": 1025, "dsc": "(Ljava/lang/CharSequence;)Ljavax/lang/model/element/PackageElement;"}, {"nme": "getPackageElement", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/CharSequence;)Ljavax/lang/model/element/PackageElement;"}, {"nme": "getAllPackageElements", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)<PERSON>ja<PERSON>/util/Set;", "sig": "(Ljava/lang/CharSequence;)Ljava/util/Set<+Ljavax/lang/model/element/PackageElement;>;"}, {"nme": "getTypeElement", "acc": 1025, "dsc": "(Ljava/lang/CharSequence;)Ljavax/lang/model/element/TypeElement;"}, {"nme": "getTypeElement", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/CharSequence;)Ljavax/lang/model/element/TypeElement;"}, {"nme": "getAllTypeElements", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)<PERSON>ja<PERSON>/util/Set;", "sig": "(Ljava/lang/CharSequence;)Ljava/util/Set<+Ljavax/lang/model/element/TypeElement;>;"}, {"nme": "getModuleElement", "acc": 1, "dsc": "(Ljava/lang/CharSequence;)Ljavax/lang/model/element/ModuleElement;"}, {"nme": "getAllModuleElements", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<+Ljavax/lang/model/element/ModuleElement;>;"}, {"nme": "getElementValuesWithDefaults", "acc": 1025, "dsc": "(Ljavax/lang/model/element/AnnotationMirror;)Ljava/util/Map;", "sig": "(Ljavax/lang/model/element/AnnotationMirror;)Ljava/util/Map<+Ljavax/lang/model/element/ExecutableElement;+Ljavax/lang/model/element/AnnotationValue;>;"}, {"nme": "getDocComment", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;)Ljava/lang/String;"}, {"nme": "isDeprecated", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;)Z"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;)Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/lang/model/AnnotatedConstruct;Ljavax/lang/model/element/AnnotationMirror;)Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljavax/lang/model/element/ModuleElement$Directive;)Ljavax/lang/model/util/Elements$Origin;"}, {"nme": "isBridge", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;)Z"}, {"nme": "getBinaryName", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeElement;)Ljavax/lang/model/element/Name;"}, {"nme": "getPackageOf", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;)Ljavax/lang/model/element/PackageElement;"}, {"nme": "getModuleOf", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;)Ljavax/lang/model/element/ModuleElement;"}, {"nme": "getAllMembers", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeElement;)Ljava/util/List;", "sig": "(Ljavax/lang/model/element/TypeElement;)Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getOutermostTypeElement", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;)Ljavax/lang/model/element/TypeElement;"}, {"nme": "getAllAnnotationMirrors", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;)Ljava/util/List;", "sig": "(Ljavax/lang/model/element/Element;)Ljava/util/List<+Ljavax/lang/model/element/AnnotationMirror;>;"}, {"nme": "hides", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;Ljavax/lang/model/element/Element;)Z"}, {"nme": "overrides", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljavax/lang/model/element/ExecutableElement;Ljavax/lang/model/element/TypeElement;)Z"}, {"nme": "getConstantExpression", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "printElements", "acc": 1153, "dsc": "(L<PERSON><PERSON>/io/Writer;[Ljavax/lang/model/element/Element;)V"}, {"nme": "getName", "acc": 1025, "dsc": "(Ljava/lang/CharSequence;)Ljavax/lang/model/element/Name;"}, {"nme": "isFunctionalInterface", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeElement;)Z"}, {"nme": "isAutomaticModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;)Z"}, {"nme": "recordComponentFor", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;)Ljavax/lang/model/element/RecordComponentElement;"}, {"nme": "isCanonicalConstructor", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;)Z"}, {"nme": "isCompactConstructor", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;)Z"}, {"nme": "getFileObjectOf", "acc": 1, "dsc": "(Ljavax/lang/model/element/Element;)Ljavax/tools/JavaFileObject;"}], "flds": []}, "classes/javax/lang/model/element/ModuleElement$ExportsDirective.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$ExportsDirective", "super": "java/lang/Object", "mthds": [{"nme": "getPackage", "acc": 1025, "dsc": "()Ljavax/lang/model/element/PackageElement;"}, {"nme": "getTargetModules", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/ModuleElement;>;"}], "flds": []}, "classes/javax/lang/model/element/ModuleElement$DirectiveKind.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/element/ModuleElement$DirectiveKind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "REQUIRES", "dsc": "Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"acc": 16409, "nme": "EXPORTS", "dsc": "Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"acc": 16409, "nme": "OPENS", "dsc": "Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"acc": 16409, "nme": "USES", "dsc": "Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"acc": 16409, "nme": "PROVIDES", "dsc": "Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/element/ModuleElement$DirectiveKind;"}]}, "classes/javax/lang/model/element/TypeParameterElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/TypeParameterElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getGenericElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getBounds", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}], "flds": []}, "classes/javax/lang/model/util/SimpleElementVisitor6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleElementVisitor6", "super": "javax/lang/model/util/AbstractElementVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "defaultAction", "acc": 4, "dsc": "(Ljavax/lang/model/element/Element;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/Element;TP;)TR;"}, {"nme": "visitPackage", "acc": 1, "dsc": "(Ljavax/lang/model/element/PackageElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/PackageElement;TP;)TR;"}, {"nme": "visitType", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitTypeParameter", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeParameterElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeParameterElement;TP;)TR;"}], "flds": [{"acc": 20, "nme": "DEFAULT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TR;"}], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/annotation/processing/SupportedAnnotationTypes.class": {"ver": 65, "acc": 9729, "nme": "javax/annotation/processing/SupportedAnnotationTypes", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "classes/javax/lang/model/util/TypeKindVisitor9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/TypeKindVisitor9", "super": "javax/lang/model/util/TypeKindVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitNoTypeAsModule", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/annotation/processing/Generated.class": {"ver": 65, "acc": 9729, "nme": "javax/annotation/processing/Generated", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "date", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "comments", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "SOURCE"]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "PACKAGE"], ["L<PERSON>va/lang/annotation/ElementType;", "TYPE"], ["L<PERSON>va/lang/annotation/ElementType;", "METHOD"], ["L<PERSON>va/lang/annotation/ElementType;", "CONSTRUCTOR"], ["L<PERSON>va/lang/annotation/ElementType;", "FIELD"], ["L<PERSON>va/lang/annotation/ElementType;", "LOCAL_VARIABLE"], ["L<PERSON>va/lang/annotation/ElementType;", "PARAMETER"]]]}]}, "classes/javax/annotation/processing/Processor.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/Processor", "super": "java/lang/Object", "mthds": [{"nme": "getSupportedOptions", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getSupportedAnnotationTypes", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getSupportedSourceVersion", "acc": 1025, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "init", "acc": 1025, "dsc": "(Ljavax/annotation/processing/ProcessingEnvironment;)V"}, {"nme": "process", "acc": 1025, "dsc": "(Ljava/util/Set;Ljavax/annotation/processing/RoundEnvironment;)Z", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/TypeElement;>;Ljavax/annotation/processing/RoundEnvironment;)Z"}, {"nme": "getCompletions", "acc": 1025, "dsc": "(Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;Ljavax/lang/model/element/ExecutableElement;Ljava/lang/String;)Ljava/lang/Iterable;", "sig": "(Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;Ljavax/lang/model/element/ExecutableElement;Ljava/lang/String;)Ljava/lang/Iterable<+Ljavax/annotation/processing/Completion;>;"}], "flds": []}, "classes/javax/tools/ForwardingJavaFileManager.class": {"ver": 65, "acc": 33, "nme": "javax/tools/ForwardingJavaFileManager", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Ljavax/tools/JavaFileManager;)V", "sig": "(TM;)V"}, {"nme": "getClassLoader", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/ClassLoader;"}, {"nme": "list", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Set;Z)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/util/Set<Ljavax/tools/JavaFileObject$Kind;>;Z)Ljava/lang/Iterable<Ljavax/tools/JavaFileObject;>;", "exs": ["java/io/IOException"]}, {"nme": "inferBinaryName", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/JavaFileObject;)Ljava/lang/String;"}, {"nme": "isSameFile", "acc": 1, "dsc": "(Ljavax/tools/FileObject;Ljavax/tools/FileObject;)Z"}, {"nme": "handleOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Iterator;)Z", "sig": "(<PERSON><PERSON><PERSON>/lang/String;Lja<PERSON>/util/Iterator<Ljava/lang/String;>;)Z"}, {"nme": "hasLocation", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Z"}, {"nme": "isSupportedOption", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getJavaFileForInput", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getJavaFileForOutput", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;Ljavax/tools/FileObject;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getJavaFileForOutputForOriginatingFiles", "acc": 129, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljavax/tools/JavaFileObject$Kind;[Ljavax/tools/FileObject;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForInput", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForOutput", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;Ljavax/tools/FileObject;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "getFileForOutputForOriginatingFiles", "acc": 129, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;Ljava/lang/String;[Ljavax/tools/FileObject;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "flush", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getLocationForModule", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/String;)Ljavax/tools/JavaFileManager$Location;", "exs": ["java/io/IOException"]}, {"nme": "getLocationForModule", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/JavaFileObject;)Ljavax/tools/JavaFileManager$Location;", "exs": ["java/io/IOException"]}, {"nme": "getServiceLoader", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Class;)Ljava/util/ServiceLoader;", "sig": "<S:Ljava/lang/Object;>(Ljavax/tools/JavaFileManager$Location;Ljava/lang/Class<TS;>;)Ljava/util/ServiceLoader<TS;>;", "exs": ["java/io/IOException"]}, {"nme": "inferModuleName", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "listLocationsForModules", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable;", "sig": "(Ljavax/tools/JavaFileManager$Location;)Ljava/lang/Iterable<Ljava/util/Set<Ljavax/tools/JavaFileManager$Location;>;>;", "exs": ["java/io/IOException"]}, {"nme": "contains", "acc": 1, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljavax/tools/FileObject;)Z", "exs": ["java/io/IOException"]}], "flds": [{"acc": 20, "nme": "fileManager", "dsc": "Ljavax/tools/JavaFileManager;", "sig": "TM;"}]}, "classes/javax/lang/model/util/Elements$1.class": {"ver": 65, "acc": 4128, "nme": "javax/lang/model/util/Elements$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$javax$lang$model$element$ElementKind", "dsc": "[I"}]}, "classes/javax/lang/model/element/Name.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/Name", "super": "java/lang/Object", "mthds": [{"nme": "equals", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1025, "dsc": "()I"}, {"nme": "contentEquals", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}], "flds": []}, "classes/javax/lang/model/util/SimpleElementVisitor9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleElementVisitor9", "super": "javax/lang/model/util/SimpleElementVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitModule", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/util/Types.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/util/Types", "super": "java/lang/Object", "mthds": [{"nme": "asElement", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/element/Element;"}, {"nme": "isSameType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljavax/lang/model/type/TypeMirror;)Z"}, {"nme": "isSubtype", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljavax/lang/model/type/TypeMirror;)Z"}, {"nme": "isAssignable", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljavax/lang/model/type/TypeMirror;)Z"}, {"nme": "contains", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljavax/lang/model/type/TypeMirror;)Z"}, {"nme": "isSubsignature", "acc": 1025, "dsc": "(Ljavax/lang/model/type/ExecutableType;Ljavax/lang/model/type/ExecutableType;)Z"}, {"nme": "directSupertypes", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljava/util/List;", "sig": "(Ljavax/lang/model/type/TypeMirror;)Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "erasure", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/TypeMirror;"}, {"nme": "boxedClass", "acc": 1025, "dsc": "(Ljavax/lang/model/type/PrimitiveType;)Ljavax/lang/model/element/TypeElement;"}, {"nme": "unboxedType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/PrimitiveType;"}, {"nme": "capture", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getPrimitiveType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeKind;)Ljavax/lang/model/type/PrimitiveType;"}, {"nme": "getNullType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/NullType;"}, {"nme": "getNoType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeKind;)Ljavax/lang/model/type/NoType;"}, {"nme": "getArrayType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/ArrayType;"}, {"nme": "getWildcardType", "acc": 1025, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/WildcardType;"}, {"nme": "getDeclaredType", "acc": 1153, "dsc": "(Ljavax/lang/model/element/TypeElement;[Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/DeclaredType;"}, {"nme": "getDeclaredType", "acc": 1153, "dsc": "(Ljavax/lang/model/type/DeclaredType;Ljavax/lang/model/element/TypeElement;[Ljavax/lang/model/type/TypeMirror;)Ljavax/lang/model/type/DeclaredType;"}, {"nme": "asMemberOf", "acc": 1025, "dsc": "(Ljavax/lang/model/type/DeclaredType;Ljavax/lang/model/element/Element;)Ljavax/lang/model/type/TypeMirror;"}], "flds": []}, "classes/javax/lang/model/util/SimpleAnnotationValueVisitor8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleAnnotationValueVisitor8", "super": "javax/lang/model/util/SimpleAnnotationValueVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}, "classes/javax/tools/StandardJavaFileManager$1.class": {"ver": 65, "acc": 32, "nme": "javax/tools/StandardJavaFileManager$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V"}, {"nme": "hasNext", "acc": 1, "dsc": "()Z"}, {"nme": "next", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "next", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 16, "nme": "iter", "dsc": "<PERSON><PERSON><PERSON>/util/Iterator;", "sig": "Ljava/util/Iterator<+Ljava/io/File;>;"}, {"acc": 4112, "nme": "val$files", "dsc": "<PERSON><PERSON><PERSON>/lang/Iterable;"}]}, "classes/javax/lang/model/util/TypeKindVisitor6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/TypeKindVisitor6", "super": "javax/lang/model/util/SimpleTypeVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "visitPrimitive", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsBoolean", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsByte", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsShort", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsInt", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsLong", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsChar", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsFloat", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitPrimitiveAsDouble", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitNoType", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}, {"nme": "visitNoTypeAsVoid", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}, {"nme": "visitNoTypeAsPackage", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}, {"nme": "visitNoTypeAsModule", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}, {"nme": "visitNoTypeAsNone", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/element/AnnotationValue.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/AnnotationValue", "super": "java/lang/Object", "mthds": [{"nme": "getValue", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"nme": "toString", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "accept", "acc": 1025, "dsc": "(Ljavax/lang/model/element/AnnotationValueVisitor;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;P:Ljava/lang/Object;>(Ljavax/lang/model/element/AnnotationValueVisitor<TR;TP;>;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/AbstractTypeVisitor6.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractTypeVisitor6", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visit", "acc": 17, "dsc": "(Ljavax/lang/model/type/TypeMirror;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;)TR;"}, {"nme": "visitUnion", "acc": 1, "dsc": "(Ljavax/lang/model/type/UnionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/UnionType;TP;)TR;"}, {"nme": "visitIntersection", "acc": 1, "dsc": "(Ljavax/lang/model/type/IntersectionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/IntersectionType;TP;)TR;"}, {"nme": "visitUnknown", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/util/AbstractTypeVisitor9.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractTypeVisitor9", "super": "javax/lang/model/util/AbstractTypeVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/util/ElementKindVisitor14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementKindVisitor14", "super": "javax/lang/model/util/ElementKindVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitRecordComponent", "acc": 1, "dsc": "(Ljavax/lang/model/element/RecordComponentElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/RecordComponentElement;TP;)TR;"}, {"nme": "visitTypeAsRecord", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitVariableAsBindingVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/lang/model/element/ModuleElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getQualifiedName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getEnclosedElements", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "isOpen", "acc": 1025, "dsc": "()Z"}, {"nme": "is<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Z"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getDirectives", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/ModuleElement$Directive;>;"}], "flds": []}, "classes/javax/lang/model/element/UnknownAnnotationValueException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/element/UnknownAnnotationValueException", "super": "javax/lang/model/UnknownEntityException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/lang/model/element/AnnotationValue;Ljava/lang/Object;)V"}, {"nme": "getUnknownAnnotationValue", "acc": 1, "dsc": "()Ljavax/lang/model/element/AnnotationValue;"}, {"nme": "getArgument", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 130, "nme": "av", "dsc": "Ljavax/lang/model/element/AnnotationValue;"}, {"acc": 130, "nme": "parameter", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/javax/lang/model/type/MirroredTypesException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/type/MirroredTypesException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljavax/lang/model/type/TypeMirror;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;)V"}, {"nme": "getTypeMirrors", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 128, "nme": "types", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}]}, "classes/javax/lang/model/util/SimpleTypeVisitor7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleTypeVisitor7", "super": "javax/lang/model/util/SimpleTypeVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitUnion", "acc": 1, "dsc": "(Ljavax/lang/model/type/UnionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/UnionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/annotation/processing/SupportedOptions.class": {"ver": 65, "acc": 9729, "nme": "javax/annotation/processing/SupportedOptions", "super": "java/lang/Object", "mthds": [{"nme": "value", "acc": 1025, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [], "vanns": [{"dsc": "Ljava/lang/annotation/Documented;"}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Target;", "vals": ["value", [["L<PERSON>va/lang/annotation/ElementType;", "TYPE"]]]}, {"dsc": "<PERSON><PERSON><PERSON>/lang/annotation/Retention;", "vals": ["value", ["<PERSON><PERSON><PERSON>/lang/annotation/RetentionPolicy;", "RUNTIME"]]}]}, "classes/javax/lang/model/element/PackageElement.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/PackageElement", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getQualifiedName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getEnclosedElements", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "is<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Z"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}], "flds": []}, "classes/javax/lang/model/util/AbstractElementVisitor9.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractElementVisitor9", "super": "javax/lang/model/util/AbstractElementVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "visitModule", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ModuleElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ModuleElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/annotation/processing/Completions$SimpleCompletion.class": {"ver": 65, "acc": 32, "nme": "javax/annotation/processing/Completions$SimpleCompletion", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getValue", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "message", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/javax/lang/model/type/ErrorType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/ErrorType", "super": "java/lang/Object", "mthds": [], "flds": []}, "classes/javax/lang/model/type/TypeKind.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/type/TypeKind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/type/TypeKind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/lang/model/type/TypeKind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "isPrimitive", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/type/TypeKind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "BOOLEAN", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "BYTE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "SHORT", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "INT", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "LONG", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "CHAR", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "FLOAT", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "DOUBLE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "VOID", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "NONE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "NULL", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "ARRAY", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "DECLARED", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "ERROR", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "TYPEVAR", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "WILDCARD", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "PACKAGE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "EXECUTABLE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "OTHER", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "UNION", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "INTERSECTION", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 16409, "nme": "MODULE", "dsc": "Ljavax/lang/model/type/TypeKind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/type/TypeKind;"}]}, "classes/javax/annotation/processing/Filer.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/Filer", "super": "java/lang/Object", "mthds": [{"nme": "createSourceFile", "acc": 1153, "dsc": "(Ljava/lang/CharSequence;[Ljavax/lang/model/element/Element;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "createClassFile", "acc": 1153, "dsc": "(Ljava/lang/CharSequence;[Ljavax/lang/model/element/Element;)Ljavax/tools/JavaFileObject;", "exs": ["java/io/IOException"]}, {"nme": "createResource", "acc": 1153, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/CharSequence;Ljava/lang/CharSequence;[Ljavax/lang/model/element/Element;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}, {"nme": "getResource", "acc": 1025, "dsc": "(Ljavax/tools/JavaFileManager$Location;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljavax/tools/FileObject;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/javax/lang/model/type/MirroredTypeException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/type/MirroredTypeException", "super": "javax/lang/model/type/MirroredTypesException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeMirror;)V"}, {"nme": "getTypeMirror", "acc": 1, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "readObject", "acc": 2, "dsc": "(Ljava/io/ObjectInputStream;)V", "exs": ["java/io/IOException", "java/lang/ClassNotFoundException"]}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 130, "nme": "type", "dsc": "Ljavax/lang/model/type/TypeMirror;"}]}, "classes/javax/lang/model/util/SimpleTypeVisitor6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleTypeVisitor6", "super": "javax/lang/model/util/AbstractTypeVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "defaultAction", "acc": 4, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeMirror;TP;)TR;"}, {"nme": "visitPrimitive", "acc": 1, "dsc": "(Ljavax/lang/model/type/PrimitiveType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/PrimitiveType;TP;)TR;"}, {"nme": "visitNull", "acc": 1, "dsc": "(Ljavax/lang/model/type/NullType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NullType;TP;)TR;"}, {"nme": "visitArray", "acc": 1, "dsc": "(Ljavax/lang/model/type/ArrayType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ArrayType;TP;)TR;"}, {"nme": "visitDeclared", "acc": 1, "dsc": "(Ljavax/lang/model/type/DeclaredType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/DeclaredType;TP;)TR;"}, {"nme": "visitError", "acc": 1, "dsc": "(Ljavax/lang/model/type/ErrorType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ErrorType;TP;)TR;"}, {"nme": "visitTypeVariable", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeVariable;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/TypeVariable;TP;)TR;"}, {"nme": "visitWildcard", "acc": 1, "dsc": "(Ljavax/lang/model/type/WildcardType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/WildcardType;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1, "dsc": "(Ljavax/lang/model/type/ExecutableType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/ExecutableType;TP;)TR;"}, {"nme": "visitNoType", "acc": 1, "dsc": "(Ljavax/lang/model/type/NoType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/NoType;TP;)TR;"}], "flds": [{"acc": 20, "nme": "DEFAULT_VALUE", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TR;"}], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/element/UnknownDirectiveException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/element/UnknownDirectiveException", "super": "javax/lang/model/UnknownEntityException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/lang/model/element/ModuleElement$Directive;Ljava/lang/Object;)V"}, {"nme": "getUnknownDirective", "acc": 1, "dsc": "()Ljavax/lang/model/element/ModuleElement$Directive;"}, {"nme": "getArgument", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 146, "nme": "directive", "dsc": "Ljavax/lang/model/element/ModuleElement$Directive;"}, {"acc": 146, "nme": "parameter", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/javax/lang/model/util/TypeKindVisitor7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/TypeKindVisitor7", "super": "javax/lang/model/util/TypeKindVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitUnion", "acc": 1, "dsc": "(Ljavax/lang/model/type/UnionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/UnionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/util/ElementFilter.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "fieldsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/VariableElement;>;"}, {"nme": "fieldsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/VariableElement;>;"}, {"nme": "recordComponentsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/RecordComponentElement;>;"}, {"nme": "recordComponentsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/RecordComponentElement;>;"}, {"nme": "constructorsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/ExecutableElement;>;"}, {"nme": "constructorsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/ExecutableElement;>;"}, {"nme": "methodsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/ExecutableElement;>;"}, {"nme": "methodsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/ExecutableElement;>;"}, {"nme": "typesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/TypeElement;>;"}, {"nme": "typesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/TypeElement;>;"}, {"nme": "packagesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/PackageElement;>;"}, {"nme": "packagesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/PackageElement;>;"}, {"nme": "modulesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement;>;"}, {"nme": "modulesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;)Ljava/util/Set<Ljavax/lang/model/element/ModuleElement;>;"}, {"nme": "listFilter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/Class;)Ljava/util/List;", "sig": "<E::Ljavax/lang/model/element/Element;>(Ljava/lang/Iterable<+Ljavax/lang/model/element/Element;>;Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;Ljava/lang/Class<TE;>;)Ljava/util/List<TE;>;"}, {"nme": "setFilter", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/Class;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "<E::Ljavax/lang/model/element/Element;>(Ljava/util/Set<+Ljavax/lang/model/element/Element;>;Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;Ljava/lang/Class<TE;>;)Ljava/util/Set<TE;>;"}, {"nme": "exportsIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement$ExportsDirective;>;"}, {"nme": "opensIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement$OpensDirective;>;"}, {"nme": "providesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement$ProvidesDirective;>;"}, {"nme": "requiresIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement$RequiresDirective;>;"}, {"nme": "usesIn", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)<PERSON>ja<PERSON>/util/List;", "sig": "(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;)Ljava/util/List<Ljavax/lang/model/element/ModuleElement$UsesDirective;>;"}, {"nme": "listFilter", "acc": 10, "dsc": "(Ljava/lang/Iterable;Ljavax/lang/model/element/ModuleElement$DirectiveKind;Ljava/lang/Class;)Ljava/util/List;", "sig": "<D::Ljavax/lang/model/element/ModuleElement$Directive;>(Ljava/lang/Iterable<+Ljavax/lang/model/element/ModuleElement$Directive;>;Ljavax/lang/model/element/ModuleElement$DirectiveKind;Ljava/lang/Class<TD;>;)Ljava/util/List<TD;>;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONSTRUCTOR_KIND", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "FIELD_KINDS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "METHOD_KIND", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "PACKAGE_KIND", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "MODULE_KIND", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "TYPE_KINDS", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}, {"acc": 26, "nme": "RECORD_COMPONENT_KIND", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljavax/lang/model/element/ElementKind;>;"}]}, "classes/javax/lang/model/util/ElementKindVisitor6.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/ElementKindVisitor6", "super": "javax/lang/model/util/SimpleElementVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "visitPackage", "acc": 1, "dsc": "(Ljavax/lang/model/element/PackageElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/PackageElement;TP;)TR;"}, {"nme": "visitType", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitTypeAsAnnotationType", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitTypeAsClass", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitTypeAsEnum", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitTypeAsInterface", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitTypeAsRecord", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeElement;TP;)TR;"}, {"nme": "visitVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsEnumConstant", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsExceptionParameter", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsField", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsLocalVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsParameter", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsResourceVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitVariableAsBindingVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}, {"nme": "visitExecutable", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitExecutableAsConstructor", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitExecutableAsInstanceInit", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitExecutableAsMethod", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitExecutableAsStaticInit", "acc": 1, "dsc": "(Ljavax/lang/model/element/ExecutableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/ExecutableElement;TP;)TR;"}, {"nme": "visitTypeParameter", "acc": 1, "dsc": "(Ljavax/lang/model/element/TypeParameterElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/TypeParameterElement;TP;)TR;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_6"]]}]}, "classes/javax/lang/model/type/WildcardType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/WildcardType", "super": "java/lang/Object", "mthds": [{"nme": "getExtendsBound", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getSuperBound", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}], "flds": []}, "classes/javax/lang/model/element/Modifier$1.class": {"ver": 65, "acc": 16432, "nme": "javax/lang/model/element/Modifier$1", "super": "javax/lang/model/element/Modifier", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/javax/tools/JavaFileManager$Location.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/JavaFileManager$Location", "super": "java/lang/Object", "mthds": [{"nme": "getName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isOutputLocation", "acc": 1025, "dsc": "()Z"}, {"nme": "isModuleOrientedLocation", "acc": 1, "dsc": "()Z"}], "flds": []}, "classes/javax/tools/StandardLocation.class": {"ver": 65, "acc": 16433, "nme": "javax/tools/StandardLocation", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/tools/StandardLocation;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/tools/StandardLocation;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "locationFor", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/tools/JavaFileManager$Location;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "isOutputLocation", "acc": 1, "dsc": "()Z"}, {"nme": "isModuleOrientedLocation", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/tools/StandardLocation;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CLASS_OUTPUT", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "SOURCE_OUTPUT", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "CLASS_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "SOURCE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "ANNOTATION_PROCESSOR_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "ANNOTATION_PROCESSOR_MODULE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "PLATFORM_CLASS_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "NATIVE_HEADER_OUTPUT", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "MODULE_SOURCE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "UPGRADE_MODULE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "SYSTEM_MODULES", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "MODULE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 16409, "nme": "PATCH_MODULE_PATH", "dsc": "Ljavax/tools/StandardLocation;"}, {"acc": 26, "nme": "locations", "dsc": "Ljava/util/concurrent/ConcurrentMap;", "sig": "Ljava/util/concurrent/ConcurrentMap<Ljava/lang/String;Ljavax/tools/JavaFileManager$Location;>;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/tools/StandardLocation;"}]}, "classes/javax/annotation/processing/RoundEnvironment.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/RoundEnvironment", "super": "java/lang/Object", "mthds": [{"nme": "processingOver", "acc": 1025, "dsc": "()Z"}, {"nme": "errorRaised", "acc": 1025, "dsc": "()Z"}, {"nme": "getRootElements", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getElementsAnnotatedWith", "acc": 1025, "dsc": "(Ljavax/lang/model/element/TypeElement;)Ljava/util/Set;", "sig": "(Ljavax/lang/model/element/TypeElement;)Ljava/util/Set<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getElementsAnnotatedWithAny", "acc": 129, "dsc": "([Ljavax/lang/model/element/TypeElement;)Ljava/util/Set;", "sig": "([Ljavax/lang/model/element/TypeElement;)Ljava/util/Set<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getElementsAnnotatedWith", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)<PERSON>java/util/Set;", "sig": "(Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;)Ljava/util/Set<+Ljavax/lang/model/element/Element;>;"}, {"nme": "getElementsAnnotatedWithAny", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/Set<Ljava/lang/Class<+Ljava/lang/annotation/Annotation;>;>;)Ljava/util/Set<+Ljavax/lang/model/element/Element;>;"}], "flds": []}, "classes/javax/lang/model/util/SimpleElementVisitor7.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleElementVisitor7", "super": "javax/lang/model/util/SimpleElementVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "<init>", "acc": 131076, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitVariable", "acc": 1, "dsc": "(Ljavax/lang/model/element/VariableElement;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/element/VariableElement;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/type/UnionType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/UnionType", "super": "java/lang/Object", "mthds": [{"nme": "getAlternatives", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/type/TypeMirror;>;"}], "flds": []}, "classes/javax/tools/ToolProvider.class": {"ver": 65, "acc": 33, "nme": "javax/tools/ToolProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getSystemJavaCompiler", "acc": 9, "dsc": "()Ljavax/tools/JavaCompiler;"}, {"nme": "getSystemDocumentationTool", "acc": 9, "dsc": "()Ljavax/tools/DocumentationTool;"}, {"nme": "getSystemToolClassLoader", "acc": 131081, "dsc": "()<PERSON><PERSON><PERSON>/lang/ClassLoader;", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "9"]}]}, {"nme": "getSystemTool", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;Ljava/lang/String;Ljava/lang/String;)TT;"}, {"nme": "matches", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)Z", "sig": "<T:Ljava/lang/Object;>(TT;Ljava/lang/String;)Z"}, {"nme": "lambda$matches$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Bo<PERSON>an;"}], "flds": [{"acc": 26, "nme": "systemJavaCompilerModule", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.compiler"}, {"acc": 26, "nme": "systemJavaCompilerName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.tools.javac.api.JavacTool"}, {"acc": 26, "nme": "systemDocumentationToolModule", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.javadoc"}, {"acc": 26, "nme": "systemDocumentationToolName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.javadoc.internal.api.JavadocTool"}]}, "classes/javax/lang/model/SourceVersion.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/SourceVersion", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/SourceVersion;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/lang/model/SourceVersion;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "latest", "acc": 9, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "getLatestSupported", "acc": 10, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "latestSupported", "acc": 9, "dsc": "()Ljavax/lang/model/SourceVersion;"}, {"nme": "isIdentifier", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "isName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "isName", "acc": 9, "dsc": "(Ljava/lang/CharSequence;Ljavax/lang/model/SourceVersion;)Z"}, {"nme": "isKeyword", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)Z"}, {"nme": "isKeyword", "acc": 9, "dsc": "(Ljava/lang/CharSequence;Ljavax/lang/model/SourceVersion;)Z"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/Runtime$Version;)Ljavax/lang/model/SourceVersion;"}, {"nme": "runtimeVersion", "acc": 1, "dsc": "()Ljava/lang/Runtime$Version;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/SourceVersion;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "RELEASE_0", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_1", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_2", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_3", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_4", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_5", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_6", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_7", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_8", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_9", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_10", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_11", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_12", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_13", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_14", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_15", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_16", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_17", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_18", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_19", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_20", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 16409, "nme": "RELEASE_21", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 26, "nme": "latestSupported", "dsc": "Ljavax/lang/model/SourceVersion;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/SourceVersion;"}]}, "classes/javax/lang/model/util/ElementKindVisitor6$1.class": {"ver": 65, "acc": 4128, "nme": "javax/lang/model/util/ElementKindVisitor6$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$javax$lang$model$element$ElementKind", "dsc": "[I"}]}, "classes/javax/annotation/processing/Messager.class": {"ver": 65, "acc": 1537, "nme": "javax/annotation/processing/Messager", "super": "java/lang/Object", "mthds": [{"nme": "printMessage", "acc": 1025, "dsc": "(Ljavax/tools/Diagnostic$Kind;Ljava/lang/CharSequence;)V"}, {"nme": "printMessage", "acc": 1025, "dsc": "(Ljavax/tools/Diagnostic$Kind;Ljava/lang/CharSequence;Ljavax/lang/model/element/Element;)V"}, {"nme": "printMessage", "acc": 1025, "dsc": "(Ljavax/tools/Diagnostic$Kind;Ljava/lang/CharSequence;Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;)V"}, {"nme": "printMessage", "acc": 1025, "dsc": "(Ljavax/tools/Diagnostic$Kind;Ljava/lang/CharSequence;Ljavax/lang/model/element/Element;Ljavax/lang/model/element/AnnotationMirror;Ljavax/lang/model/element/AnnotationValue;)V"}, {"nme": "printError", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "printError", "acc": 1, "dsc": "(Lja<PERSON>/lang/CharSequence;Ljavax/lang/model/element/Element;)V"}, {"nme": "printWarning", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "printWarning", "acc": 1, "dsc": "(Lja<PERSON>/lang/CharSequence;Ljavax/lang/model/element/Element;)V"}, {"nme": "printNote", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/CharSequence;)V"}, {"nme": "printNote", "acc": 1, "dsc": "(Lja<PERSON>/lang/CharSequence;Ljavax/lang/model/element/Element;)V"}], "flds": []}, "classes/javax/lang/model/type/UnknownTypeException.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/type/UnknownTypeException", "super": "javax/lang/model/UnknownEntityException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/lang/model/type/TypeMirror;Ljava/lang/Object;)V"}, {"nme": "getUnknownType", "acc": 1, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "getArgument", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 269}, {"acc": 130, "nme": "type", "dsc": "Ljavax/lang/model/type/TypeMirror;"}, {"acc": 130, "nme": "parameter", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}]}, "classes/javax/lang/model/type/ArrayType.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/type/ArrayType", "super": "java/lang/Object", "mthds": [{"nme": "getComponentType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}], "flds": []}, "classes/javax/tools/DiagnosticCollector.class": {"ver": 65, "acc": 49, "nme": "javax/tools/DiagnosticCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "report", "acc": 1, "dsc": "(Ljavax/tools/Diagnostic;)V", "sig": "(Ljavax/tools/Diagnostic<+TS;>;)V"}, {"nme": "getDiagnostics", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljavax/tools/Diagnostic<+TS;>;>;"}], "flds": [{"acc": 2, "nme": "diagnostics", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljavax/tools/Diagnostic<+TS;>;>;"}]}, "classes/javax/tools/JavaCompiler$CompilationTask.class": {"ver": 65, "acc": 1537, "nme": "javax/tools/JavaCompiler$CompilationTask", "super": "java/lang/Object", "mthds": [{"nme": "addModules", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(L<PERSON><PERSON>/lang/Iterable<Ljava/lang/String;>;)V"}, {"nme": "setProcessors", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Iterable;)V", "sig": "(Ljava/lang/Iterable<+Ljavax/annotation/processing/Processor;>;)V"}, {"nme": "setLocale", "acc": 1025, "dsc": "(Ljava/util/Locale;)V"}, {"nme": "call", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON>an;"}, {"nme": "call", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "exs": ["java/lang/Exception"]}], "flds": []}, "classes/javax/lang/model/element/Element.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/Element", "super": "java/lang/Object", "mthds": [{"nme": "asType", "acc": 1025, "dsc": "()Ljavax/lang/model/type/TypeMirror;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljavax/lang/model/element/ElementKind;"}, {"nme": "getModifiers", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljavax/lang/model/element/Modifier;>;"}, {"nme": "getSimpleName", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Name;"}, {"nme": "getEnclosingElement", "acc": 1025, "dsc": "()Ljavax/lang/model/element/Element;"}, {"nme": "getEnclosedElements", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/Element;>;"}, {"nme": "equals", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1025, "dsc": "()I"}, {"nme": "getAnnotationMirrors", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/AnnotationMirror;>;"}, {"nme": "getAnnotation", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)Ljava/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)TA;"}, {"nme": "getAnnotationsByType", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;)[Lja<PERSON>/lang/annotation/Annotation;", "sig": "<A::Ljava/lang/annotation/Annotation;>(Ljava/lang/Class<TA;>;)[TA;"}, {"nme": "accept", "acc": 1025, "dsc": "(Ljavax/lang/model/element/ElementVisitor;Lja<PERSON>/lang/Object;)Ljava/lang/Object;", "sig": "<R:Ljava/lang/Object;P:Ljava/lang/Object;>(Ljavax/lang/model/element/ElementVisitor<TR;TP;>;TP;)TR;"}], "flds": []}, "classes/javax/lang/model/util/AbstractTypeVisitor7.class": {"ver": 65, "acc": 1057, "nme": "javax/lang/model/util/AbstractTypeVisitor7", "super": "javax/lang/model/util/AbstractTypeVisitor6", "mthds": [{"nme": "<init>", "acc": 131076, "dsc": "()V", "vanns": [{"dsc": "<PERSON><PERSON><PERSON>/lang/Deprecated;", "vals": ["since", "12"]}]}, {"nme": "visitUnion", "acc": 1025, "dsc": "(Ljavax/lang/model/type/UnionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/UnionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_7"]]}]}, "classes/javax/lang/model/util/SimpleAnnotationValueVisitor9.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleAnnotationValueVisitor9", "super": "javax/lang/model/util/SimpleAnnotationValueVisitor8", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_14"]]}]}, "classes/javax/lang/model/element/ModuleElement$OpensDirective.class": {"ver": 65, "acc": 1537, "nme": "javax/lang/model/element/ModuleElement$OpensDirective", "super": "java/lang/Object", "mthds": [{"nme": "getPackage", "acc": 1025, "dsc": "()Ljavax/lang/model/element/PackageElement;"}, {"nme": "getTargetModules", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<+Ljavax/lang/model/element/ModuleElement;>;"}], "flds": []}, "classes/javax/lang/model/element/NestingKind.class": {"ver": 65, "acc": 16433, "nme": "javax/lang/model/element/NestingKind", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljavax/lang/model/element/NestingKind;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljavax/lang/model/element/NestingKind;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "isNested", "acc": 1, "dsc": "()Z"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljavax/lang/model/element/NestingKind;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "TOP_LEVEL", "dsc": "Ljavax/lang/model/element/NestingKind;"}, {"acc": 16409, "nme": "MEMBER", "dsc": "Ljavax/lang/model/element/NestingKind;"}, {"acc": 16409, "nme": "LOCAL", "dsc": "Ljavax/lang/model/element/NestingKind;"}, {"acc": 16409, "nme": "ANONYMOUS", "dsc": "Ljavax/lang/model/element/NestingKind;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljavax/lang/model/element/NestingKind;"}]}, "classes/javax/lang/model/util/SimpleTypeVisitor14.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/SimpleTypeVisitor14", "super": "javax/lang/model/util/SimpleTypeVisitor9", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_21"]]}]}, "classes/javax/lang/model/util/TypeKindVisitor8.class": {"ver": 65, "acc": 33, "nme": "javax/lang/model/util/TypeKindVisitor8", "super": "javax/lang/model/util/TypeKindVisitor7", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(TR;)V"}, {"nme": "visitIntersection", "acc": 1, "dsc": "(Ljavax/lang/model/type/IntersectionType;Ljava/lang/Object;)Ljava/lang/Object;", "sig": "(Ljavax/lang/model/type/IntersectionType;TP;)TR;"}], "flds": [], "vanns": [{"dsc": "Ljavax/annotation/processing/SupportedSourceVersion;", "vals": ["value", ["Ljavax/lang/model/SourceVersion;", "RELEASE_8"]]}]}}}}