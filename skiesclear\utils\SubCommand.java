/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.brigadier.tree.LiteralCommandNode
 *  kotlin.Metadata
 *  net.minecraft.class_2168
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.utils;

import com.mojang.brigadier.tree.LiteralCommandNode;
import kotlin.Metadata;
import net.minecraft.class_2168;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0015\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0002H&\u00a2\u0006\u0004\b\u0004\u0010\u0005\u00a8\u0006\u0006"}, d2={"Lcom/pokeskies/skiesclear/utils/SubCommand;", "", "Lcom/mojang/brigadier/tree/LiteralCommandNode;", "Lnet/minecraft/class_2168;", "build", "()Lcom/mojang/brigadier/tree/LiteralCommandNode;", "SkiesClear"})
public interface SubCommand {
    @NotNull
    public LiteralCommandNode<class_2168> build();
}

