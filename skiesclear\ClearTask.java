/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.jvm.internal.SourceDebugExtension
 *  kotlin.text.Regex
 *  net.kyori.adventure.text.ComponentLike
 *  net.minecraft.class_1297
 *  net.minecraft.class_1297$class_5529
 *  net.minecraft.class_1308
 *  net.minecraft.class_1657
 *  net.minecraft.class_2960
 *  net.minecraft.class_3218
 *  net.minecraft.class_3222
 *  net.minecraft.class_3414
 *  net.minecraft.class_3419
 *  net.minecraft.server.MinecraftServer
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear;

import com.pokeskies.skiesclear.config.ClearConfig;
import com.pokeskies.skiesclear.config.clearables.CobblemonClearable;
import com.pokeskies.skiesclear.config.clearables.EntityClearable;
import com.pokeskies.skiesclear.config.clearables.ItemClearable;
import com.pokeskies.skiesclear.utils.Utils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.Regex;
import net.kyori.adventure.text.ComponentLike;
import net.minecraft.class_1297;
import net.minecraft.class_1308;
import net.minecraft.class_1657;
import net.minecraft.class_2960;
import net.minecraft.class_3218;
import net.minecraft.class_3222;
import net.minecraft.class_3414;
import net.minecraft.class_3419;
import net.minecraft.server.MinecraftServer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001d\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\b\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0015\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u001d\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\b\u0016\u0010\u0017J\r\u0010\u0018\u001a\u00020\n\u00a2\u0006\u0004\b\u0018\u0010\u0019J\r\u0010\u001a\u001a\u00020\r\u00a2\u0006\u0004\b\u001a\u0010\u001bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\u0003\u0010\u001cR\u001e\u0010\u001d\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u001d\u0010\u001eR\u0016\u0010\u001f\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b\u001f\u0010 \u00a8\u0006!"}, d2={"Lcom/pokeskies/skiesclear/ClearTask;", "", "Lcom/pokeskies/skiesclear/config/ClearConfig;", "clearConfig", "<init>", "(Lcom/pokeskies/skiesclear/config/ClearConfig;)V", "Lnet/minecraft/server/MinecraftServer;", "server", "", "broadcast", "", "runClear", "(Lnet/minecraft/server/MinecraftServer;Z)I", "", "tick", "(Lnet/minecraft/server/MinecraftServer;)V", "", "Lnet/minecraft/class_3218;", "getDimensions", "(Lnet/minecraft/server/MinecraftServer;)Ljava/util/List;", "Lnet/minecraft/class_3222;", "player", "shouldInform", "(Lnet/minecraft/class_3222;)Z", "getTimer", "()I", "resetTimer", "()V", "Lcom/pokeskies/skiesclear/config/ClearConfig;", "dimensions", "Ljava/util/List;", "timer", "I", "SkiesClear"})
@SourceDebugExtension(value={"SMAP\nClearTask.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ClearTask.kt\ncom/pokeskies/skiesclear/ClearTask\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,153:1\n774#2:154\n865#2,2:155\n774#2:157\n865#2,2:158\n774#2:160\n865#2,2:161\n*S KotlinDebug\n*F\n+ 1 ClearTask.kt\ncom/pokeskies/skiesclear/ClearTask\n*L\n69#1:154\n69#1:155,2\n97#1:157\n97#1:158,2\n112#1:160\n112#1:161,2\n*E\n"})
public final class ClearTask {
    @NotNull
    private final ClearConfig clearConfig;
    @Nullable
    private List<? extends class_3218> dimensions;
    private int timer;

    public ClearTask(@NotNull ClearConfig clearConfig) {
        Intrinsics.checkNotNullParameter((Object)clearConfig, (String)"clearConfig");
        this.clearConfig = clearConfig;
        this.timer = this.clearConfig.getInterval();
    }

    /*
     * WARNING - void declaration
     */
    public final int runClear(@NotNull MinecraftServer server, boolean broadcast) {
        Intrinsics.checkNotNullParameter((Object)server, (String)"server");
        AtomicInteger itemsCleared = new AtomicInteger();
        AtomicInteger pokemonCleared = new AtomicInteger();
        AtomicInteger entitiesCleared = new AtomicInteger();
        for (class_3218 dimension : this.getDimensions(server)) {
            List removeList = new ArrayList();
            try {
                for (Object entity : dimension.method_27909()) {
                    if (entity instanceof class_1308 && ((class_1308)entity).method_5947() && !this.clearConfig.getClearPersistent() || entity instanceof class_1308 && ((class_1308)entity).method_16914() && !this.clearConfig.getClearNamed() || entity instanceof class_1657 || this.clearConfig.getClearables() == null) continue;
                    if (this.clearConfig.getClearables().getItems() != null && this.clearConfig.getClearables().getItems().getEnabled()) {
                        ItemClearable itemClearable = this.clearConfig.getClearables().getItems();
                        Intrinsics.checkNotNull((Object)entity);
                        if (itemClearable.isEntityType((class_1297)entity)) {
                            if (!this.clearConfig.getClearables().getItems().shouldClear((class_1297)entity)) continue;
                            removeList.add(entity);
                            itemsCleared.getAndIncrement();
                            continue;
                        }
                    }
                    if (this.clearConfig.getClearables().getCobblemon() != null && this.clearConfig.getClearables().getCobblemon().getEnabled()) {
                        CobblemonClearable cobblemonClearable = this.clearConfig.getClearables().getCobblemon();
                        Intrinsics.checkNotNull((Object)entity);
                        if (cobblemonClearable.isEntityType((class_1297)entity)) {
                            if (!this.clearConfig.getClearables().getCobblemon().shouldClear((class_1297)entity)) continue;
                            removeList.add(entity);
                            pokemonCleared.getAndIncrement();
                            continue;
                        }
                    }
                    if (this.clearConfig.getClearables().getEntities() == null || !this.clearConfig.getClearables().getEntities().getEnabled()) continue;
                    EntityClearable entityClearable = this.clearConfig.getClearables().getEntities();
                    Intrinsics.checkNotNull((Object)entity);
                    if (!entityClearable.isEntityType((class_1297)entity) || !this.clearConfig.getClearables().getEntities().shouldClear((class_1297)entity)) continue;
                    removeList.add(entity);
                    entitiesCleared.getAndIncrement();
                }
                for (Object entity : removeList) {
                    entity.method_5650(class_1297.class_5529.field_26998);
                }
            }
            catch (Exception exception) {
                Utils.INSTANCE.printError("An exception was thrown while attempting to clear entities: + " + exception);
                exception.printStackTrace();
            }
        }
        int total = itemsCleared.get() + pokemonCleared.get() + entitiesCleared.get();
        if (broadcast && (!((Collection)this.clearConfig.getMessages().getClear()).isEmpty() || this.clearConfig.getSounds().getClear() != null)) {
            void $this$filterTo$iv$iv;
            Object entity;
            List list = server.method_3760().method_14571();
            Intrinsics.checkNotNullExpressionValue((Object)list, (String)"getPlayers(...)");
            Iterable $this$filter$iv = list;
            boolean $i$f$filter = false;
            entity = $this$filter$iv;
            Collection destination$iv$iv = new ArrayList();
            boolean $i$f$filterTo = false;
            for (Object element$iv$iv : $this$filterTo$iv$iv) {
                class_3222 it = (class_3222)element$iv$iv;
                boolean bl = false;
                Intrinsics.checkNotNull((Object)it);
                if (!this.shouldInform(it)) continue;
                destination$iv$iv.add(element$iv$iv);
            }
            for (class_3222 player : (List)destination$iv$iv) {
                for (String line : this.clearConfig.getMessages().getClear()) {
                    CharSequence charSequence = line;
                    Regex regex = new Regex("%clear_time%");
                    Object object = Utils.INSTANCE.getFormattedTime(this.clearConfig.getInterval());
                    charSequence = regex.replace(charSequence, (String)object);
                    regex = new Regex("%clear_amount%");
                    object = String.valueOf(total);
                    charSequence = regex.replace(charSequence, (String)object);
                    regex = new Regex("%clear_amount_items%");
                    String string = itemsCleared.toString();
                    Intrinsics.checkNotNullExpressionValue((Object)string, (String)"toString(...)");
                    object = string;
                    charSequence = regex.replace(charSequence, (String)object);
                    regex = new Regex("%clear_amount_pokemon%");
                    String string2 = pokemonCleared.toString();
                    Intrinsics.checkNotNullExpressionValue((Object)string2, (String)"toString(...)");
                    object = string2;
                    charSequence = regex.replace(charSequence, (String)object);
                    regex = new Regex("%clear_amount_entities%");
                    String string3 = entitiesCleared.toString();
                    Intrinsics.checkNotNullExpressionValue((Object)string3, (String)"toString(...)");
                    object = string3;
                    player.sendMessage((ComponentLike)Utils.INSTANCE.deserializeText(regex.replace(charSequence, (String)object)));
                }
                if (this.clearConfig.getSounds().getClear() == null || !(((CharSequence)this.clearConfig.getSounds().getClear().getSound()).length() > 0)) continue;
                player.method_17356(class_3414.method_47908((class_2960)class_2960.method_60654((String)this.clearConfig.getSounds().getClear().getSound())), class_3419.field_15250, this.clearConfig.getSounds().getClear().getVolume(), this.clearConfig.getSounds().getClear().getPitch());
            }
        }
        return total;
    }

    /*
     * WARNING - void declaration
     */
    public final void tick(@NotNull MinecraftServer server) {
        ClearConfig.Sounds.SoundSettings warningSound;
        Object destination$iv$iv;
        Intrinsics.checkNotNullParameter((Object)server, (String)"server");
        List<String> warningMessage = this.clearConfig.getMessages().getWarnings().get(String.valueOf(this.timer));
        if (warningMessage != null) {
            void $this$filterTo$iv$iv;
            List list = server.method_3760().method_14571();
            Intrinsics.checkNotNullExpressionValue((Object)list, (String)"getPlayers(...)");
            Iterable $this$filter$iv = list;
            boolean $i$f$filter = false;
            Iterable iterable = $this$filter$iv;
            destination$iv$iv = new ArrayList();
            boolean $i$f$filterTo2 = false;
            for (Object element$iv$iv : $this$filterTo$iv$iv) {
                class_3222 it = (class_3222)element$iv$iv;
                boolean bl = false;
                Intrinsics.checkNotNull((Object)it);
                if (!this.shouldInform(it)) continue;
                destination$iv$iv.add(element$iv$iv);
            }
            for (class_3222 player : (List)destination$iv$iv) {
                for (String line : warningMessage) {
                    destination$iv$iv = line;
                    Regex $i$f$filterTo2 = new Regex("%time_remaining%");
                    String string = Utils.INSTANCE.getFormattedTime(this.timer);
                    player.sendMessage((ComponentLike)Utils.INSTANCE.deserializeText($i$f$filterTo2.replace((CharSequence)destination$iv$iv, string)));
                }
            }
        }
        if ((warningSound = this.clearConfig.getSounds().getWarnings().get(String.valueOf(this.timer))) != null && ((CharSequence)warningSound.getSound()).length() > 0) {
            void $this$filterTo$iv$iv;
            List list = server.method_3760().method_14571();
            Intrinsics.checkNotNullExpressionValue((Object)list, (String)"getPlayers(...)");
            Iterable $this$filter$iv = list;
            boolean $i$f$filter = false;
            destination$iv$iv = $this$filter$iv;
            Collection destination$iv$iv2 = new ArrayList();
            boolean $i$f$filterTo = false;
            for (Object element$iv$iv : $this$filterTo$iv$iv) {
                class_3222 it = (class_3222)element$iv$iv;
                boolean bl = false;
                Intrinsics.checkNotNull((Object)it);
                if (!this.shouldInform(it)) continue;
                destination$iv$iv2.add(element$iv$iv);
            }
            for (class_3222 player : (List)destination$iv$iv2) {
                player.method_17356(class_3414.method_47908((class_2960)class_2960.method_60654((String)warningSound.getSound())), class_3419.field_15250, warningSound.getVolume(), warningSound.getPitch());
            }
        }
        int n = this.timer;
        this.timer = n + -1;
        if (n <= 0) {
            this.resetTimer();
            this.runClear(server, true);
        }
    }

    private final List<class_3218> getDimensions(MinecraftServer server) {
        if (this.dimensions != null) {
            List<class_3218> list = this.dimensions;
            Intrinsics.checkNotNull(list);
            return list;
        }
        List newDimensions = new ArrayList();
        for (class_3218 level : server.method_3738()) {
            if (!this.clearConfig.getDimensions().contains(level.method_27983().method_29177().toString())) continue;
            Intrinsics.checkNotNull((Object)level);
            newDimensions.add(level);
        }
        if (newDimensions.isEmpty()) {
            Iterable iterable = server.method_3738();
            Intrinsics.checkNotNullExpressionValue((Object)iterable, (String)"getAllLevels(...)");
            newDimensions = CollectionsKt.toMutableList((Iterable)iterable);
        }
        this.dimensions = newDimensions;
        return newDimensions;
    }

    private final boolean shouldInform(class_3222 player) {
        return !this.clearConfig.getInformDimensionsOnly() || this.clearConfig.getDimensions().isEmpty() || this.clearConfig.getDimensions().contains(player.method_37908().method_27983().method_29177().toString());
    }

    public final int getTimer() {
        return this.timer;
    }

    public final void resetTimer() {
        this.timer = this.clearConfig.getInterval();
    }
}

