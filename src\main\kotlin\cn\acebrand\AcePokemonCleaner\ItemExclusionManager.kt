package cn.acebrand.AcePokemonCleaner

import org.bukkit.configuration.file.FileConfiguration
import org.bukkit.configuration.file.YamlConfiguration
import org.bukkit.plugin.Plugin
import org.bukkit.inventory.ItemStack
import org.bukkit.Material
import java.io.File
import java.io.IOException
import java.util.logging.Level

/**
 * 掉落物排除管理器
 * 负责管理通过GUI添加的排除物品列表
 */
class ItemExclusionManager(private val plugin: Plugin) {
    
    private lateinit var configFile: File
    private lateinit var config: FileConfiguration
    
    // GUI排除的物品列表
    private val guiExcludedItems = mutableSetOf<String>()
    
    // 物品显示名称映射
    private val itemDisplayNames = mutableMapOf<String, String>()
    
    // GUI设置
    var guiEnabled: Boolean = true
    var guiTitle: String = "§6§l掉落物清理管理"
    var guiSize: Int = 6
    var itemsPerPage: Int = 45
    
    // 消息配置
    private val messages = mutableMapOf<String, String>()
    
    /**
     * 初始化管理器
     */
    fun initialize() {
        setupConfigFile()
        loadConfig()
    }
    
    /**
     * 设置配置文件
     */
    private fun setupConfigFile() {
        configFile = File(plugin.dataFolder, "excluded-items.yml")
        
        if (!configFile.exists()) {
            try {
                plugin.saveResource("excluded-items.yml", false)
            } catch (e: Exception) {
                plugin.logger.log(Level.WARNING, "无法创建excluded-items.yml文件", e)
                // 创建空配置文件
                configFile.parentFile.mkdirs()
                configFile.createNewFile()
            }
        }
        
        config = YamlConfiguration.loadConfiguration(configFile)
    }
    
    /**
     * 加载配置
     */
    fun loadConfig() {
        try {
            config = YamlConfiguration.loadConfiguration(configFile)
            
            // 加载GUI排除的物品列表
            guiExcludedItems.clear()
            val guiItems = config.getStringList("gui-excluded-items")
            guiExcludedItems.addAll(guiItems)
            
            // 加载物品显示名称映射
            itemDisplayNames.clear()
            val displayNamesSection = config.getConfigurationSection("item-display-names")
            displayNamesSection?.getKeys(false)?.forEach { key ->
                val displayName = displayNamesSection.getString(key)
                if (displayName != null) {
                    itemDisplayNames[key] = displayName
                }
            }
            
            // 加载GUI设置
            val guiSection = config.getConfigurationSection("gui-settings")
            if (guiSection != null) {
                guiEnabled = guiSection.getBoolean("enabled", true)
                guiTitle = guiSection.getString("title", "§6§l掉落物清理管理") ?: "§6§l掉落物清理管理"
                guiSize = guiSection.getInt("size", 6).coerceIn(1, 6)
                itemsPerPage = guiSection.getInt("items-per-page", 45)
            }
            
            // 加载消息配置
            messages.clear()
            val messagesSection = config.getConfigurationSection("messages")
            messagesSection?.getKeys(false)?.forEach { key ->
                val message = messagesSection.getString(key)
                if (message != null) {
                    messages[key] = message
                }
            }
            
            plugin.logger.info("掉落物排除配置加载完成，共 ${guiExcludedItems.size} 个排除物品")
            
        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "加载掉落物排除配置时发生错误", e)
        }
    }
    
    /**
     * 保存配置
     */
    fun saveConfig() {
        try {
            // 保存GUI排除的物品列表
            config.set("gui-excluded-items", guiExcludedItems.toList())
            
            // 保存物品显示名称映射
            val displayNamesSection = config.createSection("item-display-names")
            itemDisplayNames.forEach { (key, value) ->
                displayNamesSection.set(key, value)
            }
            
            config.save(configFile)
            plugin.logger.info("掉落物排除配置已保存")
            
        } catch (e: IOException) {
            plugin.logger.log(Level.SEVERE, "保存掉落物排除配置时发生错误", e)
        }
    }
    
    /**
     * 添加物品到排除列表
     */
    fun addExcludedItem(itemStack: ItemStack): Boolean {
        val itemId = getItemId(itemStack)
        val wasNew = !guiExcludedItems.contains(itemId)

        guiExcludedItems.add(itemId)

        // 添加或更新显示名称映射
        val displayName = getItemDisplayName(itemStack)
        if (displayName.isNotEmpty()) {
            itemDisplayNames[itemId] = displayName
        }

        // 自动保存到文件
        saveConfig()

        return wasNew
    }
    
    /**
     * 从排除列表移除物品
     */
    fun removeExcludedItem(itemStack: ItemStack): Boolean {
        val itemId = getItemId(itemStack)
        val removed = guiExcludedItems.remove(itemId)

        if (removed) {
            // 同时移除显示名称映射
            itemDisplayNames.remove(itemId)

            // 自动保存到文件
            saveConfig()
        }

        return removed
    }
    
    /**
     * 检查物品是否被GUI排除
     */
    fun isItemExcludedByGui(itemStack: ItemStack): Boolean {
        val itemId = getItemId(itemStack)
        return guiExcludedItems.contains(itemId)
    }
    
    /**
     * 检查物品是否被GUI排除（通过物品名称）
     */
    fun isItemExcludedByGui(itemName: String): Boolean {
        val result = guiExcludedItems.any { excluded ->
            isItemMatch(excluded, itemName)
        }

        if (result) {
        }

        return result
    }

    /**
     * 检查两个物品名称是否匹配
     * 支持不同格式的物品ID匹配，包括详细信息匹配
     */
    private fun isItemMatch(excluded: String, itemName: String): Boolean {
        // 完全匹配
        if (excluded.equals(itemName, ignoreCase = true)) {
            return true
        }

        // 解析详细信息
        val excludedDetails = parseItemDetails(excluded)
        val itemDetails = parseItemDetails(itemName)

        // 基础ID匹配
        if (!isBaseIdMatch(excludedDetails["base"] ?: "", itemDetails["base"] ?: "")) {
            return false
        }

        // 如果排除项只有基础ID，则匹配成功
        if (excludedDetails.size == 1) {
            return true
        }

        // 详细信息匹配
        return isDetailedMatch(excludedDetails, itemDetails)
    }

    /**
     * 解析物品详细信息
     */
    private fun parseItemDetails(itemInfo: String): Map<String, String> {
        val details = mutableMapOf<String, String>()
        val parts = itemInfo.split(";")

        if (parts.isNotEmpty()) {
            details["base"] = parts[0]
        }

        for (i in 1 until parts.size) {
            val part = parts[i]
            if (part.contains(":")) {
                val keyValue = part.split(":", limit = 2)
                if (keyValue.size == 2) {
                    details[keyValue[0]] = keyValue[1]
                }
            }
        }

        return details
    }

    /**
     * 检查基础ID是否匹配
     */
    private fun isBaseIdMatch(excluded: String, itemId: String): Boolean {
        // 完全匹配
        if (excluded.equals(itemId, ignoreCase = true)) {
            return true
        }

        // 包含匹配
        if (itemId.contains(excluded, ignoreCase = true) || excluded.contains(itemId, ignoreCase = true)) {
            return true
        }

        // 处理命名空间匹配
        val excludedParts = excluded.split(":")
        val itemParts = itemId.split(":")

        // 如果排除项没有命名空间，但物品有命名空间，比较物品名部分
        if (excludedParts.size == 1 && itemParts.size == 2) {
            return excludedParts[0].equals(itemParts[1], ignoreCase = true)
        }

        // 如果物品没有命名空间，但排除项有命名空间，比较物品名部分
        if (itemParts.size == 1 && excludedParts.size == 2) {
            return itemParts[0].equals(excludedParts[1], ignoreCase = true)
        }

        return false
    }

    /**
     * 检查详细信息是否匹配
     */
    private fun isDetailedMatch(excludedDetails: Map<String, String>, itemDetails: Map<String, String>): Boolean {
        // 检查排除项中的每个详细信息是否在物品中匹配
        for ((key, value) in excludedDetails) {
            if (key == "base") continue // 基础ID已经检查过了

            val itemValue = itemDetails[key]
            if (itemValue == null) {
                // 如果物品没有这个属性，但排除项有，则不匹配
                return false
            }

            // 检查值是否匹配
            if (!value.equals(itemValue, ignoreCase = true) &&
                !itemValue.contains(value, ignoreCase = true) &&
                !value.contains(itemValue, ignoreCase = true)) {
                return false
            }
        }

        return true
    }
    
    /**
     * 获取所有GUI排除的物品
     */
    fun getGuiExcludedItems(): Set<String> {
        return guiExcludedItems.toSet()
    }

    /**
     * 批量更新排除列表（用于GUI保存）
     */
    fun updateExcludedItems(newItems: Set<String>, newDisplayNames: Map<String, String>) {
        // 清空现有列表
        guiExcludedItems.clear()
        itemDisplayNames.clear()

        // 添加新列表
        guiExcludedItems.addAll(newItems)
        itemDisplayNames.putAll(newDisplayNames)

        // 保存到文件
        saveConfig()

    }

    /**
     * 通过物品ID移除物品
     */
    fun removeExcludedItemById(itemId: String): Boolean {
        val removed = guiExcludedItems.remove(itemId)

        if (removed) {
            // 同时移除显示名称映射
            itemDisplayNames.remove(itemId)

            // 自动保存到文件
            saveConfig()
        }

        return removed
    }
    
    /**
     * 获取物品ID（详细版本，包含NBT等信息）
     */
    private fun getItemId(itemStack: ItemStack): String {
        return try {
            val material = itemStack.type
            val key = material.key
            val baseId = "${key.namespace}:${key.key}"

            // 获取物品的详细信息
            val details = mutableListOf<String>()
            details.add(baseId)

            // 添加自定义名称
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                val displayName = itemStack.itemMeta?.displayName?.replace("§", "")?.replace(" ", "_")
                if (!displayName.isNullOrEmpty()) {
                    details.add("name:$displayName")
                }
            }

            // 添加Lore信息
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasLore() == true) {
                val lore = itemStack.itemMeta?.lore?.joinToString("|") { it.replace("§", "").replace(" ", "_") }
                if (!lore.isNullOrEmpty()) {
                    details.add("lore:$lore")
                }
            }

            // 添加附魔信息
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasEnchants() == true) {
                val enchants = itemStack.itemMeta?.enchants?.map { "${it.key.key}:${it.value}" }?.joinToString("|")
                if (!enchants.isNullOrEmpty()) {
                    details.add("enchants:$enchants")
                }
            }

            // 尝试获取NBT数据的简化版本
            try {
                val nbtInfo = getSimpleNBTInfo(itemStack)
                if (nbtInfo.isNotEmpty()) {
                    details.add("nbt:$nbtInfo")
                }
            } catch (e: Exception) {
                // NBT获取失败，忽略
            }

            return details.joinToString(";")
        } catch (e: Exception) {
            // 如果失败，使用简单的材料名称
            itemStack.type.name.lowercase()
        }
    }

    /**
     * 获取简化的NBT信息
     */
    private fun getSimpleNBTInfo(itemStack: ItemStack): String {
        return try {
            // 检查是否有特殊的NBT标记
            val meta = itemStack.itemMeta
            if (meta != null) {
                val nbtInfo = mutableListOf<String>()

                // 检查是否有自定义模型数据
                if (meta.hasCustomModelData()) {
                    nbtInfo.add("cmd:${meta.customModelData}")
                }

                // 检查是否有属性修饰符
                if (meta.hasAttributeModifiers()) {
                    nbtInfo.add("attr:true")
                }

                // 检查是否不可破坏
                if (meta.isUnbreakable) {
                    nbtInfo.add("unbreakable:true")
                }

                return nbtInfo.joinToString(",")
            }

            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 获取物品显示名称
     */
    private fun getItemDisplayName(itemStack: ItemStack): String {
        return try {
            // 优先使用自定义名称
            if (itemStack.hasItemMeta() && itemStack.itemMeta?.hasDisplayName() == true) {
                itemStack.itemMeta?.displayName ?: ""
            } else {
                // 使用材料名称
                itemStack.type.name.lowercase().replace("_", " ")
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 获取物品的显示名称（用于GUI）
     */
    fun getItemDisplayName(itemId: String): String {
        return itemDisplayNames[itemId] ?: itemId
    }
    
    /**
     * 获取消息
     */
    fun getMessage(key: String, vararg replacements: Pair<String, String>): String {
        var message = messages[key] ?: "§c消息未找到: $key"
        
        replacements.forEach { (placeholder, value) ->
            message = message.replace("{$placeholder}", value)
        }
        
        return message
    }
    
    /**
     * 创建物品堆栈（用于GUI显示）
     */
    fun createItemStack(itemId: String): ItemStack? {
        return try {
            // 尝试解析物品ID
            val parts = itemId.split(":")
            if (parts.size >= 2) {
                // 有命名空间的物品ID
                val materialName = parts[1].uppercase()
                val material = Material.getMaterial(materialName)
                if (material != null) {
                    return ItemStack(material)
                }
            }
            
            // 尝试直接解析为材料
            val material = Material.getMaterial(itemId.uppercase())
            if (material != null) {
                return ItemStack(material)
            }
            
            // 如果都失败了，返回默认物品
            ItemStack(Material.BARRIER)
        } catch (e: Exception) {
            ItemStack(Material.BARRIER)
        }
    }
}
