{"md5": "b773405507741e1d9628db059ffc09f1", "sha2": "169aa74e9cc6bdd695d4dce233c3c6a0c89aff00", "sha256": "bb0b0159b62bbc4e2612970d7e61aaf77fe5b1ee4db526e6ffeaece0114e6fba", "contents": {"classes": {"classes/sun/management/jdp/JdpController.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpController", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getInteger", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)I", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "getInetAddress", "acc": 10, "dsc": "(Lja<PERSON>/lang/String;Ljava/net/InetAddress;Ljava/lang/String;)Ljava/net/InetAddress;", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "getProcessId", "acc": 10, "dsc": "()<PERSON><PERSON><PERSON>/lang/Long;"}, {"nme": "startDiscoveryService", "acc": 41, "dsc": "(Ljava/net/InetAddress;ILjava/lang/String;Ljava/lang/String;)V", "exs": ["java/io/IOException", "sun/management/jdp/JdpException"]}, {"nme": "stopDiscoveryService", "acc": 41, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "controller", "dsc": "Lsun/management/jdp/JdpController$JDPControllerRunner;"}]}, "classes/sun/management/jdp/JdpController$JDPControllerRunner.class": {"ver": 65, "acc": 32, "nme": "sun/management/jdp/JdpController$JDPControllerRunner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lsun/management/jdp/JdpBroadcaster;Lsun/management/jdp/JdpJmxPacket;I)V"}, {"nme": "run", "acc": 1, "dsc": "()V"}, {"nme": "stop", "acc": 1, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "packet", "dsc": "Lsun/management/jdp/JdpJmxPacket;"}, {"acc": 18, "nme": "bcast", "dsc": "Lsun/management/jdp/JdpBroadcaster;"}, {"acc": 18, "nme": "pause", "dsc": "I"}, {"acc": 66, "nme": "shutdown", "dsc": "Z"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$SslServerSocket.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$SslServerSocket", "super": "java/net/ServerSocket", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(ILjavax/net/ssl/SSLContext;[Ljava/lang/String;[Ljava/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(IILjava/net/InetAddress;Ljavax/net/ssl/SSLContext;[Ljava/lang/String;[Ljava/lang/String;Z)V", "exs": ["java/io/IOException"]}, {"nme": "accept", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["java/io/IOException"]}, {"nme": "getDefaultSSLSocketFactory", "acc": 42, "dsc": "()Ljavax/net/ssl/SSLSocketFactory;"}], "flds": [{"acc": 10, "nme": "defaultSSLSocketFactory", "dsc": "Ljavax/net/ssl/SSLSocketFactory;"}, {"acc": 18, "nme": "enabledCipherSuites", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "enabledProtocols", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "needClientAuth", "dsc": "Z"}, {"acc": 18, "nme": "context", "dsc": "Ljavax/net/ssl/SSLContext;"}]}, "classes/sun/management/jmxremote/LocalRMIServerSocketFactory.class": {"ver": 65, "acc": 49, "nme": "sun/management/jmxremote/LocalRMIServerSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "createServerSocket", "acc": 1, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}], "flds": []}, "classes/jdk/internal/agent/Agent$TextStatusCollector.class": {"ver": 65, "acc": 48, "nme": "jdk/internal/agent/Agent$TextStatusCollector", "super": "jdk/internal/agent/Agent$StatusCollector", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "addAgentStatus", "acc": 4, "dsc": "(Z)V"}, {"nme": "appendConnectionsHeader", "acc": 4, "dsc": "()V"}, {"nme": "addConnectionDetails", "acc": 4, "dsc": "(Ljavax/management/remote/JMXServiceURL;)V"}, {"nme": "appendConnectionHeader", "acc": 4, "dsc": "(Z)V"}, {"nme": "appendConfigPropsHeader", "acc": 4, "dsc": "()V"}, {"nme": "addConfigProp", "acc": 4, "dsc": "(Ljava/util/Map$Entry;)V", "sig": "(Ljava/util/Map$Entry<**>;)V"}, {"nme": "appendConnectionsFooter", "acc": 4, "dsc": "()V"}, {"nme": "appendConnectionFooter", "acc": 4, "dsc": "(Z)V"}, {"nme": "appendConfigPropsFooter", "acc": 4, "dsc": "()V"}], "flds": []}, "classes/jdk/internal/agent/AgentConfigurationError.class": {"ver": 65, "acc": 33, "nme": "jdk/internal/agent/AgentConfigurationError", "super": "java/lang/Error", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;[Ljava/lang/String;)V"}, {"nme": "getError", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getParams", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 25, "nme": "AGENT_EXCEPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.exception"}, {"acc": 25, "nme": "CONFIG_FILE_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.configfile.notfound"}, {"acc": 25, "nme": "CONFIG_FILE_OPEN_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.configfile.failed"}, {"acc": 25, "nme": "CONFIG_FILE_CLOSE_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.configfile.closed.failed"}, {"acc": 25, "nme": "CONFIG_FILE_ACCESS_DENIED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.configfile.access.denied"}, {"acc": 25, "nme": "EXPORT_ADDRESS_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.exportaddress.failed"}, {"acc": 25, "nme": "AGENT_CLASS_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.agentclass.notfound"}, {"acc": 25, "nme": "AGENT_CLASS_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.agentclass.failed"}, {"acc": 25, "nme": "AGENT_CLASS_PREMAIN_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.premain.notfound"}, {"acc": 25, "nme": "AGENT_CLASS_ACCESS_DENIED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.agentclass.access.denied"}, {"acc": 25, "nme": "AGENT_CLASS_INVALID", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.agentclass"}, {"acc": 25, "nme": "INVALID_JMXREMOTE_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.jmxremote.port"}, {"acc": 25, "nme": "INVALID_JMXREMOTE_RMI_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.jmxremote.rmi.port"}, {"acc": 25, "nme": "INVALID_JMXREMOTE_LOCAL_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.jmxremote.local.port"}, {"acc": 25, "nme": "PASSWORD_FILE_NOT_SET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.password.file.notset"}, {"acc": 25, "nme": "PASSWORD_FILE_NOT_READABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.password.file.not.readable"}, {"acc": 25, "nme": "PASSWORD_FILE_READ_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.password.file.read.failed"}, {"acc": 25, "nme": "PASSWORD_FILE_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.password.file.notfound"}, {"acc": 25, "nme": "ACCESS_FILE_NOT_SET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.access.file.notset"}, {"acc": 25, "nme": "ACCESS_FILE_NOT_READABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.access.file.not.readable"}, {"acc": 25, "nme": "ACCESS_FILE_READ_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.access.file.read.failed"}, {"acc": 25, "nme": "ACCESS_FILE_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.access.file.notfound"}, {"acc": 25, "nme": "PASSWORD_FILE_ACCESS_NOT_RESTRICTED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.password.file.access.notrestricted"}, {"acc": 25, "nme": "FILE_ACCESS_NOT_RESTRICTED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.file.access.not.restricted"}, {"acc": 25, "nme": "FILE_NOT_FOUND", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.file.not.found"}, {"acc": 25, "nme": "FILE_NOT_READABLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.file.not.readable"}, {"acc": 25, "nme": "FILE_NOT_SET", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.file.not.set"}, {"acc": 25, "nme": "FILE_READ_FAILED", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.file.read.failed"}, {"acc": 25, "nme": "CONNECTOR_SERVER_IO_ERROR", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.connector.server.io.error"}, {"acc": 25, "nme": "INVALID_OPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.option"}, {"acc": 25, "nme": "INVALID_STATE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "agent.err.invalid.state"}, {"acc": 18, "nme": "error", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "params", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1211605593516195475}]}, "classes/jdk/internal/agent/Agent.class": {"ver": 65, "acc": 33, "nme": "jdk/internal/agent/Agent", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "parseString", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Properties;"}, {"nme": "premain", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "agentmain", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "startLocalManagementAgent", "acc": 42, "dsc": "()V"}, {"nme": "startRemoteManagementAgent", "acc": 42, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "stopRemoteManagementAgent", "acc": 42, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "getManagementAgentStatus", "acc": 42, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/lang/Exception"]}, {"nme": "startAgent", "acc": 10, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/lang/Exception"]}, {"nme": "startDiscoveryService", "acc": 10, "dsc": "(Ljava/util/Properties;)V", "exs": ["java/io/IOException", "sun/management/jdp/JdpException"]}, {"nme": "loadManagementProperties", "acc": 9, "dsc": "()Ljava/util/Properties;"}, {"nme": "getManagementProperties", "acc": 41, "dsc": "()Ljava/util/Properties;"}, {"nme": "readConfiguration", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/String;Ljava/util/Properties;)V"}, {"nme": "startAgent", "acc": 9, "dsc": "()V", "exs": ["java/lang/Exception"]}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "error", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/Exception;)V"}, {"nme": "error", "acc": 9, "dsc": "(Ljdk/internal/agent/AgentConfigurationError;)V"}, {"nme": "warning", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "initResource", "acc": 10, "dsc": "()V"}, {"nme": "getText", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getText", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "mgmtProps", "dsc": "Ljava/util/Properties;"}, {"acc": 10, "nme": "messageRB", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 26, "nme": "CONFIG_FILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.config.file"}, {"acc": 26, "nme": "JMXREMOTE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote"}, {"acc": 26, "nme": "JMXREMOTE_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.port"}, {"acc": 26, "nme": "RMI_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.rmi.port"}, {"acc": 26, "nme": "ENABLE_THREAD_CONTENTION_MONITORING", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.enableThreadContentionMonitoring"}, {"acc": 26, "nme": "LOCAL_CONNECTOR_ADDRESS_PROP", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.localConnectorAddress"}, {"acc": 26, "nme": "JDP_DEFAULT_ADDRESS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "************"}, {"acc": 26, "nme": "JDP_DEFAULT_PORT", "dsc": "I", "val": 7095}, {"acc": 10, "nme": "jmxServer", "dsc": "Ljavax/management/remote/JMXConnectorServer;"}, {"acc": 10, "nme": "configProps", "dsc": "Ljava/util/Properties;"}]}, "classes/sun/management/jmxremote/SingleEntryRegistry.class": {"ver": 65, "acc": 33, "nme": "sun/management/jmxremote/SingleEntryRegistry", "super": "sun/rmi/registry/RegistryImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "<init>", "acc": 0, "dsc": "(ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/lang/String;Ljava/rmi/Remote;)V", "exs": ["java/rmi/RemoteException"]}, {"nme": "list", "acc": 1, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lookup", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON>java/rmi/Remote;", "exs": ["java/rmi/NotBoundException"]}, {"nme": "bind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/AccessException"]}, {"nme": "rebind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/rmi/Remote;)V", "exs": ["java/rmi/AccessException"]}, {"nme": "unbind", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/rmi/AccessException"]}, {"nme": "singleRegistryFilter", "acc": 10, "dsc": "(Ljava/io/ObjectInputFilter$FilterInfo;)Ljava/io/ObjectInputFilter$Status;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "object", "dsc": "<PERSON><PERSON><PERSON>/rmi/Remote;"}, {"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -4897238949499730950}]}, "classes/jdk/internal/agent/Agent$StatusCollector.class": {"ver": 65, "acc": 1056, "nme": "jdk/internal/agent/Agent$StatusCollector", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "collect", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "appendConnections", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "addConnection", "acc": 2, "dsc": "(ZLjavax/management/remote/JMXServiceURL;)V"}, {"nme": "addConfigProperties", "acc": 2, "dsc": "()V"}, {"nme": "isManagementProp", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "preprocess", "acc": 10, "dsc": "(Lja<PERSON>/util/function/Function;Ljava/util/function/Predicate;)Ljava/util/function/Predicate;", "sig": "<T:Ljava/lang/Object;V:Ljava/lang/Object;>(Ljava/util/function/Function<TT;TV;>;Ljava/util/function/Predicate<TV;>;)Ljava/util/function/Predicate<TT;>;"}, {"nme": "addAgentStatus", "acc": 1028, "dsc": "(Z)V"}, {"nme": "appendConnectionsHeader", "acc": 1028, "dsc": "()V"}, {"nme": "appendConnectionsFooter", "acc": 1028, "dsc": "()V"}, {"nme": "addConnectionDetails", "acc": 1028, "dsc": "(Ljavax/management/remote/JMXServiceURL;)V"}, {"nme": "appendConnectionHeader", "acc": 1028, "dsc": "(Z)V"}, {"nme": "appendConnectionFooter", "acc": 1028, "dsc": "(Z)V"}, {"nme": "appendConfigPropsHeader", "acc": 1028, "dsc": "()V"}, {"nme": "appendConfigPropsFooter", "acc": 1028, "dsc": "()V"}, {"nme": "addConfigProp", "acc": 1028, "dsc": "(Ljava/util/Map$Entry;)V", "sig": "(Ljava/util/Map$Entry<**>;)V"}, {"nme": "lambda$preprocess$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;Ljava/util/function/Function;Ljava/lang/Object;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 28, "nme": "DEFAULT_PROPS", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 20, "nme": "sb", "dsc": "<PERSON><PERSON><PERSON>/lang/StringBuilder;"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$JMXConnectorServerData.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$JMXConnectorServerData", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljavax/management/remote/JMXConnectorServer;Ljavax/management/remote/JMXServiceURL;)V"}], "flds": [{"acc": 0, "nme": "jmxConnectorServer", "dsc": "Ljavax/management/remote/JMXConnectorServer;"}, {"acc": 0, "nme": "jmxRemoteURL", "dsc": "Ljavax/management/remote/JMXServiceURL;"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$DefaultValues.class": {"ver": 65, "acc": 1537, "nme": "sun/management/jmxremote/ConnectorBootstrap$DefaultValues", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "0"}, {"acc": 25, "nme": "CONFIG_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "management.properties"}, {"acc": 25, "nme": "USE_SSL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "true"}, {"acc": 25, "nme": "USE_LOCAL_ONLY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "true"}, {"acc": 25, "nme": "USE_REGISTRY_SSL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "false"}, {"acc": 25, "nme": "USE_AUTHENTICATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "true"}, {"acc": 25, "nme": "PASSWORD_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmxremote.password"}, {"acc": 25, "nme": "HASH_PASSWORDS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "true"}, {"acc": 25, "nme": "ACCESS_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmxremote.access"}, {"acc": 25, "nme": "SSL_NEED_CLIENT_AUTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "false"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$HostAwareSslSocketFactory.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$HostAwareSslSocketFactory", "super": "javax/rmi/ssl/SslRMIServerSocketFactory", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;[Ljava/lang/String;Z<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Ljavax/net/ssl/SSLContext;[Ljava/lang/String;[Ljava/lang/String;Z<PERSON>java/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}, {"nme": "createServerSocket", "acc": 1, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}, {"nme": "checkValues", "acc": 10, "dsc": "(Ljavax/net/ssl/SSLContext;[Ljava/lang/String;[Ljava/lang/String;)V", "exs": ["java/lang/IllegalArgumentException"]}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "context", "dsc": "Ljavax/net/ssl/SSLContext;"}]}, "classes/sun/management/jdp/JdpPacket.class": {"ver": 65, "acc": 1537, "nme": "sun/management/jdp/JdpPacket", "super": "java/lang/Object", "mthds": [{"nme": "getPacketData", "acc": 1025, "dsc": "()[B", "exs": ["java/io/IOException"]}], "flds": []}, "classes/sun/management/jmxremote/ConnectorBootstrap$AccessFileCheckerAuthenticator.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$AccessFileCheckerAuthenticator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;)V", "exs": ["java/io/IOException"]}, {"nme": "authenticate", "acc": 1, "dsc": "(Lja<PERSON>/lang/Object;)Ljavax/security/auth/Subject;"}, {"nme": "checkAccessFileEntries", "acc": 2, "dsc": "(Ljavax/security/auth/Subject;)V"}, {"nme": "propertiesFromFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Properties;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "environment", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Object;>;"}, {"acc": 18, "nme": "properties", "dsc": "Ljava/util/Properties;"}, {"acc": 18, "nme": "accessFile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/internal/agent/ConnectorAddressLink$PerfHandle.class": {"ver": 65, "acc": 48, "nme": "jdk/internal/agent/ConnectorAddressLink$PerfHandle", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>;)V"}, {"nme": "putLong", "acc": 2, "dsc": "(J)V"}], "flds": [{"acc": 2, "nme": "bb", "dsc": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}]}, "classes/sun/management/jdp/JdpPacketReader.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpPacketReader", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "([B)V", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "getEntry", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/EOFException", "sun/management/jdp/JdpException"]}, {"nme": "getDiscoveryDataAsMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["sun/management/jdp/JdpException"]}], "flds": [{"acc": 18, "nme": "pkt", "dsc": "Ljava/io/DataInputStream;"}, {"acc": 2, "nme": "pmap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/sun/management/jdp/JdpGenericPacket.class": {"ver": 65, "acc": 1057, "nme": "sun/management/jdp/JdpGenericPacket", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "checkMagic", "acc": 9, "dsc": "(I)V", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "checkVersion", "acc": 9, "dsc": "(S)V", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "getMagic", "acc": 9, "dsc": "()I"}, {"nme": "getVersion", "acc": 9, "dsc": "()S"}], "flds": [{"acc": 26, "nme": "MAGIC", "dsc": "I", "val": -1056969150}, {"acc": 26, "nme": "PROTOCOL_VERSION", "dsc": "S", "val": 1}]}, "classes/jdk/internal/agent/resources/agent_de.class": {"ver": 65, "acc": 49, "nme": "jdk/internal/agent/resources/agent_de", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/management/jmxremote/ConnectorBootstrap.class": {"ver": 65, "acc": 49, "nme": "sun/management/jmxremote/ConnectorBootstrap", "super": "java/lang/Object", "mthds": [{"nme": "unexportRegistry", "acc": 9, "dsc": "()V"}, {"nme": "initialize", "acc": 41, "dsc": "()Ljavax/management/remote/JMXConnectorServer;"}, {"nme": "initialize", "acc": 41, "dsc": "(Ljava/lang/String;Ljava/util/Properties;)Ljavax/management/remote/JMXConnectorServer;"}, {"nme": "startRemoteConnectorServer", "acc": 41, "dsc": "(Ljava/lang/String;Ljava/util/Properties;)Ljavax/management/remote/JMXConnectorServer;"}, {"nme": "startLocalConnectorServer", "acc": 9, "dsc": "()Ljavax/management/remote/JMXConnectorServer;"}, {"nme": "checkPasswordFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "checkAccessFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "checkRestrictedFile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getDefaultFileName", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "createSslRMIServerSocketFactory", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/String;[Ljava/lang/String;[Ljava/lang/String;ZLjava/lang/String;)Ljavax/rmi/ssl/SslRMIServerSocketFactory;"}, {"nme": "exportMBeanServer", "acc": 10, "dsc": "(Ljavax/management/MBeanServer;IIZZLjava/lang/String;[Ljava/lang/String;[Ljava/lang/String;ZZLjava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lsun/management/jmxremote/ConnectorBootstrap$JMXConnectorServerData;", "exs": ["java/io/IOException", "java/net/MalformedURLException"]}, {"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "config", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "config", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "config", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "registry", "dsc": "Ljava/rmi/registry/Registry;"}, {"acc": 26, "nme": "logger", "dsc": "Ljava/lang/System$Logger;"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$PermanentExporter.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$PermanentExporter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "exportObject", "acc": 1, "dsc": "(Ljava/rmi/Remote;ILjava/rmi/server/RMIClientSocketFactory;Ljava/rmi/server/RMIServerSocketFactory;Ljava/io/ObjectInputFilter;)Ljava/rmi/Remote;", "exs": ["java/rmi/RemoteException"]}, {"nme": "unexportObject", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/rmi/Remote;Z)Z", "exs": ["java/rmi/NoSuchObjectException"]}], "flds": [{"acc": 0, "nme": "firstExported", "dsc": "<PERSON><PERSON><PERSON>/rmi/Remote;"}]}, "classes/jdk/internal/agent/FileSystemImpl.class": {"ver": 65, "acc": 33, "nme": "jdk/internal/agent/FileSystemImpl", "super": "jdk/internal/agent/FileSystem", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "supportsFileSecurity", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "isAccessUserOnly", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "init0", "acc": 264, "dsc": "()V"}, {"nme": "isSecuritySupported0", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/io/IOException"]}, {"nme": "isAccessUserOnly0", "acc": 264, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": []}, "classes/sun/management/jdp/JdpBroadcaster.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpBroadcaster", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/InetAddress;Ljava/net/InetAddress;II)V", "exs": ["java/io/IOException", "sun/management/jdp/JdpException"]}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/net/InetAddress;II)V", "exs": ["java/io/IOException", "sun/management/jdp/JdpException"]}, {"nme": "sendPacket", "acc": 1, "dsc": "(Lsun/management/jdp/JdpPacket;)V", "exs": ["java/io/IOException"]}, {"nme": "shutdown", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "addr", "dsc": "Ljava/net/InetAddress;"}, {"acc": 18, "nme": "port", "dsc": "I"}, {"acc": 18, "nme": "channel", "dsc": "Ljava/nio/channels/DatagramChannel;"}]}, "classes/sun/management/jdp/JdpException.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpException", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 1}]}, "classes/sun/management/jdp/JdpPacketWriter.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpPacketWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "addEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "addEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "getPacketBytes", "acc": 1, "dsc": "()[B"}], "flds": [{"acc": 18, "nme": "baos", "dsc": "Ljava/io/ByteArrayOutputStream;"}, {"acc": 18, "nme": "pkt", "dsc": "Ljava/io/DataOutputStream;"}]}, "classes/jdk/internal/agent/resources/agent_zh_CN.class": {"ver": 65, "acc": 49, "nme": "jdk/internal/agent/resources/agent_zh_CN", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/internal/agent/FileSystem.class": {"ver": 65, "acc": 1057, "nme": "jdk/internal/agent/FileSystem", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "()V"}, {"nme": "open", "acc": 9, "dsc": "()Ljdk/internal/agent/FileSystem;"}, {"nme": "supportsFileSecurity", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "isAccessUserOnly", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/io/File;)Z", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "lock", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 10, "nme": "fs", "dsc": "Ljdk/internal/agent/FileSystem;"}]}, "classes/sun/management/jdp/JdpJmxPacket.class": {"ver": 65, "acc": 49, "nme": "sun/management/jdp/JdpJmxPacket", "super": "sun/management/jdp/JdpGenericPacket", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/UUID;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "([B)V", "exs": ["sun/management/jdp/JdpException"]}, {"nme": "setMainClass", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "setInstanceName", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getId", "acc": 1, "dsc": "()Ljava/util/UUID;"}, {"nme": "getMainClass", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getJmxServiceUrl", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getInstanceName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getProcessId", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setProcessId", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getRmiHostname", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setRmiHostname", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getBroadcastInterval", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "setBroadcastInterval", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPacketData", "acc": 1, "dsc": "()[B", "exs": ["java/io/IOException"]}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 25, "nme": "UUID_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "DISCOVERABLE_SESSION_UUID"}, {"acc": 25, "nme": "MAIN_CLASS_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "MAIN_CLASS"}, {"acc": 25, "nme": "JMX_SERVICE_URL_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "JMX_SERVICE_URL"}, {"acc": 25, "nme": "INSTANCE_NAME_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "INSTANCE_NAME"}, {"acc": 25, "nme": "PROCESS_ID_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "PROCESS_ID"}, {"acc": 25, "nme": "RMI_HOSTNAME_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "RMI_HOSTNAME"}, {"acc": 25, "nme": "BROADCAST_INTERVAL_KEY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "BROADCAST_INTERVAL"}, {"acc": 2, "nme": "id", "dsc": "Ljava/util/UUID;"}, {"acc": 2, "nme": "mainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "jmxServiceUrl", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "instanceName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "processId", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "rmiHostname", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "broadcastInterval", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/sun/management/jmxremote/ConnectorBootstrap$HostAwareSocketFactory.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/ConnectorBootstrap$HostAwareSocketFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "createServerSocket", "acc": 1, "dsc": "(I)Ljava/net/ServerSocket;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/internal/agent/resources/agent.class": {"ver": 65, "acc": 49, "nme": "jdk/internal/agent/resources/agent", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/sun/management/jmxremote/ConnectorBootstrap$PropertyNames.class": {"ver": 65, "acc": 1537, "nme": "sun/management/jmxremote/ConnectorBootstrap$PropertyNames", "super": "java/lang/Object", "mthds": [], "flds": [{"acc": 25, "nme": "PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.port"}, {"acc": 25, "nme": "HOST", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.host"}, {"acc": 25, "nme": "RMI_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.rmi.port"}, {"acc": 25, "nme": "LOCAL_PORT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.local.port"}, {"acc": 25, "nme": "CONFIG_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.config.file"}, {"acc": 25, "nme": "USE_LOCAL_ONLY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.local.only"}, {"acc": 25, "nme": "USE_SSL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.ssl"}, {"acc": 25, "nme": "USE_REGISTRY_SSL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.registry.ssl"}, {"acc": 25, "nme": "USE_AUTHENTICATION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.authenticate"}, {"acc": 25, "nme": "PASSWORD_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.password.file"}, {"acc": 25, "nme": "HASH_PASSWORDS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.password.toHashes"}, {"acc": 25, "nme": "ACCESS_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.access.file"}, {"acc": 25, "nme": "LOGIN_CONFIG_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.login.config"}, {"acc": 25, "nme": "SSL_ENABLED_CIPHER_SUITES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.ssl.enabled.cipher.suites"}, {"acc": 25, "nme": "SSL_ENABLED_PROTOCOLS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.ssl.enabled.protocols"}, {"acc": 25, "nme": "SSL_NEED_CLIENT_AUTH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.ssl.need.client.auth"}, {"acc": 25, "nme": "SSL_CONFIG_FILE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.ssl.config.file"}, {"acc": 25, "nme": "SERIAL_FILTER_PATTERN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "com.sun.management.jmxremote.serial.filter.pattern"}]}, "classes/sun/management/jmxremote/LocalRMIServerSocketFactory$1.class": {"ver": 65, "acc": 32, "nme": "sun/management/jmxremote/LocalRMIServerSocketFactory$1", "super": "java/net/ServerSocket", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lsun/management/jmxremote/LocalRMIServerSocketFactory;I)V", "exs": ["java/io/IOException"]}, {"nme": "accept", "acc": 1, "dsc": "()Ljava/net/Socket;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/internal/agent/resources/agent_ja.class": {"ver": 65, "acc": 49, "nme": "jdk/internal/agent/resources/agent_ja", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/internal/agent/ConnectorAddressLink.class": {"ver": 65, "acc": 33, "nme": "jdk/internal/agent/ConnectorAddressLink", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "export", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "unexportRemote", "acc": 9, "dsc": "()V"}, {"nme": "unexport", "acc": 10, "dsc": "(Ljdk/internal/agent/ConnectorAddressLink$PerfHandle;)V"}, {"nme": "importFrom", "acc": 9, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;", "exs": ["java/io/IOException"]}, {"nme": "exportRemote", "acc": 9, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "importRemoteFrom", "acc": 9, "dsc": "(I)Ljava/util/Map;", "sig": "(I)<PERSON>java/util/Map<Ljava/lang/String;Ljava/lang/String;>;", "exs": ["java/io/IOException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CONNECTOR_ADDRESS_COUNTER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.management.JMXConnectorServer.address"}, {"acc": 26, "nme": "REMOTE_CONNECTOR_STATE_COUNTER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.management.JMXConnectorServer.remote.enabled"}, {"acc": 26, "nme": "REMOTE_CONNECTOR_COUNTER_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "sun.management.JMXConnectorServer."}, {"acc": 26, "nme": "counter", "dsc": "Ljava/util/concurrent/atomic/AtomicInteger;"}, {"acc": 10, "nme": "remotePerfHandle", "dsc": "Ljdk/internal/agent/ConnectorAddressLink$PerfHandle;"}]}, "classes/jdk/internal/agent/FileSystemImpl$1.class": {"ver": 65, "acc": 32, "nme": "jdk/internal/agent/FileSystemImpl$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "run", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/Void;"}, {"nme": "run", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}}}}