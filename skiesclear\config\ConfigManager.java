/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.io.FilesKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.config;

import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.config.MainConfig;
import com.pokeskies.skiesclear.utils.Utils;
import java.io.File;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import kotlin.Metadata;
import kotlin.io.FilesKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\b\u0018\u0000 \r2\u00020\u0001:\u0001\rB\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\r\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\r\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0004\b\t\u0010\bR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\n\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u000e"}, d2={"Lcom/pokeskies/skiesclear/config/ConfigManager;", "", "Ljava/io/File;", "configDir", "<init>", "(Ljava/io/File;)V", "", "reload", "()V", "copyDefaults", "Ljava/io/File;", "getConfigDir", "()Ljava/io/File;", "Companion", "SkiesClear"})
public final class ConfigManager {
    @NotNull
    public static final Companion Companion = new Companion(null);
    @NotNull
    private final File configDir;
    public static MainConfig CONFIG;

    public ConfigManager(@NotNull File configDir) {
        Intrinsics.checkNotNullParameter((Object)configDir, (String)"configDir");
        this.configDir = configDir;
        this.reload();
    }

    @NotNull
    public final File getConfigDir() {
        return this.configDir;
    }

    public final void reload() {
        this.copyDefaults();
        Companion.setCONFIG((MainConfig)SkiesClear.loadFile$default(SkiesClear.Companion.getINSTANCE(), "config.json", new MainConfig(false, null, 3, null), false, 4, null));
    }

    public final void copyDefaults() {
        ClassLoader classLoader = SkiesClear.class.getClassLoader();
        this.configDir.mkdirs();
        File configFile = FilesKt.resolve((File)this.configDir, (String)"config.json");
        if (!configFile.exists()) {
            try {
                InputStream inputStream = classLoader.getResourceAsStream("assets/skiesclear/config.json");
                Intrinsics.checkNotNullExpressionValue((Object)inputStream, (String)"getResourceAsStream(...)");
                InputStream inputStream2 = inputStream;
                CopyOption[] copyOptionArray = new CopyOption[]{StandardCopyOption.REPLACE_EXISTING};
                Files.copy(inputStream2, configFile.toPath(), copyOptionArray);
            }
            catch (Exception e) {
                Utils.INSTANCE.printError("Failed to copy the default config file: " + e);
            }
        }
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\"\u0010\u0005\u001a\u00020\u00048\u0006@\u0006X\u0086.\u00a2\u0006\u0012\n\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\n\u00a8\u0006\u000b"}, d2={"Lcom/pokeskies/skiesclear/config/ConfigManager$Companion;", "", "<init>", "()V", "Lcom/pokeskies/skiesclear/config/MainConfig;", "CONFIG", "Lcom/pokeskies/skiesclear/config/MainConfig;", "getCONFIG", "()Lcom/pokeskies/skiesclear/config/MainConfig;", "setCONFIG", "(Lcom/pokeskies/skiesclear/config/MainConfig;)V", "SkiesClear"})
    public static final class Companion {
        private Companion() {
        }

        @NotNull
        public final MainConfig getCONFIG() {
            MainConfig mainConfig = CONFIG;
            if (mainConfig != null) {
                return mainConfig;
            }
            Intrinsics.throwUninitializedPropertyAccessException((String)"CONFIG");
            return null;
        }

        public final void setCONFIG(@NotNull MainConfig mainConfig) {
            Intrinsics.checkNotNullParameter((Object)mainConfig, (String)"<set-?>");
            CONFIG = mainConfig;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

