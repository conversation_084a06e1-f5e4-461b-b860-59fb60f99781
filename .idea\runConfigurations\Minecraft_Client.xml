<component name="ProjectRunConfigurationManager">
  <configuration default="false" factoryName="Application" name="Minecraft Client" type="Application">
    <option name="MAIN_CLASS_NAME" value="net.fabricmc.devlaunchinjector.Main"/>
    <module name="ZhaiCobblemonFish.main"/>
    <option name="PROGRAM_PARAMETERS" value=""/>
    <option name="VM_PARAMETERS" value="-Dfabric.dli.config=C:\Users\<USER>\Desktop\菜品\ZhaiCobblemonFish\.gradle\loom-cache\launch.cfg -Dfabric.dli.env=client -Dfabric.dli.main=net.fabricmc.loader.launch.knot.KnotClient"/>
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/run/"/>
    <method v="2">
      <option enabled="true" name="Make"/>
    </method>
    <envs>
      
    </envs>
    <shortenClasspath name="ARGS_FILE"/>
  <classpathModifications/></configuration>
</component>