/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  com.mojang.brigadier.CommandDispatcher
 *  com.mojang.brigadier.builder.LiteralArgumentBuilder
 *  com.mojang.brigadier.context.CommandContext
 *  com.mojang.brigadier.tree.CommandNode
 *  com.mojang.brigadier.tree.LiteralCommandNode
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.jvm.internal.SourceDebugExtension
 *  kotlin.text.StringsKt
 *  me.lucko.fabric.api.permissions.v0.Permissions
 *  net.kyori.adventure.text.ComponentLike
 *  net.minecraft.class_124
 *  net.minecraft.class_2168
 *  net.minecraft.class_2170
 *  net.minecraft.class_2561
 *  net.minecraft.class_2583
 *  org.jetbrains.annotations.NotNull
 */
package com.pokeskies.skiesclear.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.tree.CommandNode;
import com.mojang.brigadier.tree.LiteralCommandNode;
import com.pokeskies.skiesclear.ClearTask;
import com.pokeskies.skiesclear.SkiesClear;
import com.pokeskies.skiesclear.commands.subcommands.DebugCommand;
import com.pokeskies.skiesclear.commands.subcommands.ForceCommand;
import com.pokeskies.skiesclear.commands.subcommands.InfoCommand;
import com.pokeskies.skiesclear.commands.subcommands.ReloadCommand;
import com.pokeskies.skiesclear.config.ClearConfig;
import com.pokeskies.skiesclear.config.ConfigManager;
import com.pokeskies.skiesclear.utils.Utils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import me.lucko.fabric.api.permissions.v0.Permissions;
import net.kyori.adventure.text.ComponentLike;
import net.minecraft.class_124;
import net.minecraft.class_2168;
import net.minecraft.class_2170;
import net.minecraft.class_2561;
import net.minecraft.class_2583;
import org.jetbrains.annotations.NotNull;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\u0018\u0000 \u000e2\u00020\u0001:\u0001\u000eB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u001b\u0010\b\u001a\u00020\u00072\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\b\u0010\tR\u001a\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\b\f\u0010\r\u00a8\u0006\u000f"}, d2={"Lcom/pokeskies/skiesclear/commands/BaseCommand;", "", "<init>", "()V", "Lcom/mojang/brigadier/CommandDispatcher;", "Lnet/minecraft/class_2168;", "dispatcher", "", "register", "(Lcom/mojang/brigadier/CommandDispatcher;)V", "", "", "aliases", "Ljava/util/List;", "Companion", "SkiesClear"})
@SourceDebugExtension(value={"SMAP\nBaseCommand.kt\nKotlin\n*S Kotlin\n*F\n+ 1 BaseCommand.kt\ncom/pokeskies/skiesclear/commands/BaseCommand\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,70:1\n1557#2:71\n1628#2,3:72\n1863#2:75\n1863#2,2:76\n1864#2:78\n*S KotlinDebug\n*F\n+ 1 BaseCommand.kt\ncom/pokeskies/skiesclear/commands/BaseCommand\n*L\n23#1:71\n23#1:72,3\n39#1:75\n40#1:76,2\n39#1:78\n*E\n"})
public final class BaseCommand {
    @NotNull
    public static final Companion Companion = new Companion(null);
    @NotNull
    private final List<String> aliases = CollectionsKt.listOf((Object)"skiesclear");

    /*
     * WARNING - void declaration
     */
    public final void register(@NotNull CommandDispatcher<class_2168> dispatcher) {
        void $this$mapTo$iv$iv;
        Intrinsics.checkNotNullParameter(dispatcher, (String)"dispatcher");
        Iterable $this$map$iv = this.aliases;
        boolean $i$f$map22 = false;
        Iterable iterable = $this$map$iv;
        Collection destination$iv$iv = new ArrayList(CollectionsKt.collectionSizeOrDefault((Iterable)$this$map$iv, (int)10));
        boolean $i$f$mapTo = false;
        for (Object item$iv$iv : $this$mapTo$iv$iv) {
            void it;
            String string = (String)item$iv$iv;
            Collection collection = destination$iv$iv;
            boolean bl = false;
            collection.add(((LiteralArgumentBuilder)((LiteralArgumentBuilder)class_2170.method_9247((String)it).requires(Permissions.require((String)"skiesclear.command.base", (int)4))).executes(BaseCommand::register$lambda$1$lambda$0)).build());
        }
        List rootCommands = (List)destination$iv$iv;
        Object[] $i$f$map22 = new LiteralCommandNode[]{new DebugCommand().build(), new ReloadCommand().build(), new ForceCommand().build(), new InfoCommand().build()};
        List subCommands = CollectionsKt.listOf((Object[])$i$f$map22);
        Iterable $this$forEach$iv = rootCommands;
        boolean $i$f$forEach = false;
        for (Object element$iv : $this$forEach$iv) {
            LiteralCommandNode root = (LiteralCommandNode)element$iv;
            boolean bl = false;
            Iterable $this$forEach$iv2 = subCommands;
            boolean $i$f$forEach2 = false;
            for (Object element$iv2 : $this$forEach$iv2) {
                LiteralCommandNode sub = (LiteralCommandNode)element$iv2;
                boolean bl2 = false;
                root.addChild((CommandNode)sub);
            }
            dispatcher.getRoot().addChild((CommandNode)root);
        }
    }

    private static final int register$lambda$1$lambda$0(CommandContext ctx) {
        Intrinsics.checkNotNull((Object)ctx);
        return BaseCommand.Companion.execute((CommandContext<class_2168>)ctx, null);
    }

    @Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J'\u0010\n\u001a\u00020\t2\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\b\u0010\b\u001a\u0004\u0018\u00010\u0007H\u0002\u00a2\u0006\u0004\b\n\u0010\u000b\u00a8\u0006\f"}, d2={"Lcom/pokeskies/skiesclear/commands/BaseCommand$Companion;", "", "<init>", "()V", "Lcom/mojang/brigadier/context/CommandContext;", "Lnet/minecraft/class_2168;", "ctx", "", "id", "", "execute", "(Lcom/mojang/brigadier/context/CommandContext;Ljava/lang/String;)I", "SkiesClear"})
    @SourceDebugExtension(value={"SMAP\nBaseCommand.kt\nKotlin\n*S Kotlin\n*F\n+ 1 BaseCommand.kt\ncom/pokeskies/skiesclear/commands/BaseCommand$Companion\n+ 2 Maps.kt\nkotlin/collections/MapsKt__MapsKt\n*L\n1#1,70:1\n535#2:71\n520#2,6:72\n*S KotlinDebug\n*F\n+ 1 BaseCommand.kt\ncom/pokeskies/skiesclear/commands/BaseCommand$Companion\n*L\n50#1:71\n50#1:72,6\n*E\n"})
    public static final class Companion {
        private Companion() {
        }

        /*
         * WARNING - void declaration
         */
        private final int execute(CommandContext<class_2168> ctx, String id) {
            void $this$filterTo$iv$iv;
            Map<String, ClearConfig> $this$filter$iv = ConfigManager.Companion.getCONFIG().getClears();
            boolean $i$f$filter = false;
            Map<String, ClearConfig> map = $this$filter$iv;
            Map destination$iv$iv = new LinkedHashMap();
            boolean $i$f$filterTo = false;
            Iterator<Object> iterator = $this$filterTo$iv$iv.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry element$iv$iv;
                Map.Entry it = element$iv$iv = iterator.next();
                boolean bl = false;
                if (!((ClearConfig)it.getValue()).getEnabled()) continue;
                destination$iv$iv.put(element$iv$iv.getKey(), element$iv$iv.getValue());
            }
            Map clearEntries = destination$iv$iv;
            if (clearEntries.isEmpty()) {
                ((class_2168)ctx.getSource()).method_9213((class_2561)class_2561.method_43470((String)"No enabled clear entries found.").method_27694(Companion::execute$lambda$1));
                return 0;
            }
            for (Map.Entry entry : clearEntries.entrySet()) {
                String clearId = (String)entry.getKey();
                ClearConfig clearConfig = (ClearConfig)entry.getValue();
                if (SkiesClear.Companion.getINSTANCE().getClearManager().getClearTask(clearId) == null) continue;
                for (String line : clearConfig.getMessages().getInfo()) {
                    ClearTask clearTask;
                    ((class_2168)ctx.getSource()).sendMessage((ComponentLike)Utils.INSTANCE.deserializeText(StringsKt.replace$default((String)line, (String)"%time_remaining%", (String)Utils.INSTANCE.getFormattedTime(clearTask.getTimer()), (boolean)false, (int)4, null)));
                }
            }
            return 1;
        }

        private static final class_2583 execute$lambda$1(class_2583 it) {
            return it.method_10977(class_124.field_1061);
        }

        public /* synthetic */ Companion(DefaultConstructorMarker $constructor_marker) {
            this();
        }
    }
}

