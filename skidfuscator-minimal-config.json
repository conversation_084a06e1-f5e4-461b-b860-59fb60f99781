{"input": "build/libs/AcePokemonCleaner-2.0.0.jar", "output": "obfuscated/AcePokemonCleaner-2.0.0-minimal-obfuscated.jar", "libraries": ["libs/CobblemonBukkitEvent-1.0-SNAPSHOT (1).jar", "libs/nmsSpigot.jar"], "exempts": ["cn.acebrand.AcePokemonCleaner.AcePokemonCleaner", "cn.acebrand.AcePokemonCleaner.AcePokemonCleaner.*", "cn.acebrand.AcePokemonCleaner.Commands", "cn.acebrand.AcePokemonCleaner.Commands.*", "cn.acebrand.AcePokemonCleaner.ConfigManager", "cn.acebrand.AcePokemonCleaner.ConfigManager.*", "cn.acebrand.AcePokemonCleaner.PokemonCleaner", "cn.acebrand.AcePokemonCleaner.PokemonCleaner.*", "cn.acebrand.AcePokemonCleaner.ItemExclusionManager", "cn.acebrand.AcePokemonCleaner.ItemExclusionManager.*", "cn.acebrand.AcePokemonCleaner.ItemExclusionGUI", "cn.acebrand.AcePokemonCleaner.ItemExclusionGUI.*", "cn.acebrand.AcePokemonCleaner.CleanResult", "cn.acebrand.AcePokemonCleaner.CleanResult.*", "cn.acebrand.AcePokemonCleaner.CleanResult$*", "cn.acebrand.AcePokemonCleaner.WorldCleanResult", "cn.acebrand.AcePokemonCleaner.WorldCleanResult.*", "cn.acebrand.AcePokemonCleaner.WorldCleanResult$*", "cn.acebrand.AcePokemonCleaner.BossBarConfig", "cn.acebrand.AcePokemonCleaner.BossBarConfig.*", "cn.acebrand.AcePokemonCleaner.BossBarConfig$*", "**$Companion", "**$Companion.*", "**$DefaultConstructorMarker", "**$DefaultConstructorMarker.*", "**$WhenMappings", "**$WhenMappings.*", "<init>(*)", "<clinit>(*)", "plugin.yml", "META-INF/**", "org.bukkit.**", "net.md_5.**", "io.papermc.**", "me.clip.**", "com.cobblemon.**", "org.jetbrains.**", "kotlin.**", "kotlin.jvm.internal.**"], "transformers": {"flow": {"enabled": true, "intensity": 1, "config": {"bogusJumps": false, "switchStatements": false}}, "string": {"enabled": true, "intensity": 1, "config": {"pool": false, "encrypt": true}}, "number": {"enabled": true, "intensity": 1, "config": {"arithmetic": false, "bitwise": false}}, "reference": {"enabled": false, "intensity": 0}, "outlining": {"enabled": false, "intensity": 0}, "ahegao": {"enabled": true, "intensity": 1, "config": {"renameFields": false, "renameMethods": false, "renameClasses": false}}, "condition": {"enabled": false, "intensity": 0}, "bogus": {"enabled": false, "intensity": 0}, "switch": {"enabled": false, "intensity": 0}, "flatten": {"enabled": false, "intensity": 0}, "native": {"enabled": false, "intensity": 0}, "pure": {"enabled": false, "intensity": 0}}, "runtime": {"jvm": ["-Xmx4G", "-Xms2G"]}, "debug": false, "phantom": true, "verify": false, "computeFrames": false}