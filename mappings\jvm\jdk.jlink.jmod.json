{"md5": "ee7b9bf14236c3626b7ef3543fce9502", "sha2": "0fd7292f2bf101c2bc55610e223f2eb69a22dada", "sha256": "5846312379d375c9d90a0e40294782a428a9b96db2a69870dd55ced0cd7219e3", "contents": {"classes": {"classes/jdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager", "super": "jdk/tools/jlink/internal/ResourcePoolManager", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "resourcePool", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "getOrderedList", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}], "flds": [{"acc": 18, "nme": "orderedList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"acc": 18, "nme": "poolImpl", "dsc": "Ljdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolImpl;"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$LocationsAdder.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$LocationsAdder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Tree;JLjdk/tools/jlink/internal/BasicImageWriter;)V"}, {"nme": "addLocations", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)I"}, {"nme": "computeContent", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<[B>;"}, {"nme": "computeContent", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;Ljava/util/Map;)I", "sig": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/ImageLocationWriter;>;)I"}], "flds": [{"acc": 2, "nme": "offset", "dsc": "J"}, {"acc": 18, "nme": "content", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<[B>;"}, {"acc": 18, "nme": "writer", "dsc": "Ljdk/tools/jlink/internal/BasicImageWriter;"}, {"acc": 18, "nme": "tree", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$Tree;"}]}, "classes/jdk/tools/jlink/internal/ImagePluginConfiguration.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImagePluginConfiguration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parseConfiguration", "acc": 9, "dsc": "(Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;)Ljdk/tools/jlink/internal/ImagePluginStack;", "exs": ["java/lang/Exception"]}, {"nme": "lambda$parseConfiguration$0", "acc": 4106, "dsc": "(Lja<PERSON>/util/List;Ljava/util/Map$Entry;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CATEGORIES_ORDER", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin$Category;>;"}]}, "classes/jdk/tools/jmod/JmodTask$Hasher.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$Hasher", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask;Ljava/lang/String;Ljava/lang/module/ModuleFinder;)V"}, {"nme": "computeHashes", "acc": 0, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljdk/internal/module/ModuleHashes;>;"}, {"nme": "recordHashes", "acc": 2, "dsc": "(Ljava/io/InputStream;Ljava/io/OutputStream;Ljdk/internal/module/ModuleHashes;)V", "exs": ["java/io/IOException"]}, {"nme": "updateModuleInfo", "acc": 0, "dsc": "(L<PERSON><PERSON>/lang/String;Ljdk/internal/module/ModuleHashes;)V", "exs": ["java/io/IOException"]}, {"nme": "updateModularJar", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;Ljdk/internal/module/ModuleHashes;)V", "exs": ["java/io/IOException"]}, {"nme": "updateJmodFile", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;Ljdk/internal/module/ModuleHashes;)V", "exs": ["java/io/IOException"]}, {"nme": "moduleToPath", "acc": 2, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$moduleToPath$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/InternalError;"}, {"nme": "lambda$updateJmodFile$4", "acc": 4106, "dsc": "(Ljdk/internal/jmod/JmodFile;Ljdk/internal/module/ModuleHashes;Ljdk/tools/jmod/JmodOutputStream;Ljdk/internal/jmod/JmodFile$Entry;)V"}, {"nme": "lambda$updateModularJar$3", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;Ljava/util/jar/JarOutputStream;Ljdk/internal/module/ModuleHashes;Ljava/util/jar/JarEntry;)V"}, {"nme": "lambda$new$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/module/ModuleFinder;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$new$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)Ljava/lang/String;"}], "flds": [{"acc": 16, "nme": "configuration", "dsc": "Lja<PERSON>/lang/module/Configuration;"}, {"acc": 16, "nme": "hashesBuilder", "dsc": "Ljdk/internal/module/ModuleHashesBuilder;"}, {"acc": 16, "nme": "modules", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 16, "nme": "moduleName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jmod/JmodTask;"}]}, "classes/jdk/tools/jlink/internal/plugins/VendorVersionPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/VendorVersionPlugin", "super": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 4161, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "configure", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "hasRawArgument", "acc": 4161, "dsc": "()Z"}, {"nme": "hasArguments", "acc": 4161, "dsc": "()Z"}, {"nme": "getType", "acc": 4161, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}], "flds": []}, "classes/jdk/tools/jlink/builder/DefaultImageBuilder.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/builder/DefaultImageBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "getTargetPlatform", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/Platform;"}, {"nme": "storeFiles", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "checkResourcePool", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "checkDuplicateResources", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "prepareApplicationFiles", "acc": 4, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V", "exs": ["java/io/IOException"]}, {"nme": "getJImageOutputStream", "acc": 1, "dsc": "()Ljava/io/DataOutputStream;"}, {"nme": "entryToFileName", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/lang/String;"}, {"nme": "entryToImagePath", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/nio/file/Path;"}, {"nme": "accept", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V", "exs": ["java/io/IOException"]}, {"nme": "writeEntry", "acc": 2, "dsc": "(Ljava/io/InputStream;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "writeSymLinkEntry", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "nativeDir", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "isWindows", "acc": 2, "dsc": "()Z"}, {"nme": "setExecutable", "acc": 2, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "setReadOnly", "acc": 2, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "getExecutableImage", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/ExecutableImage;"}, {"nme": "patchScripts", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;Ljava/util/List;)V", "sig": "(Ljdk/tools/jlink/internal/ExecutableImage;Ljava/util/List<Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "forEach<PERSON>ath", "acc": 10, "dsc": "(Ljava/nio/file/Path;Ljava/util/function/BiPredicate;Ljava/util/function/Consumer;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/function/BiPredicate<Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;>;Ljava/util/function/Consumer<Ljava/nio/file/Path;>;)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$patchScripts$10", "acc": 4106, "dsc": "(Lja<PERSON>/util/List;Ljava/nio/file/Path;)V"}, {"nme": "lambda$patchScripts$9", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Z"}, {"nme": "lambda$checkDuplicateResources$8", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/util/Map$Entry;)V"}, {"nme": "lambda$checkDuplicateResources$7", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$checkDuplicateResources$6", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$storeFiles$5", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Z"}, {"nme": "lambda$storeFiles$4", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Z"}, {"nme": "lambda$storeFiles$3", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Z"}, {"nme": "lambda$storeFiles$2", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "lambda$storeFiles$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$storeFiles$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "BIN_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "bin"}, {"acc": 25, "nme": "CONF_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "conf"}, {"acc": 25, "nme": "INCLUDE_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "include"}, {"acc": 25, "nme": "LIB_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "lib"}, {"acc": 25, "nme": "LEGAL_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "legal"}, {"acc": 25, "nme": "MAN_DIRNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "man"}, {"acc": 18, "nme": "root", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "launchers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 18, "nme": "mdir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "modules", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 2, "nme": "platform", "dsc": "Ljdk/tools/jlink/internal/Platform;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/tools/jlink/builder/DefaultImageBuilder$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/tools/jlink/builder/DefaultImageBuilder$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$tools$jlink$plugin$ResourcePoolEntry$Type", "dsc": "[I"}]}, "classes/jdk/tools/jlink/internal/Main$JlinkToolProvider.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/Main$JlinkToolProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "description", "acc": 1, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "run", "acc": 129, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/GenerateJLIClassesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/GenerateJLIClassesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/tools/jlink/plugin/Plugin$State;>;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "initialize", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "fileLines", "acc": 2, "dsc": "(Ljava/io/File;)Ljava/util/stream/Stream;", "sig": "(Ljava/io/File;)Ljava/util/stream/Stream<Ljava/lang/String;>;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "lambda$transform$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljava/lang/String;[B)V"}, {"nme": "lambda$transform$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "DEFAULT_TRACE_FILE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "default_jli_trace.txt"}, {"acc": 26, "nme": "JLIA", "dsc": "Ljdk/internal/access/JavaLangInvokeAccess;"}, {"acc": 2, "nme": "mainArgument", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "traceFileStream", "dsc": "Ljava/util/stream/Stream;", "sig": "Ljava/util/stream/Stream<Ljava/lang/String;>;"}, {"acc": 26, "nme": "DIRECT_METHOD_HOLDER_ENTRY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/java.base/java/lang/invoke/DirectMethodHandle$Holder.class"}, {"acc": 26, "nme": "DELEGATING_METHOD_HOLDER_ENTRY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/java.base/java/lang/invoke/DelegatingMethodHandle$Holder.class"}, {"acc": 26, "nme": "BASIC_FORMS_HOLDER_ENTRY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/java.base/java/lang/invoke/LambdaForm$Holder.class"}, {"acc": 26, "nme": "INVOKERS_HOLDER_ENTRY", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/java.base/java/lang/invoke/Invokers$Holder.class"}]}, "classes/jdk/tools/jmod/JmodTask$WarnIfResolvedReasonConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$WarnIfResolvedReasonConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljdk/internal/module/ModuleResolution;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljdk/internal/module/ModuleResolution;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImagePluginStack.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImagePluginStack", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/builder/ImageBuilder;Ljava/util/List;Ljdk/tools/jlink/plugin/Plugin;)V", "sig": "(Ljdk/tools/jlink/builder/ImageBuilder;Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;Ljdk/tools/jlink/plugin/Plugin;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/builder/ImageBuilder;Ljava/util/List;Ljdk/tools/jlink/plugin/Plugin;Z)V", "sig": "(Ljdk/tools/jlink/builder/ImageBuilder;Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;Ljdk/tools/jlink/plugin/Plugin;Z)V"}, {"nme": "operate", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack$ImageProvider;)V", "exs": ["java/lang/Exception"]}, {"nme": "getJImageFileOutputStream", "acc": 1, "dsc": "()Ljava/io/DataOutputStream;", "exs": ["java/io/IOException"]}, {"nme": "getImageBuilder", "acc": 1, "dsc": "()Ljdk/tools/jlink/builder/ImageBuilder;"}, {"nme": "visitResources", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;)Ljdk/tools/jlink/plugin/ResourcePool;", "exs": ["java/lang/Exception"]}, {"nme": "storeFiles", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/internal/BasicImageWriter;)V", "exs": ["java/lang/Exception"]}, {"nme": "getExecutableImage", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}, {"nme": "lambda$visitResources$5", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;Ljava/lang/String;)V"}, {"nme": "lambda$visitResources$4", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;Ljdk/tools/jlink/internal/ImagePluginStack$PreVisitStrings;Ljdk/tools/jlink/internal/ResourcePrevisitor;)V"}, {"nme": "lambda$operate$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)V"}, {"nme": "lambda$operate$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Z"}, {"nme": "lambda$operate$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;Ljdk/tools/jlink/plugin/Plugin;)Ljava/util/List;"}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)V"}], "flds": [{"acc": 18, "nme": "imageBuilder", "dsc": "Ljdk/tools/jlink/builder/ImageBuilder;"}, {"acc": 18, "nme": "lastSorter", "dsc": "Ljdk/tools/jlink/plugin/Plugin;"}, {"acc": 18, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"acc": 18, "nme": "resourcePrevisitors", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/ResourcePrevisitor;>;"}, {"acc": 18, "nme": "validate", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/PluginRepository.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/PluginRepository", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getPlugin", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;"}, {"nme": "newPlugin", "acc": 9, "dsc": "(Ljava/util/Map;Ljava/lang/String;Ljava/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;", "sig": "(Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/String;Ljava/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;"}, {"nme": "registerPlugin", "acc": 41, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)V"}, {"nme": "unregisterPlugin", "acc": 41, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getPlugins", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/ModuleLayer;)<PERSON>ja<PERSON>/util/List;", "sig": "(<PERSON>ja<PERSON>/lang/ModuleLayer;)Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"nme": "getPlugin", "acc": 10, "dsc": "(L<PERSON><PERSON>/lang/Class;Ljava/lang/String;Lja<PERSON>/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;", "sig": "<T::Ljdk/tools/jlink/plugin/Plugin;>(Ljava/lang/Class<TT;>;Ljava/lang/String;Ljava/lang/ModuleLayer;)TT;"}, {"nme": "getPlugins", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;<PERSON><PERSON><PERSON>/lang/Mo<PERSON>leLayer;)Ljava/util/List;", "sig": "<T::Ljdk/tools/jlink/plugin/Plugin;>(Ljava/lang/Class<TT;>;Ljava/lang/ModuleLayer;)Ljava/util/List<TT;>;"}, {"nme": "lambda$getPlugins$0", "acc": 4106, "dsc": "(Ljava/lang/Class;Ljava/util/List;Ljdk/tools/jlink/plugin/Plugin;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "registeredPlugins", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/plugin/Plugin;>;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolConfiguration$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolConfiguration$1", "super": "java/lang/module/ModuleReference", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/module/ModuleDescriptor;Ljava/net/URI;)V"}, {"nme": "open", "acc": 1, "dsc": "()Ljava/lang/module/ModuleReader;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/LegalNoticeFilePlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/LegalNoticeFilePlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/tools/jlink/plugin/Plugin$State;>;"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "dedupLegalNoticeEntry", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "lambda$dedupLegalNoticeEntry$5", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$dedupLegalNoticeEntry$4", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$dedupLegalNoticeEntry$3", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$dedupLegalNoticeEntry$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;"}, {"nme": "lambda$transform$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$transform$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}], "flds": [{"acc": 26, "nme": "ERROR_IF_NOT_SAME_CONTENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "error-if-not-same-content"}, {"acc": 18, "nme": "licenseOrNotice", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;>;"}, {"acc": 2, "nme": "errorIfNotSameContent", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/JarArchive$JarEntry.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/JarArchive$JarEntry", "super": "jdk/tools/jlink/internal/Archive$Entry", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/JarArchive;Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/internal/Archive$Entry$EntryType;Ljava/util/zip/ZipFile;Ljava/util/zip/ZipEntry;)V"}, {"nme": "size", "acc": 1, "dsc": "()J"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "size", "dsc": "J"}, {"acc": 18, "nme": "entry", "dsc": "<PERSON><PERSON><PERSON>/util/zip/ZipEntry;"}, {"acc": 18, "nme": "file", "dsc": "<PERSON><PERSON><PERSON>/util/zip/ZipFile;"}]}, "classes/jdk/tools/jlink/internal/ImageStringsWriter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImageStringsWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "addString", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "add", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "find", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "get", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStream", "acc": 0, "dsc": "()Ljdk/internal/jimage/ImageStream;"}, {"nme": "getSize", "acc": 0, "dsc": "()I"}, {"nme": "getCount", "acc": 0, "dsc": "()I"}], "flds": [{"acc": 26, "nme": "NOT_FOUND", "dsc": "I", "val": -1}, {"acc": 24, "nme": "EMPTY_OFFSET", "dsc": "I", "val": 0}, {"acc": 18, "nme": "stringToOffsetMap", "dsc": "<PERSON><PERSON><PERSON>/util/HashMap;", "sig": "<PERSON><PERSON><PERSON>/util/Hash<PERSON>ap<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"acc": 18, "nme": "stream", "dsc": "Ljdk/internal/jimage/ImageStream;"}]}, "classes/module-info.class": {"ver": 65, "acc": 32768, "nme": "module-info", "mthds": [], "flds": []}, "classes/jdk/tools/jlink/internal/JarArchive.class": {"ver": 65, "acc": 1057, "nme": "jdk/tools/jlink/internal/JarArchive", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;Ljava/lang/Runtime$Version;)V"}, {"nme": "moduleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/internal/Archive$Entry;>;"}, {"nme": "toEntryType", "acc": 1024, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "getFileName", "acc": 1024, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "toEntry", "acc": 1024, "dsc": "(Ljava/util/zip/ZipEntry;)Ljdk/tools/jlink/internal/Archive$Entry;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "open", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getJarFile", "acc": 4, "dsc": "()<PERSON>java/util/jar/JarFile;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$entries$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarEntry;)Z"}], "flds": [{"acc": 18, "nme": "file", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "moduleName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "version", "dsc": "Ljava/lang/Runtime$Version;"}, {"acc": 2, "nme": "jarFile", "dsc": "<PERSON><PERSON><PERSON>/util/jar/JarFile;"}]}, "classes/jdk/tools/jlink/builder/ImageBuilder.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/builder/ImageBuilder", "super": "java/lang/Object", "mthds": [{"nme": "storeFiles", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljava/util/Properties;)V"}, {"nme": "storeFiles", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "getJImageOutputStream", "acc": 1025, "dsc": "()Ljava/io/DataOutputStream;"}, {"nme": "getExecutableImage", "acc": 1025, "dsc": "()Ljdk/tools/jlink/internal/ExecutableImage;"}, {"nme": "getTargetPlatform", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/Platform;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$Mode.class": {"ver": 65, "acc": 16432, "nme": "jdk/tools/jmod/JmodTask$Mode", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jmod/JmodTask$Mode;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jmod/JmodTask$Mode;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jmod/JmodTask$Mode;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CREATE", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 16409, "nme": "EXTRACT", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 16409, "nme": "LIST", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 16409, "nme": "DESCRIBE", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 16409, "nme": "HASH", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jmod/JmodTask$Mode;"}]}, "classes/jdk/tools/jmod/resources/jmod.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jmod/resources/jmod", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/tools/jlink/plugin/Plugin$State;>;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "transformModuleInfos", "acc": 0, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/util/List;", "sig": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;"}, {"nme": "genSystemModulesClasses", "acc": 2, "dsc": "(Ljava/util/List;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/util/Set;", "sig": "(Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "resolve", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleFinder;Ljava/util/Set;)Ljava/lang/module/Configuration;", "sig": "(Ljava/lang/module/ModuleFinder;Ljava/util/Set<Ljava/lang/String;>;)Ljava/lang/module/Configuration;"}, {"nme": "sublist", "acc": 2, "dsc": "(Lja<PERSON>/util/List;Ljava/lang/module/Configuration;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;Ljava/lang/module/Configuration;)Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;"}, {"nme": "genSystemModulesClass", "acc": 2, "dsc": "(Ljava/util/List;Ljava/lang/module/Configuration;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/lang/String;", "sig": "(Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;Ljava/lang/module/Configuration;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/lang/String;"}, {"nme": "genSystemModulesMapClass", "acc": 2, "dsc": "(Ljava/lang/constant/ClassDesc;Ljava/lang/constant/ClassDesc;Ljava/util/Map;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/lang/String;", "sig": "(Ljava/lang/constant/ClassDesc;Ljava/lang/constant/ClassDesc;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljava/lang/String;"}, {"nme": "sorted", "acc": 10, "dsc": "(L<PERSON><PERSON>/util/Collection;)Ljava/util/List;", "sig": "<T::Ljava/lang/Comparable<TT;>;>(Ljava/util/Collection<TT;>;)Ljava/util/List<TT;>;"}, {"nme": "finderOf", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/module/ModuleFinder;", "sig": "(Ljava/util/Collection<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;)Ljava/lang/module/ModuleFinder;"}, {"nme": "lambda$finderOf$11", "acc": 4106, "dsc": "()Ljava/lang/module/ModuleReader;"}, {"nme": "lambda$genSystemModulesMapClass$10", "acc": 4106, "dsc": "(Lja<PERSON>/lang/constant/ClassDesc;Ljava/lang/constant/ClassDesc;Ljava/util/Map;Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "lambda$genSystemModulesMapClass$9", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genSystemModulesMapClass$8", "acc": 4106, "dsc": "(Ljava/util/Map;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genSystemModulesMapClass$7", "acc": 4106, "dsc": "(Lja<PERSON>/lang/constant/ClassDesc;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genSystemModulesMapClass$6", "acc": 4106, "dsc": "(Lja<PERSON>/lang/constant/ClassDesc;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genSystemModulesMapClass$5", "acc": 4106, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$sublist$4", "acc": 4106, "dsc": "(Ljava/util/Set;Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;)Z"}, {"nme": "lambda$transformModuleInfos$3", "acc": 4106, "dsc": "(Ljava/util/List;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "lambda$transformModuleInfos$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljdk/tools/jlink/plugin/PluginException;"}, {"nme": "lambda$transform$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$transform$0", "acc": 4106, "dsc": "(Ljava/util/Set;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CLASSFILE_VERSION", "dsc": "I"}, {"acc": 26, "nme": "SYSTEM_MODULES_MAP_CLASSNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk/internal/module/SystemModulesMap"}, {"acc": 26, "nme": "SYSTEM_MODULES_CLASS_PREFIX", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk/internal/module/SystemModules$"}, {"acc": 26, "nme": "ALL_SYSTEM_MODULES_CLASSNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk/internal/module/SystemModules$all"}, {"acc": 26, "nme": "DEFAULT_SYSTEM_MODULES_CLASSNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk/internal/module/SystemModules$default"}, {"acc": 26, "nme": "CD_ALL_SYSTEM_MODULES", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_SYSTEM_MODULES", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_SYSTEM_MODULES_MAP", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 2, "nme": "moduleDescriptorsPerMethod", "dsc": "I"}, {"acc": 2, "nme": "enabled", "dsc": "Z"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/Jlink$PluginsConfiguration.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/Jlink$PluginsConfiguration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/util/List;Ljdk/tools/jlink/builder/ImageBuilder;Ljava/lang/String;)V", "sig": "(Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;Ljdk/tools/jlink/builder/ImageBuilder;Ljava/lang/String;)V"}, {"nme": "getPlugins", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"nme": "getImageBuilder", "acc": 1, "dsc": "()Ljdk/tools/jlink/builder/ImageBuilder;"}, {"nme": "getLastSorterPluginName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"acc": 18, "nme": "imageBuilder", "dsc": "Ljdk/tools/jlink/builder/ImageBuilder;"}, {"acc": 18, "nme": "lastSorterPluginName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jlink/internal/plugins/StripJavaDebugAttributesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/StripJavaDebugAttributesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)V", "sig": "(L<PERSON><PERSON>/util/function/Predicate<Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "lambda$transform$3", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$transform$2", "acc": 4106, "dsc": "(Ljdk/internal/classfile/MethodElement;)Z"}, {"nme": "lambda$transform$1", "acc": 4106, "dsc": "(Ljdk/internal/classfile/ClassElement;)Z"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;I)V", "sig": "(Ljava/lang/String;Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;I)V"}, {"nme": "getNextLocalVar", "acc": 2, "dsc": "()I"}, {"nme": "dedups", "acc": 2, "dsc": "(Ljava/lang/module/ModuleDescriptor;)V"}, {"nme": "genClassBytes", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/Configuration;)[B"}, {"nme": "genConstructor", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genHasSplitPackages", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genIncubatorModules", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genModuleDescriptorsMethod", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genModuleTargetsMethod", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genModuleHashesMethod", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genModuleResolutionsMethod", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "genModuleReads", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;Ljava/lang/module/Configuration;)V"}, {"nme": "generate", "acc": 2, "dsc": "(Ljdk/internal/classfile/ClassBuilder;Ljava/lang/String;Ljava/util/Map;Z)V", "sig": "(Ljdk/internal/classfile/ClassBuilder;Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/String;>;>;Z)V"}, {"nme": "genImmutableSet", "acc": 2, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)V", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "lambda$generate$14", "acc": 4098, "dsc": "(ZLjava/util/Map;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$generate$13", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;<PERSON><PERSON><PERSON>/util/Set;)Z"}, {"nme": "lambda$genModuleReads$12", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ResolvedModule;)Ljava/util/Set;"}, {"nme": "lambda$genModuleResolutionsMethod$11", "acc": 4098, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genModuleHashesMethod$10", "acc": 4098, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genModuleTargetsMethod$9", "acc": 4098, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genModuleDescriptorsMethod$8", "acc": 4098, "dsc": "(IILjava/lang/constant/ClassDesc;Ljava/util/List;IILjava/lang/String;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genModuleDescriptorsMethod$7", "acc": 4098, "dsc": "(Lja<PERSON>/lang/constant/ClassDesc;Ljava/lang/String;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genModuleDescriptorsMethod$6", "acc": 4098, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genIncubatorModules$5", "acc": 4106, "dsc": "(ZLjdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genIncubatorModules$4", "acc": 4106, "dsc": "(Ljdk/internal/module/ModuleResolution;)Z"}, {"nme": "lambda$genHasSplitPackages$3", "acc": 4106, "dsc": "(ZLjdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genConstructor$2", "acc": 4106, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "lambda$genClassBytes$1", "acc": 4098, "dsc": "(Lja<PERSON>/lang/module/Configuration;Ljdk/internal/classfile/ClassBuilder;)V"}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "CD_MODULE_DESCRIPTOR", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_MODULE_BUILDER", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_REQUIRES_MODIFIER", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_EXPORTS_MODIFIER", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_OPENS_MODIFIER", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_MODULE_TARGET", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_MODULE_HASHES", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "CD_MODULE_RESOLUTION", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 26, "nme": "MAX_LOCAL_VARS", "dsc": "I", "val": 256}, {"acc": 18, "nme": "MD_VAR", "dsc": "I", "val": 1}, {"acc": 18, "nme": "MT_VAR", "dsc": "I", "val": 1}, {"acc": 18, "nme": "MH_VAR", "dsc": "I", "val": 1}, {"acc": 18, "nme": "DEDUP_LIST_VAR", "dsc": "I", "val": 2}, {"acc": 18, "nme": "BUILDER_VAR", "dsc": "I", "val": 3}, {"acc": 2, "nme": "nextLocalVar", "dsc": "I"}, {"acc": 18, "nme": "classDesc", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 18, "nme": "moduleInfos", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo;>;"}, {"acc": 18, "nme": "moduleDescriptorsPerMethod", "dsc": "I"}, {"acc": 18, "nme": "dedupSetBuilder", "dsc": "Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$DedupSetBuilder;"}]}, "classes/jdk/tools/jmod/JmodTask$PatternConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$PatternConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/util/regex/Pattern;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/VersionPropsPlugin$2.class": {"ver": 65, "acc": 4128, "nme": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin$2", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$classfile$Opcode", "dsc": "[I"}]}, "classes/jdk/tools/jlink/internal/plugins/OrderResourcesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/OrderResourcesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "stripModule", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getOrdinal", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)I"}, {"nme": "compare", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper;Ljdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper;)I"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "lambda$configure$5", "acc": 4106, "dsc": "(Ljava/nio/file/PathMatcher;ILjava/lang/String;)I"}, {"nme": "lambda$transform$4", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$transform$3", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "lambda$transform$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper;)V"}, {"nme": "lambda$transform$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper;"}, {"nme": "lambda$transform$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "JRT_FILE_SYSTEM", "dsc": "Ljava/nio/file/FileSystem;"}, {"acc": 18, "nme": "filters", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/function/ToIntFunction<Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "orderedPaths", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}]}, "classes/jdk/tools/jlink/internal/JlinkPermission.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/JlinkPermission", "super": "java/security/BasicPermission", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": -3687912306077727801}]}, "classes/jdk/tools/jlink/builder/DefaultImageBuilder$DefaultExecutableImage.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/builder/DefaultImageBuilder$DefaultExecutableImage", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/util/Set;Ljdk/tools/jlink/internal/Platform;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Set<Ljava/lang/String;>;Ljdk/tools/jlink/internal/Platform;)V"}, {"nme": "createArgs", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Ljava/util/List;", "sig": "(Ljava/nio/file/Path;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getHome", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getModules", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getExecutionArgs", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "storeLaunchArgs", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "getTargetPlatform", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/Platform;"}], "flds": [{"acc": 18, "nme": "home", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "args", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "modules", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "platform", "dsc": "Ljdk/tools/jlink/internal/Platform;"}]}, "classes/jdk/tools/jlink/resources/jlink_ja.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/jlink_ja", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/plugin/Plugin.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/Plugin", "super": "java/lang/Object", "mthds": [{"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/tools/jlink/plugin/Plugin$State;>;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUsage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOption", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "hasRawArgument", "acc": 1, "dsc": "()Z"}, {"nme": "getArgumentsDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getStateDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1025, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": []}, "classes/jdk/tools/jlink/resources/plugins_ja.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/plugins_ja", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ByteArrayResourcePoolEntry.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ByteArrayResourcePoolEntry", "super": "jdk/tools/jlink/internal/AbstractResourcePoolEntry", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;[B)V"}, {"nme": "contentBytes", "acc": 1, "dsc": "()[B"}, {"nme": "content", "acc": 1, "dsc": "()Ljava/io/InputStream;"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "contentLength", "acc": 1, "dsc": "()J"}], "flds": [{"acc": 18, "nme": "buffer", "dsc": "[B"}]}, "classes/jdk/tools/jmod/JmodTask$JmodFileWriter$3.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodFileWriter$3", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask$JmodFileWriter;Ljava/nio/file/Path;Ljdk/internal/jmod/JmodFile$Section;Ljdk/tools/jmod/JmodOutputStream;Ljava/util/SortedMap;)V"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 4112, "nme": "val$path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 4112, "nme": "val$section", "dsc": "Ljdk/internal/jmod/JmodFile$Section;"}, {"acc": 4112, "nme": "val$out", "dsc": "Ljdk/tools/jmod/JmodOutputStream;"}, {"acc": 4112, "nme": "val$filesToProcess", "dsc": "Ljava/util/SortedMap;"}, {"acc": 4112, "nme": "this$1", "dsc": "Ljdk/tools/jmod/JmodTask$JmodFileWriter;"}]}, "classes/jdk/tools/jlink/internal/JmodArchive.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/JmodArchive", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;)V"}, {"nme": "moduleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/internal/Archive$Entry;>;"}, {"nme": "open", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "ensureOpen", "acc": 2, "dsc": "()V"}, {"nme": "toEntryType", "acc": 2, "dsc": "(Ljdk/internal/jmod/JmodFile$Section;)Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "toEntry", "acc": 2, "dsc": "(Ljdk/internal/jmod/JmodFile$Entry;)Ljdk/tools/jlink/internal/Archive$Entry;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 26, "nme": "JMOD_EXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".jmod"}, {"acc": 18, "nme": "file", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "moduleName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "jmodFile", "dsc": "Ljdk/internal/jmod/JmodFile;"}]}, "classes/jdk/tools/jmod/JmodTask$ClassPathConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$ClassPathConverter", "super": "jdk/tools/jmod/JmodTask$AbstractPathConverter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Ljdk/internal/joptsimple/ValueConverter;", "sig": "Ljdk/internal/joptsimple/ValueConverter<Ljava/util/List<Ljava/nio/file/Path;>;>;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "addString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$ModuleHashesBuilder.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$ModuleHashesBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator;Ljdk/internal/module/ModuleHashes;ILjdk/internal/classfile/CodeBuilder;)V"}, {"nme": "build", "acc": 0, "dsc": "()V"}, {"nme": "newModuleHashesBuilder", "acc": 0, "dsc": "()V"}, {"nme": "pushModuleHashes", "acc": 0, "dsc": "()V"}, {"nme": "hashForModule", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V"}, {"nme": "lambda$build$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MODULE_HASHES_BUILDER", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 24, "nme": "STRING_BYTE_ARRAY_SIG", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 16, "nme": "recordedHashes", "dsc": "Ljdk/internal/module/ModuleHashes;"}, {"acc": 16, "nme": "cob", "dsc": "Ljdk/internal/classfile/CodeBuilder;"}, {"acc": 16, "nme": "index", "dsc": "I"}]}, "classes/jdk/tools/jlink/internal/TaskHelper.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/TaskHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "newOptionsHelper", "acc": 1, "dsc": "(Ljava/lang/Class;[Ljdk/tools/jlink/internal/TaskHelper$Option;)Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper;", "sig": "<T:Ljava/lang/Object;>(Ljava/lang/Class<TT;>;[Ljdk/tools/jlink/internal/TaskHelper$Option<*>;)Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper<TT;>;"}, {"nme": "newBadArgs", "acc": 129, "dsc": "(Ljava/lang/String;[Ljava/lang/Object;)Ljdk/tools/jlink/internal/TaskHelper$BadArgs;"}, {"nme": "getMessage", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "setLog", "acc": 1, "dsc": "(Ljava/io/PrintWriter;)V"}, {"nme": "reportError", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "reportUnknownError", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warning", "acc": 129, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "getPluginsConfig", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "showVersion", "acc": 1, "dsc": "(Z)V"}, {"nme": "version", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "showsPlugin", "acc": 10, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Z"}], "flds": [{"acc": 25, "nme": "JLINK_BUNDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.tools.jlink.resources.jlink"}, {"acc": 25, "nme": "JIMAGE_BUNDLE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.tools.jimage.resources.jimage"}, {"acc": 2, "nme": "pluginOptions", "dsc": "Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;"}, {"acc": 2, "nme": "log", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 18, "nme": "bundleHelper", "dsc": "Ljdk/tools/jlink/internal/TaskHelper$ResourceBundleHelper;"}]}, "classes/jdk/tools/jmod/Main.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jmod/Main", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "run", "acc": 137, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/BasicImageWriter.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/BasicImageWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>r;)V"}, {"nme": "getByteOrder", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "addString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "addLocation", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;JJJ)V"}, {"nme": "getLocations", "acc": 0, "dsc": "()[Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"nme": "getLocationsCount", "acc": 0, "dsc": "()I"}, {"nme": "generatePerfectHash", "acc": 2, "dsc": "()V"}, {"nme": "prepareStringBytes", "acc": 2, "dsc": "()V"}, {"nme": "prepareRedirectBytes", "acc": 2, "dsc": "()V"}, {"nme": "prepareLocationBytes", "acc": 2, "dsc": "()V"}, {"nme": "prepareOffsetBytes", "acc": 2, "dsc": "()V"}, {"nme": "prepareHeaderBytes", "acc": 2, "dsc": "()V"}, {"nme": "prepareTableBytes", "acc": 2, "dsc": "()V"}, {"nme": "getBytes", "acc": 1, "dsc": "()[B"}, {"nme": "find", "acc": 0, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"nme": "lambda$generatePerfectHash$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder;Ljdk/tools/jlink/internal/ImageLocationWriter;)V"}], "flds": [{"acc": 25, "nme": "MODULES_IMAGE_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "modules"}, {"acc": 2, "nme": "byteOrder", "dsc": "<PERSON><PERSON><PERSON>/nio/ByteOrder;"}, {"acc": 2, "nme": "strings", "dsc": "Ljdk/tools/jlink/internal/ImageStringsWriter;"}, {"acc": 2, "nme": "length", "dsc": "I"}, {"acc": 2, "nme": "redirect", "dsc": "[I"}, {"acc": 2, "nme": "locations", "dsc": "[Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"acc": 2, "nme": "input", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/ImageLocationWriter;>;"}, {"acc": 2, "nme": "headerStream", "dsc": "Ljdk/internal/jimage/ImageStream;"}, {"acc": 2, "nme": "redirectStream", "dsc": "Ljdk/internal/jimage/ImageStream;"}, {"acc": 2, "nme": "locationOffsetStream", "dsc": "Ljdk/internal/jimage/ImageStream;"}, {"acc": 2, "nme": "locationStream", "dsc": "Ljdk/internal/jimage/ImageStream;"}, {"acc": 2, "nme": "allIndexStream", "dsc": "Ljdk/internal/jimage/ImageStream;"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludePlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ExcludePlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "lambda$transform$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": [{"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/plugin/ResourcePool.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/ResourcePool", "super": "java/lang/Object", "mthds": [{"nme": "moduleView", "acc": 1025, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolModuleView;"}, {"nme": "entries", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1025, "dsc": "()I"}, {"nme": "findEntry", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "findEntryInContext", "acc": 1025, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "contains", "acc": 1025, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "isEmpty", "acc": 1025, "dsc": "()Z"}, {"nme": "byteOrder", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "transformAndCopy", "acc": 1, "dsc": "(Ljava/util/function/Function;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)V", "sig": "(Ljava/util/function/Function<Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)V"}, {"nme": "lambda$transformAndCopy$0", "acc": 4106, "dsc": "(Ljava/util/function/Function;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}], "flds": []}, "classes/jdk/tools/jimage/Main.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jimage/Main", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "run", "acc": 9, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;Ljava/io/PrintWriter;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$util$OperatingSystem", "dsc": "[I"}]}, "classes/jdk/tools/jmod/resources/jmod_zh_CN.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jmod/resources/jmod_zh_CN", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager$OrderedResourcePool.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager$OrderedResourcePool", "super": "jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolImpl", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager;)V"}, {"nme": "getOrderedList", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/ImagePluginStack$OrderedResourcePoolManager;"}]}, "classes/jdk/tools/jlink/internal/JlinkTask.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/JlinkTask", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setLog", "acc": 0, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V"}, {"nme": "run", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "cleanupOutput", "acc": 2, "dsc": "(Ljava/nio/file/Path;)V"}, {"nme": "createImage", "acc": 9, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;)V", "exs": ["java/lang/Exception"]}, {"nme": "initJlinkConfig", "acc": 2, "dsc": "()Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "createImage", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;)V", "exs": ["java/lang/Exception"]}, {"nme": "getDefaultModulePath", "acc": 9, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "newModuleFinder", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)<PERSON>ja<PERSON>/lang/module/ModuleFinder;", "sig": "(Ljava/util/List<Ljava/nio/file/Path;>;Ljava/util/Set<Ljava/lang/String;>;Ljava/util/Set<Ljava/lang/String;>;)Ljava/lang/module/ModuleFinder;"}, {"nme": "deleteDirectory", "acc": 10, "dsc": "(Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "toPathLocation", "acc": 10, "dsc": "(Ljava/lang/module/ResolvedModule;)Ljava/nio/file/Path;"}, {"nme": "createImageProvider", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;Ljava/nio/file/Path;ZZZLjava/io/PrintWriter;)Ljdk/tools/jlink/internal/ImagePluginStack$ImageProvider;", "exs": ["java/io/IOException"]}, {"nme": "limitFinder", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleFinder;<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/util/Set;)Ljava/lang/module/ModuleFinder;", "sig": "(Ljava/lang/module/ModuleFinder;Ljava/util/Set<Ljava/lang/String;>;Ljava/util/Set<Ljava/lang/String;>;)Ljava/lang/module/ModuleFinder;"}, {"nme": "uses", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)Ljava/util/Map;", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleReference;>;)Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/String;>;>;"}, {"nme": "printProviders", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/io/PrintWriter;Ljava/lang/String;Ljava/util/Set<Ljava/lang/module/ModuleReference;>;)V"}, {"nme": "printProviders", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/io/PrintWriter;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Set;Ljava/util/Map;)V", "sig": "(Ljava/io/PrintWriter;Ljava/lang/String;Ljava/util/Set<Ljava/lang/module/ModuleReference;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/Set<Ljava/lang/String;>;>;)V"}, {"nme": "suggestProviders", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;Ljava/util/List;)V", "sig": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;Ljava/util/List<Ljava/lang/String;>;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "getSaveOpts", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$suggestProviders$44", "acc": 4106, "dsc": "(Lja<PERSON>/lang/module/ModuleReference;)Ljava/util/stream/Stream;"}, {"nme": "lambda$suggestProviders$43", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;)V"}, {"nme": "lambda$suggestProviders$42", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;Ljava/lang/String;)V"}, {"nme": "lambda$suggestProviders$41", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V"}, {"nme": "lambda$suggestProviders$40", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "lambda$suggestProviders$39", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON>ja<PERSON>/lang/module/ModuleReference;)Z"}, {"nme": "lambda$suggestProviders$38", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)V"}, {"nme": "lambda$suggestProviders$37", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$suggestProviders$36", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)Ljava/lang/String;"}, {"nme": "lambda$printProviders$35", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/io/PrintWriter;Ljava/util/Map$Entry;)V"}, {"nme": "lambda$printProviders$34", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/Map;Ljava/io/PrintWriter;Ljava/lang/module/ModuleDescriptor;)V"}, {"nme": "lambda$printProviders$33", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/io/PrintWriter;Ljava/lang/module/ModuleDescriptor;Ljava/lang/module/ModuleDescriptor$Provides;)V"}, {"nme": "lambda$printProviders$32", "acc": 4106, "dsc": "(Ljava/lang/String;Ljava/lang/module/ModuleDescriptor$Provides;)Z"}, {"nme": "lambda$printProviders$31", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;)V"}, {"nme": "lambda$printProviders$30", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;Ljava/lang/module/ModuleDescriptor$Provides;)V"}, {"nme": "lambda$printProviders$29", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "lambda$printProviders$28", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor$Provides;)Z"}, {"nme": "lambda$uses$27", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;)V"}, {"nme": "lambda$uses$26", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor;Ljava/lang/String;)V"}, {"nme": "lambda$uses$25", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "lambda$uses$24", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleDescriptor$Provides;)V"}, {"nme": "lambda$uses$23", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "lambda$limitFinder$22", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ModuleReference;)V"}, {"nme": "lambda$limitFinder$21", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/module/ResolvedModule;)V"}, {"nme": "lambda$createImageProvider$20", "acc": 4106, "dsc": "(Ljava/io/PrintWriter;Ljava/lang/module/ResolvedModule;)V"}, {"nme": "lambda$createImageProvider$19", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)V"}, {"nme": "lambda$createImageProvider$18", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleReference;)Z"}, {"nme": "lambda$newModuleFinder$17", "acc": 4106, "dsc": "()Ljava/lang/IllegalArgumentException;"}, {"nme": "lambda$initJlinkConfig$16", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$static$15", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$14", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$13", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$12", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$11", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$10", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$9", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$8", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$7", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$6", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/JlinkTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 25, "nme": "DEBUG", "dsc": "Z"}, {"acc": 24, "nme": "IGNORE_SIGNING_DEFAULT", "dsc": "Z", "val": 1}, {"acc": 26, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "Ljdk/tools/jlink/internal/TaskHelper;"}, {"acc": 26, "nme": "recognizedOptions", "dsc": "[Ljdk/tools/jlink/internal/TaskHelper$Option;", "sig": "[Ljdk/tools/jlink/internal/TaskHelper$Option<*>;"}, {"acc": 26, "nme": "PROGNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jlink"}, {"acc": 18, "nme": "options", "dsc": "Ljdk/tools/jlink/internal/JlinkTask$OptionsValues;"}, {"acc": 26, "nme": "optionsHelper", "dsc": "Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper;", "sig": "Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper<Ljdk/tools/jlink/internal/JlinkTask;>;"}, {"acc": 2, "nme": "log", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 24, "nme": "EXIT_OK", "dsc": "I", "val": 0}, {"acc": 24, "nme": "EXIT_ERROR", "dsc": "I", "val": 1}, {"acc": 24, "nme": "EXIT_CMDERR", "dsc": "I", "val": 2}, {"acc": 24, "nme": "EXIT_SYSERR", "dsc": "I", "val": 3}, {"acc": 24, "nme": "EXIT_ABNORMAL", "dsc": "I", "val": 4}, {"acc": 25, "nme": "OPTIONS_RESOURCE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk/tools/jlink/internal/options"}, {"acc": 26, "nme": "ALL_MODULE_PATH", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ALL-MODULE-PATH"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImageResourcesTree", "super": "java/lang/Object", "mthds": [{"nme": "isTreeInfoResource", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<init>", "acc": 1, "dsc": "(JLjdk/tools/jlink/internal/BasicImageWriter;Ljava/util/List;)V", "sig": "(JLjdk/tools/jlink/internal/BasicImageWriter;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "addContent", "acc": 1, "dsc": "(Ljava/io/DataOutputStream;)V", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "paths", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "adder", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$LocationsAdder;"}]}, "classes/jdk/tools/jlink/internal/plugins/StringSharingPlugin.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/plugins/StringSharingPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)V", "sig": "(L<PERSON><PERSON>/util/function/Predicate<Ljava/lang/String;>;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "previsit", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "lambda$previsit$2", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper;Ljdk/tools/jlink/internal/StringTable;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$transform$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$new$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "SIZES", "dsc": "[I"}, {"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludeVMPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ExcludeVMPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getVMs", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;[Ljava/lang/String;)Ljava/util/List;", "sig": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;[Ljava/lang/String;)Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "isRemoved", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "handleJvmCfgFile", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljava/util/TreeSet;Ljava/util/TreeSet;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;", "sig": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljava/util/TreeSet<Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;>;Ljava/util/TreeSet<Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;>;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;", "exs": ["java/io/IOException"]}, {"nme": "jvmlibs", "acc": 10, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)[Ljava/lang/String;"}, {"nme": "lambda$handleJvmCfgFile$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$transform$1", "acc": 4098, "dsc": "(Ljava/util/TreeSet;Ljava/util/TreeSet;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$getVMs$0", "acc": 4106, "dsc": "([Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}], "flds": [{"acc": 26, "nme": "JVM_CFG", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jvm.cfg"}, {"acc": 26, "nme": "ALL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "all"}, {"acc": 26, "nme": "CLIENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "client"}, {"acc": 26, "nme": "SERVER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "server"}, {"acc": 26, "nme": "MINIMAL", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "minimal"}, {"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 2, "nme": "target", "dsc": "Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"acc": 2, "nme": "keepAll", "dsc": "Z"}]}, "classes/jdk/tools/jmod/JmodTask$JmodFileWriter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodFileWriter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask;)V"}, {"nme": "write", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodOutputStream;)V", "exs": ["java/io/IOException"]}, {"nme": "newModuleInfoSupplier", "acc": 0, "dsc": "()Ljava/util/function/Supplier;", "sig": "()Ljava/util/function/Supplier<Ljava/io/InputStream;>;", "exs": ["java/io/IOException"]}, {"nme": "writeModuleInfo", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodOutputStream;Ljava/util/Set;)V", "sig": "(Ljdk/tools/jmod/JmodOutputStream;Ljava/util/Set<Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "validatePackages", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/module/ModuleDescriptor;Ljava/util/Set;)V", "sig": "(Ljava/lang/module/ModuleDescriptor;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "computeHashes", "acc": 2, "dsc": "(Lja<PERSON>/lang/module/ModuleDescriptor;)Ljdk/internal/module/ModuleHashes;"}, {"nme": "findPackages", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)<PERSON><PERSON><PERSON>/util/Set;", "sig": "(Ljava/util/List<Ljava/nio/file/Path;>;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "findPackages", "acc": 0, "dsc": "(Ljava/nio/file/Path;)Ljava/util/Set;", "sig": "(Ljava/nio/file/Path;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "findPackages", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)Ljava/util/Set;", "sig": "(<PERSON><PERSON><PERSON>/util/jar/JarFile;)Ljava/util/Set<Ljava/lang/String;>;"}, {"nme": "isResource", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "toPackageName", "acc": 0, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "toPackageName", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/zip/ZipEntry;)Lja<PERSON>/lang/String;"}, {"nme": "processClasses", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodOutputStream;Ljava/util/List;)V", "sig": "(Ljdk/tools/jmod/JmodOutputStream;Ljava/util/List<Ljava/nio/file/Path;>;)V", "exs": ["java/io/IOException"]}, {"nme": "processSection", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodOutputStream;Ljdk/internal/jmod/JmodFile$Section;Ljava/util/List;)V", "sig": "(Ljdk/tools/jmod/JmodOutputStream;Ljdk/internal/jmod/JmodFile$Section;Ljava/util/List<Ljava/nio/file/Path;>;)V", "exs": ["java/io/IOException"]}, {"nme": "processSection", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodOutputStream;Ljdk/internal/jmod/JmodFile$Section;Ljava/nio/file/Path;)V", "exs": ["java/io/IOException"]}, {"nme": "matches", "acc": 0, "dsc": "(Ljava/nio/file/Path;Ljava/util/List;)Z", "sig": "(Ljava/nio/file/Path;Ljava/util/List<Ljava/nio/file/PathMatcher;>;)Z"}, {"nme": "lambda$findPackages$10", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$findPackages$9", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarEntry;)Ljava/lang/String;"}, {"nme": "lambda$findPackages$8", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarEntry;)Z"}, {"nme": "lambda$findPackages$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$findPackages$6", "acc": 4098, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "lambda$findPackages$5", "acc": 4098, "dsc": "(Ljava/nio/file/Path;)Z"}, {"nme": "lambda$findPackages$4", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Z"}, {"nme": "lambda$validatePackages$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$validatePackages$2", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$writeModuleInfo$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)<PERSON><PERSON><PERSON>/util/Set;"}, {"nme": "lambda$newModuleInfoSupplier$0", "acc": 4106, "dsc": "([B)Ljava/io/InputStream;"}], "flds": [{"acc": 16, "nme": "cmds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "libs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "configs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "classpath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "headerFiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "manPages", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "legalNotices", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "moduleVersion", "dsc": "Ljava/lang/module/ModuleDescriptor$Version;"}, {"acc": 16, "nme": "mainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "targetPlatform", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "excludes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/PathMatcher;>;"}, {"acc": 16, "nme": "moduleResolution", "dsc": "Ljdk/internal/module/ModuleResolution;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jmod/JmodTask;"}]}, "classes/jdk/tools/jimage/JImageTask$ModuleAction.class": {"ver": 65, "acc": 1536, "nme": "jdk/tools/jimage/JImageTask$ModuleAction", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/VersionPropsPlugin$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/plugins/VersionPropsPlugin;)V"}, {"nme": "flushPendingLDC", "acc": 2, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "accept", "acc": 1, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljdk/internal/classfile/CodeElement;)V"}, {"nme": "accept", "acc": 4161, "dsc": "(Ljdk/internal/classfile/ClassfileBuilder;Ljdk/internal/classfile/ClassfileElement;)V"}], "flds": [{"acc": 2, "nme": "pendingLDC", "dsc": "Ljdk/internal/classfile/CodeElement;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/plugins/VersionPropsPlugin;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolImpl.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;)V"}, {"nme": "moduleView", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolModuleView;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1, "dsc": "()I"}, {"nme": "findEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "findEntryInContext", "acc": 1, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "byteOrder", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "getStringTable", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/StringTable;"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/ResourcePoolManager;"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludeFilesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ExcludeFilesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "lambda$transform$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": [{"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jmod/JmodTask$DirPathConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$DirPathConverter", "super": "jdk/tools/jmod/JmodTask$AbstractPathConverter", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "to<PERSON><PERSON>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "INSTANCE", "dsc": "Ljdk/internal/joptsimple/ValueConverter;", "sig": "Ljdk/internal/joptsimple/ValueConverter<Ljava/util/List<Ljava/nio/file/Path;>;>;"}]}, "classes/jdk/tools/jmod/JmodTask$JmodFileWriter$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodFileWriter$1", "super": "java/lang/module/ModuleReference", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask$JmodFileWriter;Ljava/lang/module/ModuleDescriptor;Ljava/net/URI;Ljava/lang/String;)V"}, {"nme": "open", "acc": 1, "dsc": "()Ljava/lang/module/ModuleReader;"}], "flds": [{"acc": 4112, "nme": "val$mn", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jlink/internal/TaskHelper$ResourceBundleHelper.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/TaskHelper$ResourceBundleHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getMessage", "acc": 128, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "bundle", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 18, "nme": "pluginBundle", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/tools/jlink/resources/plugins_de.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/plugins_de", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$JmodHelpFormatter.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jmod/JmodTask$JmodHelpFormatter", "super": "jdk/internal/joptsimple/BuiltinHelpFormatter", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/tools/jmod/JmodTask$Options;)V"}, {"nme": "format", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;)Ljava/lang/String;", "sig": "(Ljava/util/Map<Ljava/lang/String;+Ljdk/internal/joptsimple/OptionDescriptor;>;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "opts", "dsc": "Ljdk/tools/jmod/JmodTask$Options;"}]}, "classes/jdk/tools/jlink/internal/plugins/CDSPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/CDSPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "javaExecutableName", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "generateCDSArchive", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;Z)V"}, {"nme": "process", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;)Ljava/util/List;", "sig": "(Ljdk/tools/jlink/internal/ExecutableImage;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": [{"acc": 26, "nme": "NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "generate-cds-archive"}, {"acc": 2, "nme": "targetPlatform", "dsc": "Ljdk/tools/jlink/internal/Platform;"}, {"acc": 2, "nme": "runtimePlatform", "dsc": "Ljdk/tools/jlink/internal/Platform;"}]}, "classes/jdk/tools/jlink/internal/PostProcessor.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/PostProcessor", "super": "java/lang/Object", "mthds": [{"nme": "process", "acc": 1025, "dsc": "(Ljdk/tools/jlink/internal/ExecutableImage;)Ljava/util/List;", "sig": "(Ljdk/tools/jlink/internal/ExecutableImage;)Ljava/util/List<Ljava/lang/String;>;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$CommandException.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$CommandException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 128, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "showUsage", "acc": 0, "dsc": "(Z)Ljdk/tools/jmod/JmodTask$CommandException;"}, {"nme": "getMessageOrKey", "acc": 138, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 0}, {"acc": 0, "nme": "showUsage", "dsc": "Z"}]}, "classes/jdk/tools/jimage/JImageTask$Task.class": {"ver": 65, "acc": 16432, "nme": "jdk/tools/jimage/JImageTask$Task", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jimage/JImageTask$Task;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jimage/JImageTask$Task;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jimage/JImageTask$Task;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "EXTRACT", "dsc": "Ljdk/tools/jimage/JImageTask$Task;"}, {"acc": 16409, "nme": "INFO", "dsc": "Ljdk/tools/jimage/JImageTask$Task;"}, {"acc": 16409, "nme": "LIST", "dsc": "Ljdk/tools/jimage/JImageTask$Task;"}, {"acc": 16409, "nme": "VERIFY", "dsc": "Ljdk/tools/jimage/JImageTask$Task;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jimage/JImageTask$Task;"}]}, "classes/jdk/tools/jlink/internal/ArchiveEntryResourcePoolEntry.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ArchiveEntryResourcePoolEntry", "super": "jdk/tools/jlink/internal/AbstractResourcePoolEntry", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/internal/Archive$Entry;)V"}, {"nme": "content", "acc": 1, "dsc": "()Ljava/io/InputStream;"}, {"nme": "contentLength", "acc": 1, "dsc": "()J"}, {"nme": "getImageFileType", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/Archive$Entry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}], "flds": [{"acc": 18, "nme": "entry", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry;"}]}, "classes/jdk/tools/jmod/JmodTask$ExtractDirPathConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$ExtractDirPathConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/nio/file/Path;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/Main.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/Main", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "main", "acc": 137, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V", "exs": ["java/lang/Exception"]}, {"nme": "run", "acc": 137, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/PathResourcePoolEntry.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/PathResourcePoolEntry", "super": "jdk/tools/jlink/internal/AbstractResourcePoolEntry", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljava/nio/file/Path;)V"}, {"nme": "content", "acc": 17, "dsc": "()Ljava/io/InputStream;"}, {"nme": "contentLength", "acc": 17, "dsc": "()J"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 4161, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "file", "dsc": "Ljava/nio/file/Path;"}]}, "classes/jdk/tools/jlink/internal/Archive$Entry.class": {"ver": 65, "acc": 1057, "nme": "jdk/tools/jlink/internal/Archive$Entry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/Archive;Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/internal/Archive$Entry$EntryType;)V"}, {"nme": "archive", "acc": 17, "dsc": "()Ljdk/tools/jlink/internal/Archive;"}, {"nme": "type", "acc": 17, "dsc": "()Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "name", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getResourcePoolEntryName", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "size", "acc": 1025, "dsc": "()J"}, {"nme": "stream", "acc": 1025, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 18, "nme": "archive", "dsc": "Ljdk/tools/jlink/internal/Archive;"}, {"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$PackageNode$PackageReference.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$PackageNode$PackageReference", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "isEmpty", "dsc": "Z"}]}, "classes/jdk/tools/jmod/JmodTask$DateConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$DateConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/time/LocalDateTime;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/time/LocalDateTime;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jimage/JImageTask$JImageAction.class": {"ver": 65, "acc": 1536, "nme": "jdk/tools/jimage/JImageTask$JImageAction", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(Ljava/io/File;Ljdk/internal/jimage/BasicImageReader;)V", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}], "flds": []}, "classes/jdk/tools/jlink/internal/DirArchive$FileEntry.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/DirArchive$FileEntry", "super": "jdk/tools/jlink/internal/Archive$Entry", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/DirArchive;Ljava/nio/file/Path;Ljava/lang/String;)V"}, {"nme": "size", "acc": 1, "dsc": "()J"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "size", "dsc": "J"}, {"acc": 18, "nme": "path", "dsc": "Ljava/nio/file/Path;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/DirArchive;"}]}, "classes/jdk/tools/jlink/resources/plugins_zh_CN.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/plugins_zh_CN", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImagePluginStack$ImageProvider.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/ImagePluginStack$ImageProvider", "super": "java/lang/Object", "mthds": [{"nme": "retrieve", "acc": 1025, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$SetBuilder.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$SetBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/util/Set;ILjava/util/function/IntSupplier;)V", "sig": "(Ljava/util/Set<TT;>;ILjava/util/function/IntSupplier;)V"}, {"nme": "increment", "acc": 16, "dsc": "()V"}, {"nme": "visitElement", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Comparable;Ljdk/internal/classfile/CodeBuilder;)V", "sig": "(TT;Ljdk/internal/classfile/CodeBuilder;)V"}, {"nme": "build", "acc": 16, "dsc": "(Ljdk/internal/classfile/CodeBuilder;)I"}, {"nme": "generateSetOf", "acc": 2, "dsc": "(Ljdk/internal/classfile/CodeBuilder;I)V"}], "flds": [{"acc": 18, "nme": "elements", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<TT;>;"}, {"acc": 18, "nme": "defaultVarIndex", "dsc": "I"}, {"acc": 18, "nme": "nextLocalVar", "dsc": "Ljava/util/function/IntSupplier;"}, {"acc": 2, "nme": "refCount", "dsc": "I"}, {"acc": 2, "nme": "localVarIndex", "dsc": "I"}]}, "classes/jdk/tools/jlink/internal/plugins/ZipPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ZipPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;)V", "sig": "(L<PERSON><PERSON>/util/function/Predicate<Ljava/lang/String;>;)V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/function/Predicate;I)V", "sig": "(<PERSON><PERSON><PERSON>/util/function/Predicate<Ljava/lang/String;>;I)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "compress", "acc": 8, "dsc": "([BI)[B"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "lambda$transform$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": [{"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 26, "nme": "DEFAULT_COMPRESSION", "dsc": "I", "val": 6}, {"acc": 18, "nme": "compressionLevel", "dsc": "I"}]}, "classes/jdk/tools/jimage/JImageTask$ResourceAction.class": {"ver": 65, "acc": 1536, "nme": "jdk/tools/jimage/JImageTask$ResourceAction", "super": "java/lang/Object", "mthds": [{"nme": "apply", "acc": 1025, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljdk/internal/jimage/ImageLocation;)V", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}], "flds": []}, "classes/jdk/tools/jlink/internal/ModularJarArchive.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/ModularJarArchive", "super": "jdk/tools/jlink/internal/JarArchive", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;Ljava/nio/file/Path;Ljava/lang/Runtime$Version;)V"}, {"nme": "toEntryType", "acc": 0, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "toEntry", "acc": 0, "dsc": "(Ljava/util/zip/ZipEntry;)Ljdk/tools/jlink/internal/Archive$Entry;"}, {"nme": "getFileName", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": [{"acc": 26, "nme": "JAR_EXT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": ".jar"}]}, "classes/jdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/internal/StringTable;)[B", "exs": ["java/io/IOException", "java/lang/Exception"]}, {"nme": "optimize", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/internal/StringTable;Ljava/util/Set;[B)[B", "sig": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;Ljdk/tools/jlink/internal/StringTable;Ljava/util/Set<Ljava/lang/Integer;>;[B)[B", "exs": ["java/lang/Exception"]}, {"nme": "writeDescriptorReference", "acc": 2, "dsc": "(Ljava/io/DataOutputStream;Ljava/util/List;)V", "sig": "(Ljava/io/DataOutputStream;Ljava/util/List<Ljava/lang/Integer;>;)V", "exs": ["java/io/IOException"]}, {"nme": "writeUTF8Reference", "acc": 2, "dsc": "(Ljava/io/DataOutputStream;I)V", "exs": ["java/io/IOException"]}, {"nme": "lambda$optimize$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/StringTable;Ljava/lang/String;)Ljava/lang/Integer;"}], "flds": []}, "classes/jdk/tools/jlink/internal/TaskHelper$Option$Processing.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/TaskHelper$Option$Processing", "super": "java/lang/Object", "mthds": [{"nme": "process", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(TT;Lja<PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}], "flds": []}, "classes/jdk/tools/jlink/internal/TaskHelper$PluginOption.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/TaskHelper$PluginOption", "super": "jdk/tools/jlink/internal/TaskHelper$Option", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;ZLjava/lang/String;Ljava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;>;ZLjava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;ZLjava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;>;ZLjava/lang/String;)V"}, {"nme": "resourcePrefix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ResourcePoolEntryFactory.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ResourcePoolEntryFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;[B)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljava/nio/file/Path;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;[B)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljava/nio/file/Path;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "createSymbolicLink", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "moduleFrom", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$Options.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$Options", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "mode", "dsc": "Ljdk/tools/jmod/JmodTask$Mode;"}, {"acc": 0, "nme": "jmodFile", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "help", "dsc": "Z"}, {"acc": 0, "nme": "helpExtra", "dsc": "Z"}, {"acc": 0, "nme": "version", "dsc": "Z"}, {"acc": 0, "nme": "classpath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "cmds", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "configs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "libs", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "headerFiles", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "manPages", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "legalNotices", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 0, "nme": "moduleFinder", "dsc": "<PERSON><PERSON><PERSON>/lang/module/ModuleFinder;"}, {"acc": 0, "nme": "moduleVersion", "dsc": "Ljava/lang/module/ModuleDescriptor$Version;"}, {"acc": 0, "nme": "mainClass", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "targetPlatform", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "modulesToHash", "dsc": "<PERSON><PERSON><PERSON>/util/regex/Pattern;"}, {"acc": 0, "nme": "moduleResolution", "dsc": "Ljdk/internal/module/ModuleResolution;"}, {"acc": 0, "nme": "dryrun", "dsc": "Z"}, {"acc": 0, "nme": "excludes", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/PathMatcher;>;"}, {"acc": 0, "nme": "extractDir", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "date", "dsc": "Ljava/time/LocalDateTime;"}, {"acc": 0, "nme": "compressLevel", "dsc": "I"}]}, "classes/jdk/tools/jlink/internal/plugins/VendorBugURLPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/VendorBugURLPlugin", "super": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 4161, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "configure", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "hasRawArgument", "acc": 4161, "dsc": "()Z"}, {"nme": "hasArguments", "acc": 4161, "dsc": "()Z"}, {"nme": "getType", "acc": 4161, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}], "flds": []}, "classes/jdk/tools/jlink/resources/jlink.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/jlink", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$CompLevelConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$CompLevelConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Integer;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/lang/Integer;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/JlinkTask$ImageHelper.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/JlinkTask$ImageHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Lja<PERSON>/lang/module/Configuration;Ljava/util/Map;Ljava/nio/ByteOrder;Ljava/nio/file/Path;Z)V", "sig": "(Ljava/lang/module/Configuration;Ljava/util/Map<Ljava/lang/String;Ljava/nio/file/Path;>;Ljava/nio/ByteOrder;Ljava/nio/file/Path;Z)V", "exs": ["java/io/IOException"]}, {"nme": "newArchive", "acc": 2, "dsc": "(Ljava/lang/String;Ljava/nio/file/Path;)Ljdk/tools/jlink/internal/Archive;"}, {"nme": "findModuleName", "acc": 10, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "retrieve", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}, {"nme": "lambda$newArchive$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/Archive$Entry;)Z"}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(Ljava/util/Map$Entry;)Ljdk/tools/jlink/internal/Archive;"}], "flds": [{"acc": 16, "nme": "order", "dsc": "<PERSON><PERSON><PERSON>/nio/ByteOrder;"}, {"acc": 16, "nme": "packagedModulesPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 16, "nme": "ignoreSigning", "dsc": "Z"}, {"acc": 16, "nme": "version", "dsc": "Ljava/lang/Runtime$Version;"}, {"acc": 16, "nme": "archives", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/ResourceFilter.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/plugins/ResourceFilter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Z)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;Z)V"}, {"nme": "includeFilter", "acc": 9, "dsc": "(Ljava/util/List;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;"}, {"nme": "includeFilter", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;"}, {"nme": "excludeFilter", "acc": 9, "dsc": "(Ljava/util/List;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;", "sig": "(Ljava/util/List<Ljava/lang/String;>;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;"}, {"nme": "excludeFilter", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/plugins/ResourceFilter;"}, {"nme": "test", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "EMPTY_LIST", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "matchers", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/PathMatcher;>;"}, {"acc": 18, "nme": "include", "dsc": "Z"}, {"acc": 18, "nme": "otherwise", "dsc": "Z"}]}, "classes/jdk/tools/jlink/plugin/ResourcePoolEntry.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/ResourcePoolEntry", "super": "java/lang/Object", "mthds": [{"nme": "moduleName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "path", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "type", "acc": 1025, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"nme": "contentLength", "acc": 1025, "dsc": "()J"}, {"nme": "content", "acc": 1025, "dsc": "()Ljava/io/InputStream;"}, {"nme": "linkedTarget", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "contentBytes", "acc": 1, "dsc": "()[B"}, {"nme": "write", "acc": 1, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "copyWithContent", "acc": 1, "dsc": "([B)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "copyWithContent", "acc": 1, "dsc": "(Ljava/nio/file/Path;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;[B)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;[B)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljava/nio/file/Path;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/lang/String;Ljava/nio/file/Path;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "createSymLink", "acc": 9, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jmod/JmodTask", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "setLog", "acc": 0, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V"}, {"nme": "run", "acc": 1, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "list", "acc": 2, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "extract", "acc": 2, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "hashModules", "acc": 2, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "describe", "acc": 2, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "toLowerCaseString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/String;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Collection<TT;>;)Ljava/lang/String;"}, {"nme": "toString", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)Ljava/lang/String;", "sig": "<T:Ljava/lang/Object;>(Ljava/util/Collection<TT;>;)Ljava/lang/String;"}, {"nme": "describeModule", "acc": 2, "dsc": "(Ljava/lang/module/ModuleDescriptor;Ljdk/internal/module/ModuleTarget;Ljdk/internal/module/ModuleHashes;)V", "exs": ["java/io/IOException"]}, {"nme": "toHex", "acc": 2, "dsc": "([B)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "create", "acc": 2, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "jmodTempFilePath", "acc": 10, "dsc": "(Ljava/nio/file/Path;)Ljava/nio/file/Path;", "exs": ["java/io/IOException"]}, {"nme": "handleOptions", "acc": 2, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "isValidJavaIdentifier", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "getLastElement", "acc": 8, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)<PERSON><PERSON><PERSON>/lang/Object;", "sig": "<E:Ljava/lang/Object;>(Ljava/util/List<TE;>;)TE;"}, {"nme": "reportError", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "warning", "acc": 130, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[Lja<PERSON>/lang/Object;)V"}, {"nme": "showUsageSummary", "acc": 2, "dsc": "()V"}, {"nme": "showHelp", "acc": 2, "dsc": "()V"}, {"nme": "showVersion", "acc": 2, "dsc": "()V"}, {"nme": "version", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 136, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "lambda$describeModule$17", "acc": 4098, "dsc": "(Lja<PERSON>/lang/StringBuilder;Ljdk/internal/module/ModuleHashes;Ljava/lang/String;)V"}, {"nme": "lambda$describeModule$16", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$describeModule$15", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$describeModule$14", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Opens;)V"}, {"nme": "lambda$describeModule$13", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Opens;)V"}, {"nme": "lambda$describeModule$12", "acc": 4106, "dsc": "(Ljava/lang/module/ModuleDescriptor$Opens;)Z"}, {"nme": "lambda$describeModule$11", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Exports;)V"}, {"nme": "lambda$describeModule$10", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Provides;)V"}, {"nme": "lambda$describeModule$9", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/StringBuilder;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "lambda$describeModule$8", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Requires;)V"}, {"nme": "lambda$describeModule$7", "acc": 4106, "dsc": "(Ljava/lang/StringBuilder;Ljava/lang/module/ModuleDescriptor$Exports;)V"}, {"nme": "lambda$describeModule$6", "acc": 4106, "dsc": "(Ljava/lang/module/ModuleDescriptor$Exports;)Z"}, {"nme": "lambda$toString$5", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "lambda$toLowerCaseString$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/String;"}, {"nme": "lambda$hashModules$3", "acc": 4098, "dsc": "(Ljdk/tools/jmod/JmodTask$Hasher;Ljava/lang/String;Ljdk/internal/module/ModuleHashes;)V"}, {"nme": "lambda$hashModules$2", "acc": 4098, "dsc": "(Ljdk/internal/module/ModuleHashes;Ljava/lang/String;)V"}, {"nme": "lambda$extract$1", "acc": 4106, "dsc": "(Ljava/nio/file/Path;Ljdk/internal/jmod/JmodFile;Ljdk/internal/jmod/JmodFile$Entry;)V"}, {"nme": "lambda$list$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/zip/ZipEntry;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "PROGNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jmod"}, {"acc": 26, "nme": "MODULE_INFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "module-info.class"}, {"acc": 26, "nme": "CWD", "dsc": "Ljava/nio/file/Path;"}, {"acc": 2, "nme": "options", "dsc": "Ljdk/tools/jmod/JmodTask$Options;"}, {"acc": 2, "nme": "out", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 24, "nme": "EXIT_OK", "dsc": "I", "val": 0}, {"acc": 24, "nme": "EXIT_ERROR", "dsc": "I", "val": 1}, {"acc": 24, "nme": "EXIT_CMDERR", "dsc": "I", "val": 2}, {"acc": 24, "nme": "EXIT_SYSERR", "dsc": "I", "val": 3}, {"acc": 24, "nme": "EXIT_ABNORMAL", "dsc": "I", "val": 4}, {"acc": 24, "nme": "DATE_MIN", "dsc": "Ljava/time/ZonedDateTime;"}, {"acc": 24, "nme": "DATE_MAX", "dsc": "Ljava/time/ZonedDateTime;"}, {"acc": 26, "nme": "CMD_FILENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "@<filename>"}, {"acc": 18, "nme": "parser", "dsc": "Ljdk/internal/joptsimple/OptionParser;"}]}, "classes/jdk/tools/jimage/resources/jimage.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jimage/resources/jimage", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImageFileCreator$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImageFileCreator$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/BasicImageWriter;)V", "sig": "()V"}, {"nme": "addString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 4112, "nme": "val$writer", "dsc": "Ljdk/tools/jlink/internal/BasicImageWriter;"}]}, "classes/jdk/tools/jlink/internal/PerfectHashBuilder.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/PerfectHashBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Class;Ljava/lang/Class;)V", "sig": "(Ljava/lang/Class<*>;Ljava/lang/Class<*>;)V"}, {"nme": "getCount", "acc": 1, "dsc": "()I"}, {"nme": "getRedirect", "acc": 1, "dsc": "()[I"}, {"nme": "getOrder", "acc": 1, "dsc": "()[Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;", "sig": "()[Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;"}, {"nme": "put", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/Object;)Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;", "sig": "(Ljava/lang/String;TE;)Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;"}, {"nme": "put", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;)Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;", "sig": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;)Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;"}, {"nme": "generate", "acc": 1, "dsc": "()V"}, {"nme": "createBuckets", "acc": 2, "dsc": "()[Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;", "sig": "()[Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket<TE;>;"}, {"nme": "collidedEntries", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;I)Z", "sig": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket<TE;>;I)Z"}, {"nme": "lambda$collidedEntries$3", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Integer;)V"}, {"nme": "lambda$createBuckets$2", "acc": 4098, "dsc": "(I)[Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;"}, {"nme": "lambda$createBuckets$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;)Z"}, {"nme": "lambda$createBuckets$0", "acc": 4098, "dsc": "([Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;)V"}], "flds": [{"acc": 26, "nme": "RETRY_LIMIT", "dsc": "I", "val": 1000}, {"acc": 2, "nme": "entryComponent", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 2, "nme": "bucketComponent", "dsc": "L<PERSON>va/lang/Class;", "sig": "Ljava/lang/Class<*>;"}, {"acc": 18, "nme": "map", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;>;"}, {"acc": 2, "nme": "redirect", "dsc": "[I"}, {"acc": 2, "nme": "order", "dsc": "[Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;", "sig": "[Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;"}, {"acc": 2, "nme": "count", "dsc": "I"}]}, "classes/jdk/tools/jlink/internal/plugins/ReleaseInfoPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ReleaseInfoPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "getState", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljdk/tools/jlink/plugin/Plugin$State;>;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "parseVersion", "acc": 10, "dsc": "(Ljava/lang/module/ModuleDescriptor$Version;)Ljava/lang/String;"}, {"nme": "quote", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "releaseFileContent", "acc": 2, "dsc": "()[B"}, {"nme": "lambda$releaseFileContent$4", "acc": 4106, "dsc": "(Ljava/io/PrintWriter;Ljava/util/Map$Entry;)V"}, {"nme": "lambda$transform$3", "acc": 4098, "dsc": "(Ljava/lang/module/ModuleDescriptor$Version;)V"}, {"nme": "lambda$configure$2", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "lambda$configure$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/util/Map;Ljava/lang/String;)V"}, {"nme": "lambda$configure$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 25, "nme": "KEYS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "keys"}, {"acc": 18, "nme": "release", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/plugin/Plugin$Category.class": {"ver": 65, "acc": 16433, "nme": "jdk/tools/jlink/plugin/Plugin$Category", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "FILTER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "ADDER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "TRANSFORMER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "MODULEINFO_TRANSFORMER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "SORTER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "METAINFO_ADDER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "COMPRESSOR", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "VERIFIER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "PROCESSOR", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 16409, "nme": "PACKAGER", "dsc": "Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jlink/plugin/Plugin$Category;"}]}, "classes/jdk/tools/jlink/resources/plugins.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/plugins", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$JmodFileWriter$2.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodFileWriter$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask$JmodFileWriter;Ljava/lang/module/ModuleDescriptor;Ljava/lang/module/ModuleReference;)V", "sig": "()V"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/module/ModuleReference;>;"}, {"nme": "findAll", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/module/ModuleReference;>;"}], "flds": [{"acc": 4112, "nme": "val$descriptor", "dsc": "Ljava/lang/module/ModuleDescriptor;"}, {"acc": 4112, "nme": "val$mref", "dsc": "L<PERSON><PERSON>/lang/module/ModuleReference;"}]}, "classes/jdk/tools/jlink/internal/JlinkTask$2.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/JlinkTask$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/Map;Ljava/util/Set;)V", "sig": "()V"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/module/ModuleReference;>;"}, {"nme": "findAll", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/module/ModuleReference;>;"}], "flds": [{"acc": 4112, "nme": "val$map", "dsc": "Ljava/util/Map;"}, {"acc": 4112, "nme": "val$mrefs", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/ResourcePoolManager", "super": "java/lang/Object", "mthds": [{"nme": "readModuleAttributes", "acc": 8, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljdk/internal/module/ModuleInfo$Attributes;"}, {"nme": "isNamedPackageResource", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>r;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "resourcePool", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "resourcePoolBuilder", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolBuilder;"}, {"nme": "moduleView", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolModuleView;"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "findModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "modules", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "moduleCount", "acc": 1, "dsc": "()I"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1, "dsc": "()I"}, {"nme": "findEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "findEntryInContext", "acc": 1, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "byteOrder", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "getStringTable", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/StringTable;"}, {"nme": "newCompressedResource", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;Ljava/nio/ByteBuffer;Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/internal/StringTable;Ljava/nio/ByteOrder;)Ljdk/tools/jlink/internal/ResourcePoolManager$CompressedModuleData;"}], "flds": [{"acc": 18, "nme": "resources", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"acc": 18, "nme": "modules", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"acc": 18, "nme": "order", "dsc": "<PERSON><PERSON><PERSON>/nio/ByteOrder;"}, {"acc": 18, "nme": "table", "dsc": "Ljdk/tools/jlink/internal/StringTable;"}, {"acc": 18, "nme": "poolImpl", "dsc": "Ljdk/tools/jlink/plugin/ResourcePool;"}, {"acc": 18, "nme": "poolBuilderImpl", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolBuilder;"}, {"acc": 18, "nme": "moduleViewImpl", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolModuleView;"}]}, "classes/jdk/tools/jlink/internal/DirArchive.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/DirArchive", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/util/function/Consumer;)V", "sig": "(Ljava/nio/file/Path;Ljava/lang/String;Ljava/util/function/Consumer<Ljava/lang/String;>;)V"}, {"nme": "moduleName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/internal/Archive$Entry;>;"}, {"nme": "toEntry", "acc": 2, "dsc": "(Ljava/nio/file/Path;)Ljdk/tools/jlink/internal/Archive$Entry;"}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "open", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "getPathName", "acc": 10, "dsc": "(Lja<PERSON>/nio/file/Path;)Ljava/lang/String;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "lambda$entries$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/Archive$Entry;)Z"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "moduleName", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "open", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/io/InputStream;>;"}, {"acc": 18, "nme": "chop", "dsc": "I"}, {"acc": 18, "nme": "log", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}, {"acc": 26, "nme": "noopConsumer", "dsc": "Ljava/util/function/Consumer;", "sig": "Ljava/util/function/Consumer<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper$DescriptorsScanner.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/plugins/StringSharingPlugin$CompactCPHelper$DescriptorsScanner", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Lcom/sun/tools/classfile/ClassFile;)V"}, {"nme": "scan", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/Integer;>;", "exs": ["java/lang/Exception"]}, {"nme": "scanAttributes", "acc": 2, "dsc": "(Lcom/sun/tools/classfile/Attributes;Ljava/util/Set;)V", "sig": "(Lcom/sun/tools/classfile/Attributes;Ljava/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "scanAnnotation", "acc": 2, "dsc": "(Lcom/sun/tools/classfile/Annotation;Ljava/util/Set;)V", "sig": "(Lcom/sun/tools/classfile/Annotation;Ljava/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "scanElementValue", "acc": 2, "dsc": "(Lcom/sun/tools/classfile/Annotation$element_value;Ljava/util/Set;)V", "sig": "(Lcom/sun/tools/classfile/Annotation$element_value;Ljava/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "scanFields", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "scanMethods", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}, {"nme": "scanConstantPool", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/Integer;>;)V", "exs": ["java/lang/Exception"]}], "flds": [{"acc": 18, "nme": "cf", "dsc": "Lcom/sun/tools/classfile/ClassFile;"}]}, "classes/jdk/tools/jlink/internal/plugins/StripNativeCommandsPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/StripNativeCommandsPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "lambda$transform$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": []}, "classes/jdk/tools/jlink/internal/Platform.class": {"ver": 65, "acc": 65585, "nme": "jdk/tools/jlink/internal/Platform", "super": "java/lang/Record", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/internal/util/OperatingSystem;Ljdk/internal/util/Architecture;)V"}, {"nme": "parsePlatform", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/Platform;"}, {"nme": "runtime", "acc": 9, "dsc": "()Ljdk/tools/jlink/internal/Platform;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "hashCode", "acc": 17, "dsc": "()I"}, {"nme": "equals", "acc": 17, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "os", "acc": 1, "dsc": "()Ljdk/internal/util/OperatingSystem;"}, {"nme": "arch", "acc": 1, "dsc": "()Ljdk/internal/util/Architecture;"}], "flds": [{"acc": 18, "nme": "os", "dsc": "Ljdk/internal/util/OperatingSystem;"}, {"acc": 18, "nme": "arch", "dsc": "Ljdk/internal/util/Architecture;"}]}, "classes/jdk/tools/jlink/internal/plugins/AbstractPlugin.class": {"ver": 65, "acc": 1057, "nme": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/util/ResourceBundle;)V"}, {"nme": "dumpClassFile", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)V"}, {"nme": "newClassReader", "acc": 128, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;[Ljdk/internal/classfile/Classfile$Option;)Ljdk/internal/classfile/ClassModel;"}, {"nme": "newClassReader", "acc": 132, "dsc": "(Lja<PERSON>/lang/String;[B[Ljdk/internal/classfile/Classfile$Option;)Ljdk/internal/classfile/ClassModel;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getUsage", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getArgumentsDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getMessage", "acc": 132, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "description"}, {"acc": 24, "nme": "USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "usage"}, {"acc": 26, "nme": "standardPluginsBundle", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 18, "nme": "pluginsBundle", "dsc": "Ljava/util/ResourceBundle;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jlink/internal/PerfectHashBuilder$Bucket.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/PerfectHashBuilder$Bucket", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "add", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;)V", "sig": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;)V"}, {"nme": "getSize", "acc": 0, "dsc": "()I"}, {"nme": "getList", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;>;"}, {"nme": "get<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry;", "sig": "()Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "compareTo", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket;)I", "sig": "(Ljdk/tools/jlink/internal/PerfectHashBuilder$Bucket<TE;>;)I"}, {"nme": "compareTo", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "list", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/PerfectHashBuilder$Entry<TE;>;>;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/ImagePluginStack$CheckOrderResourcePoolManager.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImagePluginStack$CheckOrderResourcePoolManager", "super": "jdk/tools/jlink/internal/ResourcePoolManager", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/nio/ByteOrder;Ljava/util/List;Ljdk/tools/jlink/internal/StringTable;)V", "sig": "(Ljava/nio/ByteOrder;Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}], "flds": [{"acc": 18, "nme": "orderedList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"acc": 2, "nme": "currentIndex", "dsc": "I"}]}, "classes/jdk/tools/jlink/internal/plugins/AddResourcePlugin.class": {"ver": 65, "acc": 1056, "nme": "jdk/tools/jlink/internal/plugins/AddResourcePlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "hasRawArgument", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": [{"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jimage/JImageTask.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jimage/JImageTask", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "setLog", "acc": 0, "dsc": "(Ljava/io/PrintWriter;)V"}, {"nme": "pad", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;IZ)Ljava/lang/String;"}, {"nme": "pad", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"nme": "pad", "acc": 2, "dsc": "(JI)Ljava/lang/String;"}, {"nme": "run", "acc": 0, "dsc": "([<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "processInclude", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "listTitle", "acc": 2, "dsc": "(Ljava/io/File;Ljdk/internal/jimage/BasicImageReader;)V"}, {"nme": "extract", "acc": 2, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljdk/internal/jimage/ImageLocation;)V", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "trimModule", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "print", "acc": 2, "dsc": "(L<PERSON><PERSON>/lang/String;Ljdk/internal/jimage/ImageLocation;)V"}, {"nme": "print", "acc": 2, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;)V"}, {"nme": "info", "acc": 2, "dsc": "(Ljava/io/File;Ljdk/internal/jimage/BasicImageReader;)V", "exs": ["java/io/IOException"]}, {"nme": "listModule", "acc": 2, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "list", "acc": 2, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljdk/internal/jimage/ImageLocation;)V"}, {"nme": "verify", "acc": 0, "dsc": "(Ljdk/internal/jimage/BasicImageReader;Ljava/lang/String;Ljdk/internal/jimage/ImageLocation;)V"}, {"nme": "iterate", "acc": 2, "dsc": "(Ljdk/tools/jimage/JImageTask$JImageAction;Ljdk/tools/jimage/JImageTask$ModuleAction;Ljdk/tools/jimage/JImageTask$ResourceAction;)V", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "run", "acc": 2, "dsc": "()Z", "exs": ["java/lang/Exception", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$verify$11", "acc": 4106, "dsc": "(Ljdk/internal/classfile/ClassElement;)V"}, {"nme": "lambda$verify$10", "acc": 4106, "dsc": "(Ljdk/internal/classfile/MethodElement;)V"}, {"nme": "lambda$verify$9", "acc": 4106, "dsc": "(Ljdk/internal/classfile/CodeElement;)V"}, {"nme": "lambda$processInclude$8", "acc": 4106, "dsc": "(Ljava/nio/file/PathMatcher;Ljava/lang/String;)Z"}, {"nme": "lambda$run$7", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$Option;)V"}, {"nme": "lambda$run$6", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$Option;)Z"}, {"nme": "lambda$static$5", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(Ljdk/tools/jimage/JImageTask;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "RECOGNIZED_OPTIONS", "dsc": "[Ljdk/tools/jlink/internal/TaskHelper$Option;", "sig": "[Ljdk/tools/jlink/internal/TaskHelper$Option<*>;"}, {"acc": 26, "nme": "TASK_HELPER", "dsc": "Ljdk/tools/jlink/internal/TaskHelper;"}, {"acc": 26, "nme": "OPTION_HELPER", "dsc": "Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper;", "sig": "Ljdk/tools/jlink/internal/TaskHelper$OptionsHelper<Ljdk/tools/jimage/JImageTask;>;"}, {"acc": 26, "nme": "PROGNAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jimage"}, {"acc": 26, "nme": "JRT_FILE_SYSTEM", "dsc": "Ljava/nio/file/FileSystem;"}, {"acc": 18, "nme": "options", "dsc": "Ljdk/tools/jimage/JImageTask$OptionsValues;"}, {"acc": 18, "nme": "includePredicates", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/function/Predicate<Ljava/lang/String;>;>;"}, {"acc": 2, "nme": "log", "dsc": "Ljava/io/PrintWriter;"}, {"acc": 26, "nme": "EXIT_OK", "dsc": "I", "val": 0}, {"acc": 26, "nme": "EXIT_ERROR", "dsc": "I", "val": 1}, {"acc": 26, "nme": "EXIT_CMDERR", "dsc": "I", "val": 2}, {"acc": 26, "nme": "EXIT_ABNORMAL", "dsc": "I", "val": 4}, {"acc": 26, "nme": "OFFSET_WIDTH", "dsc": "I", "val": 12}, {"acc": 26, "nme": "SIZE_WIDTH", "dsc": "I", "val": 10}, {"acc": 26, "nme": "COMPRESSEDSIZE_WIDTH", "dsc": "I", "val": 10}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$Tree.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$Tree", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "buildTree", "acc": 2, "dsc": "()V"}, {"nme": "toResourceName", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)Ljava/lang/String;"}, {"nme": "getModule", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)Ljava/lang/String;"}, {"nme": "toPackageName", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)Ljava/lang/String;"}, {"nme": "removeRadical", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)Ljava/lang/String;"}, {"nme": "removeRadical", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getRoot", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}, {"nme": "getMap", "acc": 1, "dsc": "()Ljava/util/Map;", "sig": "()Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;>;"}], "flds": [{"acc": 18, "nme": "directAccess", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;>;"}, {"acc": 18, "nme": "paths", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 18, "nme": "root", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}, {"acc": 2, "nme": "modules", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}, {"acc": 2, "nme": "packages", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}]}, "classes/jdk/tools/jlink/internal/TaskHelper$PluginsHelper.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/TaskHelper$PluginsHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "argListFor", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Ljava/util/List;", "sig": "(Ljdk/tools/jlink/plugin/Plugin;)Ljava/util/List<Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;>;"}, {"nme": "addEmptyArgumentMap", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)V"}, {"nme": "addArgumentMap", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Ljava/util/Map;", "sig": "(Ljdk/tools/jlink/plugin/Plugin;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"nme": "addOrderedPluginOptions", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljava/util/Set;)V", "sig": "(Ljdk/tools/jlink/plugin/Plugin;Ljava/util/Set<Ljava/lang/String;>;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "getOption", "acc": 2, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/TaskHelper$PluginOption;", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "getPluginsConfig", "acc": 2, "dsc": "(Ljava/nio/file/Path;Ljava/util/Map;)Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;", "sig": "(Ljava/nio/file/Path;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;)Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;", "exs": ["java/io/IOException", "jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$addOrderedPluginOptions$7", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$addOrderedPluginOptions$6", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$addOrderedPluginOptions$5", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$addOrderedPluginOptions$4", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$addOrderedPluginOptions$3", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljava/lang/String;Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$new$2", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$new$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "lambda$new$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$PluginsHelper;Ljava/lang/String;Ljava/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}], "flds": [{"acc": 26, "nme": "STRIP_NATIVE_DEBUG_SYMBOLS_NAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "strip-native-debug-symbols"}, {"acc": 2, "nme": "pluginsLayer", "dsc": "<PERSON><PERSON><PERSON>/lang/<PERSON><PERSON>le<PERSON>ayer;"}, {"acc": 18, "nme": "plugins", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"acc": 2, "nme": "lastSorter", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "listPlugins", "dsc": "Z"}, {"acc": 18, "nme": "pluginToMaps", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/tools/jlink/plugin/Plugin;Ljava/util/List<Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;>;>;"}, {"acc": 18, "nme": "pluginsOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/TaskHelper$PluginOption;>;"}, {"acc": 18, "nme": "mainOptions", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/TaskHelper$PluginOption;>;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/TaskHelper;"}]}, "classes/jdk/tools/jlink/internal/plugins/PluginsResourceBundle.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/PluginsResourceBundle", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "getArgument", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;"}, {"nme": "getDescription", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getUsage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getOption", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "getMessage", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/String;", "exs": ["java/util/MissingResourceException"]}, {"nme": "getMessage", "acc": 137, "dsc": "(<PERSON><PERSON><PERSON>/util/ResourceBundle;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;", "exs": ["java/util/MissingResourceException"]}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "DESCRIPTION", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "description"}, {"acc": 24, "nme": "ARGUMENT", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "argument"}, {"acc": 24, "nme": "USAGE", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "usage"}, {"acc": 26, "nme": "pluginsBundle", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/tools/jlink/internal/Archive.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/Archive", "super": "java/lang/Object", "mthds": [{"nme": "moduleName", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1025, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "entries", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/internal/Archive$Entry;>;"}, {"nme": "open", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1025, "dsc": "()V", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/tools/jlink/internal/ExecutableImage.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/ExecutableImage", "super": "java/lang/Object", "mthds": [{"nme": "getHome", "acc": 1025, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getModules", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "getExecutionArgs", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "storeLaunchArgs", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(L<PERSON><PERSON>/util/List<Ljava/lang/String;>;)V"}, {"nme": "getTargetPlatform", "acc": 1025, "dsc": "()Ljdk/tools/jlink/internal/Platform;"}], "flds": []}, "classes/jdk/tools/jlink/internal/TaskHelper$Option.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/TaskHelper$Option", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;ZLjava/lang/String;Ljava/lang/String;Z)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;ZLjava/lang/String;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;Ljava/lang/String;Ljava/lang/String;Z)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;Ljava/lang/String;Ljava/lang/String;Z)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;Ljava/lang/String;Ljava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;ZLjava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;ZLjava/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing;Ljava/lang/String;)V", "sig": "(ZLjdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;Ljava/lang/String;)V"}, {"nme": "isHidden", "acc": 1, "dsc": "()Z"}, {"nme": "isTerminal", "acc": 1, "dsc": "()Z"}, {"nme": "matches", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "ignoreRest", "acc": 1, "dsc": "()Z"}, {"nme": "process", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(TT;Lja<PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "resourceName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getShortname", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "resourcePrefix", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "compareTo", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": [{"acc": 16, "nme": "<PERSON><PERSON><PERSON>", "dsc": "Z"}, {"acc": 16, "nme": "processing", "dsc": "Ljdk/tools/jlink/internal/TaskHelper$Option$Processing;", "sig": "Ljdk/tools/jlink/internal/TaskHelper$Option$Processing<TT;>;"}, {"acc": 16, "nme": "hidden", "dsc": "Z"}, {"acc": 16, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "shortname", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "shortname2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 16, "nme": "terminalOption", "dsc": "Z"}]}, "classes/jdk/tools/jlink/plugin/ResourcePoolEntry$Type.class": {"ver": 65, "acc": 16433, "nme": "jdk/tools/jlink/plugin/ResourcePoolEntry$Type", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "CLASS_OR_RESOURCE", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "CONFIG", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "HEADER_FILE", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "LEGAL_NOTICE", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "MAN_PAGE", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "NATIVE_CMD", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "NATIVE_LIB", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 16409, "nme": "TOP", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}]}, "classes/jdk/tools/jlink/internal/ImagePluginStack$LastPoolManager$LastModule.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImagePluginStack$LastPoolManager$LastModule", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack$LastPoolManager;Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "findEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "descriptor", "acc": 1, "dsc": "()Ljava/lang/module/ModuleDescriptor;"}, {"nme": "targetPlatform", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "initModuleAttributes", "acc": 2, "dsc": "()V"}, {"nme": "packages", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1, "dsc": "()I"}, {"nme": "lambda$entries$0", "acc": 4098, "dsc": "(Ljava/util/List;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}], "flds": [{"acc": 16, "nme": "module", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolModule;"}, {"acc": 0, "nme": "descriptor", "dsc": "Ljava/lang/module/ModuleDescriptor;"}, {"acc": 0, "nme": "target", "dsc": "Ljdk/internal/module/ModuleTarget;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/ImagePluginStack$LastPoolManager;"}]}, "classes/jdk/tools/jlink/resources/jlink_de.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/jlink_de", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ResourcePrevisitor.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/ResourcePrevisitor", "super": "java/lang/Object", "mthds": [{"nme": "previsit", "acc": 1025, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/internal/StringTable;)V"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImageLocationWriter.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImageLocationWriter", "super": "jdk/internal/jimage/ImageLocation", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/ImageStringsWriter;)V"}, {"nme": "writeTo", "acc": 0, "dsc": "(Ljdk/internal/jimage/ImageStream;)V"}, {"nme": "addAttribute", "acc": 2, "dsc": "(IJ)Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"nme": "addAttribute", "acc": 2, "dsc": "(ILjava/lang/String;)Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"nme": "newLocation", "acc": 8, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/internal/ImageStringsWriter;JJJ)Ljdk/tools/jlink/internal/ImageLocationWriter;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "hashCode", "acc": 0, "dsc": "(I)I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "getLocationOffset", "acc": 0, "dsc": "()I"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 2, "nme": "locationOffset", "dsc": "I"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$NativePluginFactory;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": [{"acc": 26, "nme": "STRIP_NATIVE_DEBUG_PLUGIN", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "strip-native-debug-symbols"}, {"acc": 26, "nme": "EXCLUDE_DEBUGINFO", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "exclude-debuginfo-files"}, {"acc": 18, "nme": "javaStripPlugin", "dsc": "Ljdk/tools/jlink/plugin/Plugin;"}, {"acc": 18, "nme": "stripNativePluginFactory", "dsc": "Ljdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$NativePluginFactory;"}]}, "classes/jdk/tools/jlink/internal/ArchiveEntryResourcePoolEntry$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/tools/jlink/internal/ArchiveEntryResourcePoolEntry$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$tools$jlink$internal$Archive$Entry$EntryType", "dsc": "[I"}]}, "classes/jdk/tools/jlink/internal/Jlink$JlinkConfiguration.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/Jlink$JlinkConfiguration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Lja<PERSON>/nio/file/Path;Ljava/util/Set;Ljava/nio/ByteOrder;Lja<PERSON>/lang/module/ModuleFinder;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Set<Ljava/lang/String;>;Ljava/nio/ByteOrder;Ljava/lang/module/ModuleFinder;)V"}, {"nme": "getByteOrder", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "getOutput", "acc": 1, "dsc": "()Ljava/nio/file/Path;"}, {"nme": "getModules", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "finder", "acc": 1, "dsc": "()Ljava/lang/module/ModuleFinder;"}, {"nme": "resolveAndBind", "acc": 1, "dsc": "()Ljava/lang/module/Configuration;"}, {"nme": "resolve", "acc": 1, "dsc": "()Ljava/lang/module/Configuration;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "output", "dsc": "Ljava/nio/file/Path;"}, {"acc": 18, "nme": "modules", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "endian", "dsc": "<PERSON><PERSON><PERSON>/nio/ByteOrder;"}, {"acc": 18, "nme": "finder", "dsc": "<PERSON><PERSON><PERSON>/lang/module/ModuleFinder;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolConfiguration$2.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolConfiguration$2", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/Map;Ljava/util/Set;)V", "sig": "()V"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/module/ModuleReference;>;"}, {"nme": "findAll", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/module/ModuleReference;>;"}], "flds": [{"acc": 4112, "nme": "val$nameToModRef", "dsc": "Ljava/util/Map;"}, {"acc": 4112, "nme": "val$allRefs", "dsc": "<PERSON><PERSON><PERSON>/util/Set;"}]}, "classes/jdk/tools/jlink/internal/plugins/VendorVMBugURLPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/VendorVMBugURLPlugin", "super": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 4161, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "configure", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "hasRawArgument", "acc": 4161, "dsc": "()Z"}, {"nme": "hasArguments", "acc": 4161, "dsc": "()Z"}, {"nme": "getType", "acc": 4161, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$DedupSetBuilder.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$DedupSetBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/function/IntSupplier;)V"}, {"nme": "stringSet", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)V"}, {"nme": "exportsModifiers", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Exports$Modifier;>;)V"}, {"nme": "opensModifiers", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Opens$Modifier;>;)V"}, {"nme": "requiresModifiers", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Requires$Modifier;>;)V"}, {"nme": "indexOfStringSet", "acc": 0, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)I", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/String;>;)I"}, {"nme": "indexOfExportsModifiers", "acc": 0, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)I", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Exports$Modifier;>;)I"}, {"nme": "indexOfOpensModifiers", "acc": 0, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)I", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Opens$Modifier;>;)I"}, {"nme": "indexOfRequiresModifiers", "acc": 0, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)I", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Requires$Modifier;>;)I"}, {"nme": "newStringSet", "acc": 0, "dsc": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set;)I", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/util/Set<Ljava/lang/String;>;)I"}, {"nme": "lambda$requiresModifiers$3", "acc": 4098, "dsc": "(Ljava/util/Set;)Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder;"}, {"nme": "lambda$opensModifiers$2", "acc": 4098, "dsc": "(Ljava/util/Set;)Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder;"}, {"nme": "lambda$exportsModifiers$1", "acc": 4098, "dsc": "(Ljava/util/Set;)Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder;"}, {"nme": "lambda$stringSet$0", "acc": 4098, "dsc": "(Ljava/util/Set;)Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$SetBuilder;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16, "nme": "stringSets", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/Set<Ljava/lang/String;>;Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$SetBuilder<Ljava/lang/String;>;>;"}, {"acc": 16, "nme": "requiresModifiersSets", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Requires$Modifier;>;Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder<Ljava/lang/module/ModuleDescriptor$Requires$Modifier;>;>;"}, {"acc": 16, "nme": "exportsModifiersSets", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Exports$Modifier;>;Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder<Ljava/lang/module/ModuleDescriptor$Exports$Modifier;>;>;"}, {"acc": 16, "nme": "opensModifiersSets", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Opens$Modifier;>;Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder<Ljava/lang/module/ModuleDescriptor$Opens$Modifier;>;>;"}, {"acc": 18, "nme": "stringSetVar", "dsc": "I"}, {"acc": 18, "nme": "enumSetVar", "dsc": "I"}, {"acc": 18, "nme": "localVarSupplier", "dsc": "Ljava/util/function/IntSupplier;"}, {"acc": 4120, "nme": "$assertionsDisabled", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolBuilderImpl.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolBuilderImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;)V"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "build", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": [{"acc": 2, "nme": "built", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/ResourcePoolManager;"}]}, "classes/jdk/tools/jlink/plugin/ResourcePoolBuilder.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/ResourcePoolBuilder", "super": "java/lang/Object", "mthds": [{"nme": "add", "acc": 1025, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "build", "acc": 1025, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": []}, "classes/jdk/tools/jlink/resources/jlink_zh_CN.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/resources/jlink_zh_CN", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$JmodFileWriter$JarEntryConsumer.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodFileWriter$JarEntryConsumer", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask$JmodFileWriter;Ljdk/tools/jmod/JmodOutputStream;Ljava/util/jar/JarFile;)V"}, {"nme": "accept", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarEntry;)V"}, {"nme": "test", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/util/jar/JarEntry;)Z"}, {"nme": "accept", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}, {"nme": "test", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 16, "nme": "out", "dsc": "Ljdk/tools/jmod/JmodOutputStream;"}, {"acc": 16, "nme": "jarfile", "dsc": "<PERSON><PERSON><PERSON>/util/jar/JarFile;"}, {"acc": 4112, "nme": "this$1", "dsc": "Ljdk/tools/jmod/JmodTask$JmodFileWriter;"}]}, "classes/jdk/tools/jlink/plugin/PluginException.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/plugin/PluginException", "super": "java/lang/RuntimeException", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Throwable;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Throwable;)V"}], "flds": [{"acc": 26, "nme": "serialVersionUID", "dsc": "J", "val": 7117982019443100395}]}, "classes/jdk/tools/jlink/internal/ImagePluginStack$LastPoolManager.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImagePluginStack$LastPoolManager", "super": "jdk/tools/jlink/internal/ResourcePoolManager", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "add", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "findModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "modules", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "moduleCount", "acc": 1, "dsc": "()I"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1, "dsc": "()I"}, {"nme": "findEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "findEntryInContext", "acc": 1, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}, {"nme": "isEmpty", "acc": 1, "dsc": "()Z"}, {"nme": "byteOrder", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/nio/Byte<PERSON>r;"}, {"nme": "getUncompressed", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$getUncompressed$2", "acc": 4098, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$entries$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$modules$0", "acc": 4098, "dsc": "(Ljava/util/List;Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}], "flds": [{"acc": 18, "nme": "pool", "dsc": "Ljdk/tools/jlink/plugin/ResourcePool;"}, {"acc": 0, "nme": "decompressor", "dsc": "Ljdk/internal/jimage/decompressor/Decompressor;"}, {"acc": 0, "nme": "content", "dsc": "Ljava/util/Collection;", "sig": "Ljava/util/Collection<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/IncludeLocalesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/IncludeLocalesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "previsit", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "includeLocaleFilePatterns", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "includeLocaleFiles", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "stripUnsupportedLocales", "acc": 2, "dsc": "([B)Z"}, {"nme": "filterOutUnsupportedTags", "acc": 2, "dsc": "([B)Z"}, {"nme": "filterLocales", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljava/util/Locale;>;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "tagToLocale", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Locale;"}, {"nme": "localeToTags", "acc": 10, "dsc": "(Ljava/util/Locale;)Ljava/util/stream/Stream;", "sig": "(Ljava/util/Locale;)Ljava/util/stream/Stream<Ljava/lang/String;>;"}, {"nme": "lambda$filterLocales$18", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;Ljava/util/Locale;)Ljava/util/Optional;"}, {"nme": "lambda$filterLocales$17", "acc": 4106, "dsc": "(Ljava/util/Locale;Ljava/util/Locale;)Z"}, {"nme": "lambda$filterLocales$16", "acc": 4106, "dsc": "(Ljava/util/Locale;)Ljava/util/stream/Stream;"}, {"nme": "lambda$filterOutUnsupportedTags$15", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$stripUnsupportedLocales$14", "acc": 4106, "dsc": "([BI)I"}, {"nme": "lambda$includeLocaleFiles$13", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$previsit$12", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "lambda$previsit$11", "acc": 4098, "dsc": "(Lja<PERSON>/lang/String;)Ljava/util/stream/Stream;"}, {"nme": "lambda$previsit$10", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/regex/Matcher;)Lja<PERSON>/lang/String;"}, {"nme": "lambda$previsit$9", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/regex/Matcher;)Z"}, {"nme": "lambda$previsit$8", "acc": 4106, "dsc": "(Ljava/util/regex/Pattern;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/regex/Matcher;"}, {"nme": "lambda$previsit$7", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$transform$6", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$transform$5", "acc": 4106, "dsc": "(Ljdk/internal/classfile/constantpool/ClassEntry;)Z"}, {"nme": "lambda$static$4", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/List;<PERSON><PERSON><PERSON>/util/List;)<PERSON>java/util/List;"}, {"nme": "lambda$static$3", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Ljava/util/AbstractMap$SimpleEntry;"}, {"nme": "lambda$static$2", "acc": 4106, "dsc": "(Lja<PERSON>/lang/String;)Ljava/util/stream/Stream;"}, {"nme": "lambda$static$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$static$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 26, "nme": "MODULENAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "jdk.localedata"}, {"acc": 26, "nme": "LOCALEDATA_PACKAGES", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 26, "nme": "METAINFONAME", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "LocaleDataMetaInfo"}, {"acc": 26, "nme": "META_FILES", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 26, "nme": "INCLUDE_LOCALE_FILES", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 2, "nme": "predicate", "dsc": "<PERSON><PERSON><PERSON>/util/function/Predicate;", "sig": "Ljava/util/function/Predicate<Ljava/lang/String;>;"}, {"acc": 2, "nme": "userParam", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "priorityList", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/Locale$LanguageRange;>;"}, {"acc": 2, "nme": "available", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/util/Locale;>;"}, {"acc": 2, "nme": "filtered", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}, {"acc": 26, "nme": "CLDR_ADAPTER", "dsc": "Lsun/util/locale/provider/ResourceBundleBasedAdapter;"}, {"acc": 26, "nme": "CLDR_PARENT_LOCALES", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/util/Locale;[Ljava/lang/String;>;"}, {"acc": 26, "nme": "EQUIV_MAP", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljava/lang/String;>;>;"}, {"acc": 26, "nme": "jaJPJPTag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "ja-<PERSON><PERSON><PERSON>"}, {"acc": 26, "nme": "noNONYTag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "no-NO-NY"}, {"acc": 26, "nme": "thTHTHTag", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "th-TH-TH"}, {"acc": 26, "nme": "jaJPJP", "dsc": "Ljava/util/Locale;"}, {"acc": 26, "nme": "noNONY", "dsc": "Ljava/util/Locale;"}, {"acc": 26, "nme": "thTHTH", "dsc": "Ljava/util/Locale;"}, {"acc": 26, "nme": "LOCALE_BUILDER", "dsc": "Ljava/util/Locale$Builder;"}]}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$EnumSetBuilder", "super": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$SetBuilder", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(L<PERSON><PERSON>/util/Set;Ljava/lang/constant/ClassDesc;ILjava/util/function/IntSupplier;)V", "sig": "(Ljava/util/Set<TT;>;Ljava/lang/constant/ClassDesc;ILjava/util/function/IntSupplier;)V"}, {"nme": "visitElement", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/Comparable;Ljdk/internal/classfile/CodeBuilder;)V", "sig": "(TT;Ljdk/internal/classfile/CodeBuilder;)V"}], "flds": [{"acc": 18, "nme": "classDesc", "dsc": "Ljava/lang/constant/ClassDesc;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolModuleImpl.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolModuleImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "findEntry", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "descriptor", "acc": 1, "dsc": "()Ljava/lang/module/ModuleDescriptor;"}, {"nme": "targetPlatform", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "initModuleAttributes", "acc": 2, "dsc": "()V"}, {"nme": "packages", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "entries", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1, "dsc": "()I"}, {"nme": "lambda$packages$1", "acc": 4106, "dsc": "(Ljava/util/Set;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$packages$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Z"}], "flds": [{"acc": 16, "nme": "moduleContent", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"acc": 2, "nme": "descriptor", "dsc": "Ljava/lang/module/ModuleDescriptor;"}, {"acc": 2, "nme": "target", "dsc": "Ljdk/internal/module/ModuleTarget;"}, {"acc": 16, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}]}, "classes/jdk/tools/jlink/plugin/ResourcePoolModule.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/ResourcePoolModule", "super": "java/lang/Object", "mthds": [{"nme": "name", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "findEntry", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "descriptor", "acc": 1025, "dsc": "()Ljava/lang/module/ModuleDescriptor;"}, {"nme": "targetPlatform", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "packages", "acc": 1025, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "entries", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolEntry;>;"}, {"nme": "entryCount", "acc": 1025, "dsc": "()I"}], "flds": []}, "classes/jdk/tools/jlink/internal/Archive$Entry$EntryType.class": {"ver": 65, "acc": 16433, "nme": "jdk/tools/jlink/internal/Archive$Entry$EntryType", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "MODULE_NAME", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "CLASS_OR_RESOURCE", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "CONFIG", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "NATIVE_LIB", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "NATIVE_CMD", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "HEADER_FILE", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "LEGAL_NOTICE", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "MAN_PAGE", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 16409, "nme": "SERVICE", "dsc": "Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jlink/internal/Archive$Entry$EntryType;"}]}, "classes/jdk/tools/jlink/internal/AbstractResourcePoolEntry.class": {"ver": 65, "acc": 1056, "nme": "jdk/tools/jlink/internal/AbstractResourcePoolEntry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;)V"}, {"nme": "moduleName", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "path", "acc": 17, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "type", "acc": 17, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "toString", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 18, "nme": "path", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "module", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "type", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;"}]}, "classes/jdk/tools/jlink/internal/Utils$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/Utils$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "compare", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljdk/tools/jlink/plugin/Plugin;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$PathMatcherConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$PathMatcherConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/PathMatcher;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/nio/file/PathMatcher;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/AddOptionsPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/AddOptionsPlugin", "super": "jdk/tools/jlink/internal/plugins/AddResourcePlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 4161, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "configure", "acc": 4161, "dsc": "(Ljava/util/Map;)V"}, {"nme": "hasRawArgument", "acc": 4161, "dsc": "()Z"}, {"nme": "hasArguments", "acc": 4161, "dsc": "()Z"}, {"nme": "getType", "acc": 4161, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}], "flds": []}, "classes/jdk/tools/jlink/plugin/ResourcePoolModuleView.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/plugin/ResourcePoolModuleView", "super": "java/lang/Object", "mthds": [{"nme": "findModule", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "findModule", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional;", "sig": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "modules", "acc": 1025, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "moduleCount", "acc": 1025, "dsc": "()I"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$NativePluginFactory.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$NativePluginFactory", "super": "java/lang/Object", "mthds": [{"nme": "create", "acc": 1025, "dsc": "()Ljdk/tools/jlink/plugin/Plugin;"}], "flds": []}, "classes/jdk/tools/jlink/internal/JmodArchive$JmodEntry.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/JmodArchive$JmodEntry", "super": "jdk/tools/jlink/internal/Archive$Entry", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/JmodArchive;Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/internal/Archive$Entry$EntryType;Ljdk/internal/jmod/JmodFile$Entry;)V"}, {"nme": "size", "acc": 1, "dsc": "()J"}, {"nme": "stream", "acc": 1, "dsc": "()Ljava/io/InputStream;", "exs": ["java/io/IOException"]}], "flds": [{"acc": 18, "nme": "entry", "dsc": "Ljdk/internal/jmod/JmodFile$Entry;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/JmodArchive;"}]}, "classes/jdk/tools/jlink/internal/PerfectHashBuilder$Entry.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/PerfectHashBuilder$Entry", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/Object;)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;TE;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getValue", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/Object;", "sig": "()TE;"}, {"nme": "hashCode", "acc": 0, "dsc": "(I)I"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "flds": [{"acc": 18, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/Object;", "sig": "TE;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$CompressedModuleData.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$CompressedModuleData", "super": "jdk/tools/jlink/internal/ByteArrayResourcePoolEntry", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;[BJ)V"}, {"nme": "getUncompressedSize", "acc": 1, "dsc": "()J"}, {"nme": "equals", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 1, "dsc": "()I"}, {"nme": "contentLength", "acc": 4161, "dsc": "()J"}, {"nme": "write", "acc": 4161, "dsc": "(Ljava/io/OutputStream;)V"}, {"nme": "content", "acc": 4161, "dsc": "()Ljava/io/InputStream;"}, {"nme": "contentBytes", "acc": 4161, "dsc": "()[B"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}], "flds": [{"acc": 16, "nme": "uncompressed_size", "dsc": "J"}]}, "classes/jdk/tools/jlink/internal/Jlink.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/Jlink", "super": "java/lang/Object", "mthds": [{"nme": "newPlugin", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;Ljava/util/Map;Ljava/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;", "sig": "(Ljava/lang/String;Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;Ljava/lang/ModuleLayer;)Ljdk/tools/jlink/plugin/Plugin;"}, {"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "build", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;)V"}, {"nme": "build", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/Jlink$JlinkConfiguration;Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;)V"}, {"nme": "addAutoEnabledPlugins", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;)Ljdk/tools/jlink/internal/Jlink$PluginsConfiguration;"}], "flds": []}, "classes/jdk/tools/jmod/JmodTask$ModuleVersionConverter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$ModuleVersionConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(Ljava/lang/String;)Ljava/lang/module/ModuleDescriptor$Version;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/lang/module/ModuleDescriptor$Version;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/JlinkTask$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/JlinkTask$1", "super": "java/nio/file/SimpleFileVisitor", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "visitFile", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "postVisitDirectory", "acc": 1, "dsc": "(Ljava/nio/file/Path;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "postVisitDirectory", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;Ljava/io/IOException;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}, {"nme": "visitFile", "acc": 4161, "dsc": "(Lja<PERSON>/lang/Object;Ljava/nio/file/attribute/BasicFileAttributes;)Ljava/nio/file/FileVisitResult;", "exs": ["java/io/IOException"]}], "flds": []}, "classes/jdk/tools/jlink/internal/JmodArchive$1.class": {"ver": 65, "acc": 4128, "nme": "jdk/tools/jlink/internal/JmodArchive$1", "super": "java/lang/Object", "mthds": [{"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 4120, "nme": "$SwitchMap$jdk$internal$jmod$JmodFile$Section", "dsc": "[I"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm.class": {"ver": 65, "acc": 16432, "nme": "jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"nme": "getName", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getEfficience", "acc": 2, "dsc": "()I"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "SERVER", "dsc": "Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"acc": 16409, "nme": "CLIENT", "dsc": "Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"acc": 16409, "nme": "MINIMAL", "dsc": "Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}, {"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "efficience", "dsc": "I"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;"}]}, "classes/jdk/tools/jimage/JImageTask$OptionsValues.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jimage/JImageTask$OptionsValues", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "task", "dsc": "Ljdk/tools/jimage/JImageTask$Task;"}, {"acc": 0, "nme": "directory", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "include", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "fullVersion", "dsc": "Z"}, {"acc": 0, "nme": "help", "dsc": "Z"}, {"acc": 0, "nme": "verbose", "dsc": "Z"}, {"acc": 0, "nme": "version", "dsc": "Z"}, {"acc": 0, "nme": "jimages", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/io/File;>;"}]}, "classes/jdk/tools/jlink/internal/TaskHelper$BadArgs.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/TaskHelper$BadArgs", "super": "java/lang/Exception", "mthds": [{"nme": "<init>", "acc": 130, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper;Ljava/lang/String;[Ljava/lang/Object;)V"}, {"nme": "showUsage", "acc": 1, "dsc": "(Z)Ljdk/tools/jlink/internal/TaskHelper$BadArgs;"}], "flds": [{"acc": 24, "nme": "serialVersionUID", "dsc": "J", "val": 8765093759964640721}, {"acc": 17, "nme": "key", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 17, "nme": "args", "dsc": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"acc": 1, "nme": "showUsage", "dsc": "Z"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/TaskHelper;"}]}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/util/Map;)V", "sig": "()V"}, {"nme": "find", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/Optional<Ljava/lang/module/ModuleReference;>;"}, {"nme": "findAll", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Ljava/util/Set<Ljava/lang/module/ModuleReference;>;"}], "flds": [{"acc": 4112, "nme": "val$namesToReference", "dsc": "Ljava/util/Map;"}]}, "classes/jdk/tools/jmod/JmodTask$JmodHelpFormatter$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$JmodHelpFormatter$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jmod/JmodTask$JmodHelpFormatter;)V"}, {"nme": "options", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "description", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "defaultValues", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<*>;"}, {"nme": "isRequired", "acc": 1, "dsc": "()Z"}, {"nme": "acceptsArguments", "acc": 1, "dsc": "()Z"}, {"nme": "requiresArgument", "acc": 1, "dsc": "()Z"}, {"nme": "argumentDescription", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "argumentTypeIndicator", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "representsNonOptions", "acc": 1, "dsc": "()Z"}], "flds": []}, "classes/jdk/tools/jlink/internal/StringTable.class": {"ver": 65, "acc": 1537, "nme": "jdk/tools/jlink/internal/StringTable", "super": "java/lang/Object", "mthds": [{"nme": "addString", "acc": 1025, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getString", "acc": 1025, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ResourcePoolConfiguration.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ResourcePoolConfiguration", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "descriptorOf", "acc": 10, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljava/lang/module/ModuleDescriptor;"}, {"nme": "moduleReference", "acc": 10, "dsc": "(Lja<PERSON>/lang/module/ModuleDescriptor;)Ljava/lang/module/ModuleReference;"}, {"nme": "allModRefs", "acc": 10, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)Ljava/util/Map;", "sig": "(Ljdk/tools/jlink/plugin/ResourcePool;)Ljava/util/Map<Ljava/lang/String;Ljava/lang/module/ModuleReference;>;"}, {"nme": "checkPackages", "acc": 10, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}, {"nme": "validate", "acc": 8, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)Ljava/lang/module/Configuration;"}, {"nme": "lambda$checkPackages$1", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "lambda$allModRefs$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljava/lang/module/ModuleReference;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/DefaultCompressPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/DefaultCompressPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "previsit", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/internal/StringTable;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}], "flds": [{"acc": 25, "nme": "FILTER", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "filter"}, {"acc": 25, "nme": "LEVEL_0", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "0"}, {"acc": 25, "nme": "LEVEL_1", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "1"}, {"acc": 25, "nme": "LEVEL_2", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "2"}, {"acc": 2, "nme": "ss", "dsc": "Ljdk/tools/jlink/internal/plugins/StringSharingPlugin;"}, {"acc": 2, "nme": "zip", "dsc": "Ljdk/tools/jlink/internal/plugins/ZipPlugin;"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$PackageNode.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$PackageNode", "super": "jdk/tools/jlink/internal/ImageResourcesTree$Node", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)V"}, {"nme": "addReference", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "validate", "acc": 2, "dsc": "()V"}], "flds": [{"acc": 18, "nme": "references", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$PackageNode$PackageReference;>;"}]}, "classes/jdk/tools/jlink/internal/Utils.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/Utils", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "parseList", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getSortedPlugins", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/util/List;)Ljava/util/List;", "sig": "(Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;)Ljava/util/List<Ljdk/tools/jlink/plugin/Plugin;>;"}, {"nme": "isFunctional", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Z"}, {"nme": "isAutoEnabled", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Z"}, {"nme": "isDisabled", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Z"}, {"nme": "isBuiltin", "acc": 9, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Z"}, {"nme": "jrtFileSystem", "acc": 9, "dsc": "()Ljava/nio/file/FileSystem;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 9, "dsc": "(Ljava/nio/file/FileSystem;Ljava/lang/String;)Ljava/nio/file/PathMatcher;"}, {"nme": "getJRTFSPathMatcher", "acc": 9, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/PathMatcher;"}, {"nme": "getJRTFSPath", "acc": 137, "dsc": "(Lja<PERSON>/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;"}, {"nme": "lambda$parseList$1", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$parseList$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 10, "nme": "JRT_FILE_SYSTEM", "dsc": "Ljava/nio/file/FileSystem;"}, {"acc": 26, "nme": "THIS_MODULE", "dsc": "<PERSON><PERSON><PERSON>/lang/Module;"}]}, "classes/jdk/tools/jlink/internal/SymLinkResourcePoolEntry.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jlink/internal/SymLinkResourcePoolEntry", "super": "jdk/tools/jlink/internal/AbstractResourcePoolEntry", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljava/lang/String;Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "contentLength", "acc": 1, "dsc": "()J"}, {"nme": "content", "acc": 1, "dsc": "()Ljava/io/InputStream;"}, {"nme": "linkedTarget", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "toString", "acc": 4161, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "equals", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"nme": "hashCode", "acc": 4161, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "target", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}]}, "classes/jdk/tools/jlink/internal/TaskHelper$OptionsHelper.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/TaskHelper$OptionsHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper;Ljava/util/List;)V", "sig": "(Ljava/util/List<Ljdk/tools/jlink/internal/TaskHelper$Option<TT;>;>;)V"}, {"nme": "shouldList<PERSON><PERSON>ins", "acc": 1, "dsc": "()Z"}, {"nme": "handleOptions", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;[<PERSON>ja<PERSON>/lang/String;)Ljava/util/List;", "sig": "(TT;[<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List<Ljava/lang/String;>;", "exs": ["jdk/tools/jlink/internal/TaskHelper$BadArgs"]}, {"nme": "getOption", "acc": 2, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/TaskHelper$Option;", "sig": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/TaskHelper$Option<TT;>;"}, {"nme": "showHelp", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "listPlugins", "acc": 1, "dsc": "()V"}, {"nme": "showPlugin", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;Ljava/io/PrintWriter;)V"}, {"nme": "getInputCommand", "acc": 0, "dsc": "()[<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getDefaults", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/<PERSON><PERSON>le<PERSON>ayer;"}, {"nme": "lambda$listPlugins$4", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)V"}, {"nme": "lambda$listPlugins$3", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/lang/<PERSON>;<PERSON><PERSON><PERSON>/lang/<PERSON>an;)I"}, {"nme": "lambda$listPlugins$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/Plugin;)Ljava/lang/<PERSON>;"}, {"nme": "lambda$showHelp$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$Option;)V"}, {"nme": "lambda$showHelp$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/TaskHelper$Option;)Z"}], "flds": [{"acc": 18, "nme": "options", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/internal/TaskHelper$Option<TT;>;>;"}, {"acc": 2, "nme": "command", "dsc": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "defaults", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/TaskHelper;"}]}, "classes/jdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/OrderResourcesPlugin$SortWrapper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;I)V"}, {"nme": "getResource", "acc": 0, "dsc": "()Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getOrdinal", "acc": 0, "dsc": "()I"}], "flds": [{"acc": 18, "nme": "resource", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"acc": 18, "nme": "ordinal", "dsc": "I"}]}, "classes/jdk/tools/jmod/JmodOutputStream.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodOutputStream", "super": "java/io/OutputStream", "mthds": [{"nme": "newOutputStream", "acc": 8, "dsc": "(Ljava/nio/file/Path;Ljava/time/LocalDateTime;I)Ljdk/tools/jmod/JmodOutputStream;", "exs": ["java/io/IOException"]}, {"nme": "<init>", "acc": 2, "dsc": "(Ljava/io/OutputStream;Ljava/time/LocalDateTime;I)V"}, {"nme": "writeEntry", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljdk/internal/jmod/JmodFile$Section;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "writeEntry", "acc": 1, "dsc": "([BLjdk/internal/jmod/JmodFile$Section;Ljava/lang/String;)V", "exs": ["java/io/IOException"]}, {"nme": "writeEntry", "acc": 1, "dsc": "(Ljava/io/InputStream;Ljdk/internal/jmod/JmodFile$Entry;)V", "exs": ["java/io/IOException"]}, {"nme": "newEntry", "acc": 2, "dsc": "(Ljdk/internal/jmod/JmodFile$Section;Ljava/lang/String;)Ljava/util/zip/ZipEntry;", "exs": ["java/io/IOException"]}, {"nme": "contains", "acc": 1, "dsc": "(Ljdk/internal/jmod/JmodFile$Section;Ljava/lang/String;)Z"}, {"nme": "write", "acc": 1, "dsc": "(I)V", "exs": ["java/io/IOException"]}, {"nme": "close", "acc": 1, "dsc": "()V", "exs": ["java/io/IOException"]}, {"nme": "lambda$contains$0", "acc": 4106, "dsc": "(Ljdk/internal/jmod/JmodFile$Section;)Ljava/util/Set;"}], "flds": [{"acc": 18, "nme": "entries", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/internal/jmod/JmodFile$Section;Ljava/util/Set<Ljava/lang/String;>;>;"}, {"acc": 18, "nme": "zos", "dsc": "Ljava/util/zip/ZipOutputStream;"}, {"acc": 18, "nme": "date", "dsc": "Ljava/time/LocalDateTime;"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludeJmodSectionPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/ExcludeJmodSectionPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "lambda$transform$0", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}], "flds": [{"acc": 25, "nme": "MAN_PAGES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "man"}, {"acc": 25, "nme": "INCLUDE_HEADER_FILES", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "headers"}, {"acc": 18, "nme": "filters", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljdk/tools/jlink/plugin/ResourcePoolEntry$Type;>;"}]}, "classes/jdk/tools/jmod/JmodTask$ResourceBundleHelper.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jmod/JmodTask$ResourceBundleHelper", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "bundle", "dsc": "Ljava/util/ResourceBundle;"}]}, "classes/jdk/tools/jmod/Main$JmodToolProvider.class": {"ver": 65, "acc": 33, "nme": "jdk/tools/jmod/Main$JmodToolProvider", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "name", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "description", "acc": 1, "dsc": "()Ljava/util/Optional;", "sig": "()Ljava/util/Optional<Ljava/lang/String;>;"}, {"nme": "run", "acc": 129, "dsc": "(Ljava/io/PrintWriter;Ljava/io/PrintWriter;[<PERSON>ja<PERSON>/lang/String;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$DefaultNativePluginFactory.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/DefaultStripDebugPlugin$DefaultNativePluginFactory", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "create", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin;"}], "flds": []}, "classes/jdk/tools/jmod/resources/jmod_ja.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jmod/resources/jmod_ja", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SaveJlinkArgfilesPlugin.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/plugins/SaveJlinkArgfilesPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "hasRawArgument", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "readArgfile", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}], "flds": [{"acc": 2, "nme": "<PERSON><PERSON><PERSON><PERSON>", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/internal/plugins/VersionPropsPlugin.class": {"ver": 65, "acc": 1056, "nme": "jdk/tools/jlink/internal/plugins/VersionPropsPlugin", "super": "jdk/tools/jlink/internal/plugins/AbstractPlugin", "mthds": [{"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<init>", "acc": 4, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "getType", "acc": 1, "dsc": "()Ljdk/tools/jlink/plugin/Plugin$Category;"}, {"nme": "hasArguments", "acc": 1, "dsc": "()Z"}, {"nme": "hasRawArgument", "acc": 1, "dsc": "()Z"}, {"nme": "configure", "acc": 1, "dsc": "(Ljava/util/Map;)V", "sig": "(Lja<PERSON>/util/Map<Ljava/lang/String;Ljava/lang/String;>;)V"}, {"nme": "redefine", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;[B)[B"}, {"nme": "transform", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;Ljdk/tools/jlink/plugin/ResourcePoolBuilder;)Ljdk/tools/jlink/plugin/ResourcePool;"}, {"nme": "lambda$transform$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolEntry;)Ljdk/tools/jlink/plugin/ResourcePoolEntry;"}, {"nme": "lambda$redefine$0", "acc": 4106, "dsc": "(Ljdk/internal/classfile/MethodModel;)Z"}], "flds": [{"acc": 26, "nme": "VERSION_PROPS_CLASS", "dsc": "<PERSON><PERSON><PERSON>/lang/String;", "val": "/java.base/java/lang/VersionProps.class"}, {"acc": 18, "nme": "field", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "value", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 2, "nme": "redefined", "dsc": "Z"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$Node.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$Node", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)V"}, {"nme": "<PERSON><PERSON><PERSON>", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "getName", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc": 1, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}, {"nme": "buildPath", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)Ljava/lang/String;"}], "flds": [{"acc": 18, "nme": "name", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 18, "nme": "children", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;>;"}, {"acc": 18, "nme": "parent", "dsc": "Ljdk/tools/jlink/internal/ImageResourcesTree$Node;"}, {"acc": 2, "nme": "loc", "dsc": "Ljdk/tools/jlink/internal/ImageLocationWriter;"}]}, "classes/jdk/tools/jlink/internal/ImageResourcesTree$ResourceNode.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImageResourcesTree$ResourceNode", "super": "jdk/tools/jlink/internal/ImageResourcesTree$Node", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/internal/ImageResourcesTree$Node;)V"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "([B<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "([<PERSON><PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)V", "exs": ["java/io/IOException"]}, {"nme": "moduleName", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "descriptor", "acc": 0, "dsc": "()Ljava/lang/module/ModuleDescriptor;"}, {"nme": "packages", "acc": 0, "dsc": "()<PERSON><PERSON><PERSON>/util/Set;", "sig": "()Lja<PERSON>/util/Set<Ljava/lang/String;>;"}, {"nme": "target", "acc": 0, "dsc": "()Ljdk/internal/module/ModuleTarget;"}, {"nme": "recordedHashes", "acc": 0, "dsc": "()Ljdk/internal/module/ModuleHashes;"}, {"nme": "moduleResolution", "acc": 0, "dsc": "()Ljdk/internal/module/ModuleResolution;"}, {"nme": "validateNames", "acc": 0, "dsc": "()V"}, {"nme": "validatePackages", "acc": 0, "dsc": "()V"}, {"nme": "hasModulePackages", "acc": 0, "dsc": "()Z", "exs": ["java/io/IOException"]}, {"nme": "shouldRewrite", "acc": 0, "dsc": "()Z"}, {"nme": "getBytes", "acc": 0, "dsc": "()[B", "exs": ["java/io/IOException"]}, {"nme": "getInputStream", "acc": 0, "dsc": "()Ljava/io/InputStream;"}, {"nme": "lambda$hasModulePackages$2", "acc": 4106, "dsc": "(Ljdk/internal/classfile/ClassElement;)Z"}, {"nme": "lambda$validatePackages$1", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}, {"nme": "lambda$validatePackages$0", "acc": 4098, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Z"}], "flds": [{"acc": 18, "nme": "bais", "dsc": "Ljava/io/ByteArrayInputStream;"}, {"acc": 18, "nme": "attrs", "dsc": "Ljdk/internal/module/ModuleInfo$Attributes;"}, {"acc": 18, "nme": "packages", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 18, "nme": "addModulePackages", "dsc": "Z"}, {"acc": 2, "nme": "descriptor", "dsc": "Ljava/lang/module/ModuleDescriptor;"}]}, "classes/jdk/tools/jlink/internal/ModuleSorter.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ModuleSorter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModuleView;)V"}, {"nme": "readModuleDescriptor", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljava/lang/module/ModuleDescriptor;"}, {"nme": "addModule", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljdk/tools/jlink/internal/ModuleSorter;"}, {"nme": "addNode", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "build", "acc": 34, "dsc": "()V"}, {"nme": "sorted", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "visit", "acc": 2, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;Ljava/util/Set;Ljava/util/Set;)V", "sig": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;Ljava/util/Set<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;Ljava/util/Set<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;)V"}, {"nme": "lambda$visit$4", "acc": 4098, "dsc": "(Ljava/util/Set;Ljava/util/Set;Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "lambda$build$3", "acc": 4098, "dsc": "(Ljava/util/Set;Ljava/util/Set;Ljdk/tools/jlink/plugin/ResourcePoolModule;)V"}, {"nme": "lambda$addNode$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljava/util/Set;"}, {"nme": "lambda$addModule$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;Ljava/lang/module/ModuleDescriptor$Requires;)V"}, {"nme": "lambda$readModuleDescriptor$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePoolModule;)Ljdk/tools/jlink/plugin/PluginException;"}], "flds": [{"acc": 18, "nme": "graph", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljdk/tools/jlink/plugin/ResourcePoolModule;Ljava/util/Set<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;>;"}, {"acc": 18, "nme": "result", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"acc": 18, "nme": "moduleView", "dsc": "Ljdk/tools/jlink/plugin/ResourcePoolModuleView;"}]}, "classes/jdk/tools/jmod/JmodTask$AbstractPathConverter.class": {"ver": 65, "acc": 1056, "nme": "jdk/tools/jmod/JmodTask$AbstractPathConverter", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "convert", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/List;", "sig": "(Lja<PERSON>/lang/String;)Ljava/util/List<Ljava/nio/file/Path;>;"}, {"nme": "valueType", "acc": 1, "dsc": "()<PERSON><PERSON>va/lang/Class;", "sig": "()Ljava/lang/Class<Ljava/util/List<Ljava/nio/file/Path;>;>;"}, {"nme": "valuePattern", "acc": 1, "dsc": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "to<PERSON><PERSON>", "acc": 1024, "dsc": "(Lja<PERSON>/lang/String;)Ljava/nio/file/Path;"}, {"nme": "convert", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/JlinkTask$OptionsValues.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/JlinkTask$OptionsValues", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}], "flds": [{"acc": 0, "nme": "help", "dsc": "Z"}, {"acc": 0, "nme": "saveoptsfile", "dsc": "<PERSON><PERSON><PERSON>/lang/String;"}, {"acc": 0, "nme": "verbose", "dsc": "Z"}, {"acc": 0, "nme": "version", "dsc": "Z"}, {"acc": 0, "nme": "fullVersion", "dsc": "Z"}, {"acc": 16, "nme": "modulePath", "dsc": "<PERSON><PERSON><PERSON>/util/List;", "sig": "Ljava/util/List<Ljava/nio/file/Path;>;"}, {"acc": 16, "nme": "limitMods", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 16, "nme": "addMods", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 0, "nme": "output", "dsc": "Ljava/nio/file/Path;"}, {"acc": 16, "nme": "launchers", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"}, {"acc": 0, "nme": "packagedModulesPath", "dsc": "Ljava/nio/file/Path;"}, {"acc": 0, "nme": "endian", "dsc": "<PERSON><PERSON><PERSON>/nio/ByteOrder;"}, {"acc": 0, "nme": "ignoreSigning", "dsc": "Z"}, {"acc": 0, "nme": "bindServices", "dsc": "Z"}, {"acc": 0, "nme": "suggestProviders", "dsc": "Z"}]}, "classes/jdk/tools/jlink/plugin/Plugin$State.class": {"ver": 65, "acc": 16433, "nme": "jdk/tools/jlink/plugin/Plugin$State", "super": "java/lang/Enum", "mthds": [{"nme": "values", "acc": 9, "dsc": "()[Ljdk/tools/jlink/plugin/Plugin$State;"}, {"nme": "valueOf", "acc": 9, "dsc": "(Ljava/lang/String;)Ljdk/tools/jlink/plugin/Plugin$State;"}, {"nme": "<init>", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;I)V", "sig": "()V"}, {"nme": "$values", "acc": 4106, "dsc": "()[Ljdk/tools/jlink/plugin/Plugin$State;"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 16409, "nme": "DISABLED", "dsc": "Ljdk/tools/jlink/plugin/Plugin$State;"}, {"acc": 16409, "nme": "AUTO_ENABLED", "dsc": "Ljdk/tools/jlink/plugin/Plugin$State;"}, {"acc": 16409, "nme": "FUNCTIONAL", "dsc": "Ljdk/tools/jlink/plugin/Plugin$State;"}, {"acc": 4122, "nme": "$VALUES", "dsc": "[Ljdk/tools/jlink/plugin/Plugin$State;"}]}, "classes/jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$JvmComparator.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/plugins/ExcludeVMPlugin$JvmComparator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "compare", "acc": 1, "dsc": "(Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;Ljdk/tools/jlink/internal/plugins/ExcludeVMPlugin$Jvm;)I"}, {"nme": "compare", "acc": 4161, "dsc": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$ModuleDescriptorBuilder.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator$ModuleDescriptorBuilder", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator;Ljdk/internal/classfile/CodeBuilder;Ljava/lang/module/ModuleDescriptor;Ljava/util/Set;I)V", "sig": "(Ljdk/internal/classfile/CodeBuilder;Ljava/lang/module/ModuleDescriptor;Ljava/util/Set<Ljava/lang/String;>;I)V"}, {"nme": "build", "acc": 0, "dsc": "()V"}, {"nme": "newBuilder", "acc": 0, "dsc": "()V"}, {"nme": "setModuleBit", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)V"}, {"nme": "putModuleDescriptor", "acc": 0, "dsc": "()V"}, {"nme": "requires", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Requires;>;)V"}, {"nme": "newRequires", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Requires$Modifier;>;Ljava/lang/String;Ljava/lang/String;)V"}, {"nme": "exports", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Exports;>;)V"}, {"nme": "newExports", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Exports$Modifier;>;Ljava/lang/String;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "opens", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Opens;>;)V"}, {"nme": "newOpens", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljava/lang/module/ModuleDescriptor$Opens$Modifier;>;Ljava/lang/String;Ljava/util/Set<Ljava/lang/String;>;)V"}, {"nme": "uses", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)V"}, {"nme": "provides", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Collection;)V", "sig": "(Ljava/util/Collection<Ljava/lang/module/ModuleDescriptor$Provides;>;)V"}, {"nme": "new<PERSON><PERSON><PERSON>", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/util/List;)V", "sig": "(Lja<PERSON>/lang/String;Ljava/util/List<Ljava/lang/String;>;)V"}, {"nme": "packages", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)V"}, {"nme": "mainClass", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "version", "acc": 0, "dsc": "(Ljava/lang/module/ModuleDescriptor$Version;)V"}, {"nme": "invokeBuilderMethod", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"nme": "<clinit>", "acc": 8, "dsc": "()V"}], "flds": [{"acc": 24, "nme": "CD_EXPORTS", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 24, "nme": "CD_OPENS", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 24, "nme": "CD_PROVIDES", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 24, "nme": "CD_REQUIRES", "dsc": "Ljava/lang/constant/ClassDesc;"}, {"acc": 24, "nme": "MTD_EXPORTS_MODIFIER_SET_STRING_SET", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_EXPORTS_MODIFIER_SET_STRING", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_OPENS_MODIFIER_SET_STRING_SET", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_OPENS_MODIFIER_SET_STRING", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_PROVIDES_STRING_LIST", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_REQUIRES_SET_STRING", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_REQUIRES_SET_STRING_STRING", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_EXPORTS_ARRAY", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_OPENS_ARRAY", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_PROVIDES_ARRAY", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_REQUIRES_ARRAY", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_SET", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_STRING", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 24, "nme": "MTD_BOOLEAN", "dsc": "Ljava/lang/constant/MethodTypeDesc;"}, {"acc": 16, "nme": "cob", "dsc": "Ljdk/internal/classfile/CodeBuilder;"}, {"acc": 16, "nme": "md", "dsc": "Ljava/lang/module/ModuleDescriptor;"}, {"acc": 16, "nme": "packages", "dsc": "<PERSON><PERSON><PERSON>/util/Set;", "sig": "Ljava/util/Set<Ljava/lang/String;>;"}, {"acc": 16, "nme": "index", "dsc": "I"}, {"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/plugins/SystemModulesPlugin$SystemModulesClassGenerator;"}]}, "classes/jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolModuleViewImpl.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ResourcePoolManager$ResourcePoolModuleViewImpl", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;)V"}, {"nme": "findModule", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/util/Optional;", "sig": "(Ljava/lang/String;)Ljava/util/Optional<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "modules", "acc": 1, "dsc": "()Ljava/util/stream/Stream;", "sig": "()Ljava/util/stream/Stream<Ljdk/tools/jlink/plugin/ResourcePoolModule;>;"}, {"nme": "moduleCount", "acc": 1, "dsc": "()I"}], "flds": [{"acc": 4112, "nme": "this$0", "dsc": "Ljdk/tools/jlink/internal/ResourcePoolManager;"}]}, "classes/jdk/tools/jlink/internal/ImagePluginStack$PreVisitStrings.class": {"ver": 65, "acc": 48, "nme": "jdk/tools/jlink/internal/ImagePluginStack$PreVisitStrings", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "()V"}, {"nme": "addString", "acc": 1, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)I"}, {"nme": "getSortedStrings", "acc": 2, "dsc": "()<PERSON><PERSON><PERSON>/util/List;", "sig": "()Ljava/util/List<Ljava/lang/String;>;"}, {"nme": "getString", "acc": 1, "dsc": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"nme": "lambda$getSortedStrings$1", "acc": 4106, "dsc": "(Ljava/util/Map$Entry;)Z"}, {"nme": "lambda$getSortedStrings$0", "acc": 4106, "dsc": "(<PERSON><PERSON><PERSON>/util/Map$Entry;)Ljava/lang/Integer;"}], "flds": [{"acc": 2, "nme": "currentid", "dsc": "I"}, {"acc": 18, "nme": "stringsUsage", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"acc": 18, "nme": "stringsMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/lang/Integer;>;"}, {"acc": 18, "nme": "reverseMap", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/Integer;Ljava/lang/String;>;"}]}, "classes/jdk/tools/jlink/internal/ImageFileCreator.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jlink/internal/ImageFileCreator", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 2, "dsc": "(Ljdk/tools/jlink/internal/ImagePluginStack;)V"}, {"nme": "create", "acc": 9, "dsc": "(Ljava/util/Set;Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}, {"nme": "create", "acc": 9, "dsc": "(Lja<PERSON>/util/Set;Ljava/nio/ByteOrder;)Ljdk/tools/jlink/internal/ExecutableImage;", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljava/nio/ByteOrder;)Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}, {"nme": "create", "acc": 9, "dsc": "(Ljava/util/Set;Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/ImagePluginStack;)Ljdk/tools/jlink/internal/ExecutableImage;", "exs": ["java/io/IOException"]}, {"nme": "readAllEntries", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;)V"}, {"nme": "recreateJimage", "acc": 9, "dsc": "(Ljava/nio/file/Path;Ljava/util/Set;Ljdk/tools/jlink/internal/ImagePluginStack;)V", "sig": "(Ljava/nio/file/Path;Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljdk/tools/jlink/internal/ImagePluginStack;)V", "exs": ["java/io/IOException"]}, {"nme": "writeImage", "acc": 2, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;<PERSON><PERSON><PERSON>/nio/ByteOrder;)V", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljava/nio/ByteOrder;)V", "exs": ["java/io/IOException"]}, {"nme": "generateJImage", "acc": 10, "dsc": "(Ljdk/tools/jlink/internal/ResourcePoolManager;Ljdk/tools/jlink/internal/BasicImageWriter;Ljdk/tools/jlink/internal/ImagePluginStack;Ljava/io/DataOutputStream;)Ljdk/tools/jlink/plugin/ResourcePool;", "exs": ["java/io/IOException"]}, {"nme": "createPoolManager", "acc": 10, "dsc": "(Ljava/util/Set;Ljava/util/Map;Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/BasicImageWriter;)Ljdk/tools/jlink/internal/ResourcePoolManager;", "sig": "(Ljava/util/Set<Ljdk/tools/jlink/internal/Archive;>;Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljdk/tools/jlink/internal/Archive$Entry;>;>;Ljava/nio/ByteOrder;Ljdk/tools/jlink/internal/BasicImageWriter;)Ljdk/tools/jlink/internal/ResourcePoolManager;", "exs": ["java/io/IOException"]}, {"nme": "splitPath", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)[Lja<PERSON>/lang/String;"}, {"nme": "resourceName", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "toPackage", "acc": 9, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"nme": "toPackage", "acc": 10, "dsc": "(<PERSON><PERSON><PERSON>/lang/String;Z)Ljava/lang/String;"}, {"nme": "lambda$createPoolManager$6", "acc": 4106, "dsc": "(Ljava/util/Map;Ljava/lang/String;)Ljava/util/stream/Stream;"}, {"nme": "lambda$createPoolManager$5", "acc": 4106, "dsc": "(Ljava/lang/String;Ljdk/tools/jlink/internal/Archive$Entry;)Ljdk/tools/jlink/internal/ArchiveEntryResourcePoolEntry;"}, {"nme": "lambda$generateJImage$4", "acc": 4106, "dsc": "(Ljava/io/DataOutputStream;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$generateJImage$3", "acc": 4106, "dsc": "(Ljava/util/List;Ljava/util/Set;[JLjdk/tools/jlink/internal/BasicImageWriter;Ljava/util/List;Ljdk/tools/jlink/plugin/ResourcePoolEntry;)V"}, {"nme": "lambda$recreateJimage$2", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/Archive;)Ljava/util/List;"}, {"nme": "lambda$readAllEntries$1", "acc": 4098, "dsc": "(Ljdk/tools/jlink/internal/Archive;)V"}, {"nme": "lambda$readAllEntries$0", "acc": 4106, "dsc": "(Ljdk/tools/jlink/internal/Archive$Entry;)Z"}], "flds": [{"acc": 18, "nme": "entriesForModule", "dsc": "Ljava/util/Map;", "sig": "Ljava/util/Map<Ljava/lang/String;Ljava/util/List<Ljdk/tools/jlink/internal/Archive$Entry;>;>;"}, {"acc": 18, "nme": "plugins", "dsc": "Ljdk/tools/jlink/internal/ImagePluginStack;"}]}, "classes/jdk/tools/jmod/resources/jmod_de.class": {"ver": 65, "acc": 49, "nme": "jdk/tools/jmod/resources/jmod_de", "super": "java/util/ListResourceBundle", "mthds": [{"nme": "<init>", "acc": 1, "dsc": "()V"}, {"nme": "getContents", "acc": 20, "dsc": "()[[<PERSON><PERSON><PERSON>/lang/Object;"}], "flds": []}, "classes/jdk/tools/jlink/internal/ImagePluginConfiguration$1.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/ImagePluginConfiguration$1", "super": "java/lang/Object", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "()V"}, {"nme": "getJImageOutputStream", "acc": 1, "dsc": "()Ljava/io/DataOutputStream;"}, {"nme": "getExecutableImage", "acc": 1, "dsc": "()Ljdk/tools/jlink/internal/ExecutableImage;"}, {"nme": "storeFiles", "acc": 1, "dsc": "(Ljdk/tools/jlink/plugin/ResourcePool;)V"}], "flds": []}, "classes/jdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo$ModuleInfoRewriter.class": {"ver": 65, "acc": 32, "nme": "jdk/tools/jlink/internal/plugins/SystemModulesPlugin$ModuleInfo$ModuleInfoRewriter", "super": "java/io/ByteArrayOutputStream", "mthds": [{"nme": "<init>", "acc": 0, "dsc": "(<PERSON><PERSON>va/io/InputStream;)V"}, {"nme": "addModulePackages", "acc": 0, "dsc": "(<PERSON><PERSON><PERSON>/util/Set;)V", "sig": "(<PERSON><PERSON><PERSON>/util/Set<Ljava/lang/String;>;)V"}, {"nme": "getBytes", "acc": 0, "dsc": "()[B", "exs": ["java/io/IOException"]}], "flds": [{"acc": 16, "nme": "extender", "dsc": "Ljdk/internal/module/ModuleInfoExtender;"}]}}}}