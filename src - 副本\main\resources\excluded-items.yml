# 掉落物排除配置文件
# 此文件保存通过GUI菜单添加的排除物品
# 支持原版物品和MOD物品
#
# 管理员功能:
# - 使用 /pokemoncleaner items 打开管理GUI
# - 拖拽背包物品到GUI空槽位添加
# - Shift+左键背包物品快速添加
# - 右键点击GUI中的物品移除
# - 重复添加会替换物品信息
# - 移除操作会实时同步本地文件

# 通过GUI菜单添加的排除物品列表
# 格式: 物品ID或物品名称
# 支持详细信息匹配，包括NBT、附魔、自定义名称等
gui-excluded-items:
  # 原版贵重物品 (minecraft命名空间)
  - "minecraft:diamond"           # 钻石
  - "minecraft:emerald"           # 绿宝石
  - "minecraft:netherite_ingot"   # 下界合金锭
  - "minecraft:elytra"            # 鞘翅
  - "minecraft:totem_of_undying"  # 不死图腾
  - "minecraft:enchanted_book"    # 附魔书
  - "minecraft:nether_star"       # 下界之星
  - "minecraft:dragon_egg"        # 龙蛋
  - "minecraft:beacon"            # 信标
  - "minecraft:shulker_box"       # 潜影盒

  # Cobblemon物品 (cobblemon命名空间)
  - "cobblemon:poke_ball"         # 精灵球
  - "cobblemon:great_ball"        # 超级球
  - "cobblemon:ultra_ball"        # 高级球
  - "cobblemon:master_ball"       # 大师球
  - "cobblemon:rare_candy"        # 神奇糖果
  - "cobblemon:exp_candy_xs"      # 经验糖果XS
  - "cobblemon:exp_candy_s"       # 经验糖果S
  - "cobblemon:exp_candy_m"       # 经验糖果M
  - "cobblemon:exp_candy_l"       # 经验糖果L
  - "cobblemon:exp_candy_xl"      # 经验糖果XL

  # 其他MOD物品示例
  # - "modid:item_name"           # MOD物品格式
  # - "modid:special_item;name:自定义名称"  # 带自定义名称的物品
  # - "modid:enchanted_item;enchants:sharpness:5"  # 带附魔的物品

# 物品显示名称映射（用于GUI显示）
item-display-names:
  "minecraft:diamond": "钻石"
  "minecraft:emerald": "绿宝石"
  "minecraft:netherite_ingot": "下界合金锭"
  "minecraft:elytra": "鞘翅"
  "minecraft:totem_of_undying": "不死图腾"
  "minecraft:enchanted_book": "附魔书"
  "minecraft:nether_star": "下界之星"
  "minecraft:dragon_egg": "龙蛋"
  "minecraft:beacon": "信标"
  "minecraft:shulker_box": "潜影盒"
  "cobblemon:poke_ball": "精灵球"
  "cobblemon:great_ball": "超级球"
  "cobblemon:ultra_ball": "高级球"
  "cobblemon:master_ball": "大师球"
  "cobblemon:rare_candy": "神奇糖果"

# GUI菜单配置
gui-settings:
  # 菜单标题（支持颜色代码）
  title: "§6§l掉落物清理管理"

  # 菜单大小（行数，1-6行）
  # 1行=9个槽位, 6行=54个槽位
  size: 6

  # 是否启用GUI管理功能
  # false: 禁用GUI，只能通过配置文件管理
  # true: 启用GUI，管理员可以通过 /pokemoncleaner items 打开管理界面
  enabled: true

  # 每页显示的物品数量
  # 建议设置: 1行菜单=4个物品, 6行菜单=45个物品（预留控制按钮空间）
  items-per-page: 45
  
  # 按钮配置
  # 注意: slot编号从0开始，6行菜单的槽位范围是0-53
  # 最后一行槽位: 45-53 (用于放置控制按钮)
  buttons:
    # 添加物品按钮
    add-item:
      slot: 49
      material: "GREEN_STAINED_GLASS_PANE"
      name: "§a§l添加物品"
      lore:
        - "§7点击添加手中的物品到排除列表"
        - "§7被添加的物品将不会被清理"
        - "§e左键: 添加物品"
    
    # 移除物品按钮
    remove-item:
      slot: 50
      material: "RED_STAINED_GLASS_PANE"
      name: "§c§l移除物品"
      lore:
        - "§7点击移除手中的物品从排除列表"
        - "§7移除后该物品将可以被清理"
        - "§e左键: 移除物品"
    
    # 保存配置按钮
    save-config:
      slot: 51
      material: "BLUE_STAINED_GLASS_PANE"
      name: "§b§l保存配置"
      lore:
        - "§7保存当前配置到文件"
        - "§7确保配置不会丢失"
        - "§e左键: 保存配置"
    
    # 重载配置按钮
    reload-config:
      slot: 52
      material: "YELLOW_STAINED_GLASS_PANE"
      name: "§e§l重载配置"
      lore:
        - "§7重新加载配置文件"
        - "§7应用最新的配置更改"
        - "§e左键: 重载配置"
    
    # 上一页按钮
    previous-page:
      slot: 45
      material: "ARROW"
      name: "§f§l上一页"
      lore:
        - "§7查看上一页的物品"
        - "§e左键: 上一页"
    
    # 下一页按钮
    next-page:
      slot: 53
      material: "ARROW"
      name: "§f§l下一页"
      lore:
        - "§7查看下一页的物品"
        - "§e左键: 下一页"
    
    # 关闭菜单按钮
    close-menu:
      slot: 48
      material: "BARRIER"
      name: "§c§l关闭菜单"
      lore:
        - "§7关闭当前菜单"
        - "§e左键: 关闭"

# 消息配置
# 支持颜色代码 (§a=绿色, §c=红色, §e=黄色, §6=金色, §7=灰色等)
# 支持变量替换: {item} = 物品名称
messages:
  item-added: "§a成功添加物品 §e{item} §a到排除列表！"
  item-removed: "§c成功从排除列表移除物品 §e{item}§c！"
  item-already-excluded: "§e物品 §6{item} §e已经在排除列表中！"
  item-not-excluded: "§e物品 §6{item} §e不在排除列表中！"
  no-item-in-hand: "§c请手持要操作的物品！"
  config-saved: "§a配置已保存到文件！"
  config-reloaded: "§a配置已重新加载！"
  permission-denied: "§c你没有权限使用此功能！"
  gui-disabled: "§c掉落物管理GUI已被禁用！"
