/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  kotlin.Metadata
 *  kotlin.collections.CollectionsKt
 *  kotlin.jvm.internal.DefaultConstructorMarker
 *  kotlin.jvm.internal.Intrinsics
 *  kotlin.jvm.internal.SourceDebugExtension
 *  kotlin.text.StringsKt
 *  net.minecraft.class_1297
 *  net.minecraft.class_1299
 *  net.minecraft.class_2960
 *  net.minecraft.class_7923
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.pokeskies.skiesclear.config.clearables;

import com.pokeskies.skiesclear.utils.Utils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import net.minecraft.class_1297;
import net.minecraft.class_1299;
import net.minecraft.class_2960;
import net.minecraft.class_7923;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(mv={2, 0, 0}, k=1, xi=48, d1={"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u00002\u00020\u0001B1\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\b\b\u0010\tJ\u0015\u0010\f\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\b\f\u0010\rJ\u0015\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\b\u000e\u0010\rJ'\u0010\u0012\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00110\u00102\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\f\n\u0004\b\u0003\u0010\u0016\u001a\u0004\b\u0017\u0010\u0018R(\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0006\u0010\u0019\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR(\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\b\u0007\u0010\u0019\u001a\u0004\b\u001e\u0010\u001b\"\u0004\b\u001f\u0010\u001dR\"\u0010 \u001a\u000e\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u0011\u0018\u00010\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b \u0010\u0019R\"\u0010!\u001a\u000e\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u0011\u0018\u00010\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\b!\u0010\u0019\u00a8\u0006\""}, d2={"Lcom/pokeskies/skiesclear/config/clearables/EntityClearable;", "", "", "enabled", "", "", "blacklist", "whitelist", "<init>", "(ZLjava/util/List;Ljava/util/List;)V", "Lnet/minecraft/class_1297;", "entity", "isEntityType", "(Lnet/minecraft/class_1297;)Z", "shouldClear", "list", "", "Lnet/minecraft/class_1299;", "createEntitiesList", "(Ljava/util/List;)Ljava/util/List;", "toString", "()Ljava/lang/String;", "Z", "getEnabled", "()Z", "Ljava/util/List;", "getBlacklist", "()Ljava/util/List;", "setBlacklist", "(Ljava/util/List;)V", "getWhitelist", "setWhitelist", "blacklistedEntities", "whitelistedEntities", "SkiesClear"})
@SourceDebugExtension(value={"SMAP\nEntityClearable.kt\nKotlin\n*S Kotlin\n*F\n+ 1 EntityClearable.kt\ncom/pokeskies/skiesclear/config/clearables/EntityClearable\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,59:1\n1755#2,3:60\n2632#2,3:63\n*S KotlinDebug\n*F\n+ 1 EntityClearable.kt\ncom/pokeskies/skiesclear/config/clearables/EntityClearable\n*L\n27#1:60,3\n31#1:63,3\n*E\n"})
public final class EntityClearable {
    private final boolean enabled;
    @NotNull
    private List<String> blacklist;
    @NotNull
    private List<String> whitelist;
    @Nullable
    private transient List<class_1299<?>> blacklistedEntities;
    @Nullable
    private transient List<class_1299<?>> whitelistedEntities;

    public EntityClearable(boolean enabled, @NotNull List<String> blacklist, @NotNull List<String> whitelist) {
        Intrinsics.checkNotNullParameter(blacklist, (String)"blacklist");
        Intrinsics.checkNotNullParameter(whitelist, (String)"whitelist");
        this.enabled = enabled;
        this.blacklist = blacklist;
        this.whitelist = whitelist;
    }

    public /* synthetic */ EntityClearable(boolean bl, List list, List list2, int n, DefaultConstructorMarker defaultConstructorMarker) {
        if ((n & 1) != 0) {
            bl = true;
        }
        if ((n & 2) != 0) {
            list = CollectionsKt.emptyList();
        }
        if ((n & 4) != 0) {
            list2 = CollectionsKt.emptyList();
        }
        this(bl, list, list2);
    }

    public final boolean getEnabled() {
        return this.enabled;
    }

    @NotNull
    public final List<String> getBlacklist() {
        return this.blacklist;
    }

    public final void setBlacklist(@NotNull List<String> list) {
        Intrinsics.checkNotNullParameter(list, (String)"<set-?>");
        this.blacklist = list;
    }

    @NotNull
    public final List<String> getWhitelist() {
        return this.whitelist;
    }

    public final void setWhitelist(@NotNull List<String> list) {
        Intrinsics.checkNotNullParameter(list, (String)"<set-?>");
        this.whitelist = list;
    }

    public final boolean isEntityType(@NotNull class_1297 entity) {
        Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
        return true;
    }

    public final boolean shouldClear(@NotNull class_1297 entity) {
        boolean bl;
        block11: {
            Intrinsics.checkNotNullParameter((Object)entity, (String)"entity");
            if (this.blacklistedEntities == null) {
                this.blacklistedEntities = this.createEntitiesList(this.blacklist);
            }
            List<class_1299<?>> list = this.blacklistedEntities;
            Intrinsics.checkNotNull(list);
            Iterable $this$any$iv = list;
            boolean $i$f$any = false;
            if ($this$any$iv instanceof Collection && ((Collection)$this$any$iv).isEmpty()) {
                bl = false;
            } else {
                for (Object element$iv : $this$any$iv) {
                    class_1299 it = (class_1299)element$iv;
                    boolean bl2 = false;
                    if (!Intrinsics.areEqual((Object)it, (Object)entity.method_5864())) continue;
                    bl = true;
                    break block11;
                }
                bl = false;
            }
        }
        if (bl) {
            return false;
        }
        if (this.whitelistedEntities == null) {
            this.whitelistedEntities = this.createEntitiesList(this.whitelist);
        }
        List<class_1299<?>> list = this.whitelistedEntities;
        Intrinsics.checkNotNull(list);
        List<class_1299<?>> list2 = list;
        boolean bl3 = false;
        if (!((Collection)list2).isEmpty()) {
            boolean bl4;
            block12: {
                Iterable $this$none$iv = list2;
                boolean $i$f$none = false;
                if ($this$none$iv instanceof Collection && ((Collection)$this$none$iv).isEmpty()) {
                    bl4 = true;
                } else {
                    for (Object element$iv : $this$none$iv) {
                        class_1299 it = (class_1299)element$iv;
                        boolean bl5 = false;
                        if (!Intrinsics.areEqual((Object)it, (Object)entity.method_5864())) continue;
                        bl4 = false;
                        break block12;
                    }
                    bl4 = true;
                }
            }
            if (bl4) {
                return false;
            }
        }
        return true;
    }

    private final List<class_1299<?>> createEntitiesList(List<String> list) {
        List newEntities = new ArrayList();
        for (String entry : list) {
            if (Utils.INSTANCE.getWildcardPattern().matcher(entry).matches()) {
                String[] stringArray = new String[]{":"};
                String namespace = (String)StringsKt.split$default((CharSequence)entry, (String[])stringArray, (boolean)false, (int)0, (int)6, null).get(0);
                for (class_1299 type : class_7923.field_41177) {
                    if (!Intrinsics.areEqual((Object)class_7923.field_41177.method_10221((Object)type).method_12836(), (Object)namespace)) continue;
                    Intrinsics.checkNotNull((Object)type);
                    newEntities.add(type);
                }
                continue;
            }
            Object object = class_7923.field_41177.method_10223(class_2960.method_60654((String)entry));
            Intrinsics.checkNotNullExpressionValue((Object)object, (String)"get(...)");
            newEntities.add(object);
        }
        return newEntities;
    }

    @NotNull
    public String toString() {
        return "EntityClearable(enabled=" + this.enabled + ", blacklist=" + this.blacklist + ", whitelist=" + this.whitelist + ", blacklistedEntities=" + this.blacklistedEntities + ", whitelistedEntities=" + this.whitelistedEntities + ")";
    }

    public EntityClearable() {
        this(false, null, null, 7, null);
    }
}

